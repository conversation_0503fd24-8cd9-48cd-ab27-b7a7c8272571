const { heroui } = require("@heroui/react");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/framesound-ui/**/*.{js,ts,jsx,tsx}",
    // HeroUI components
    "./node_modules/@heroui/theme/dist/components/toast.js",
    "./node_modules/@heroui/theme/dist/components/button.js",
    "./node_modules/@heroui/theme/dist/components/popover.js",
    "./node_modules/@heroui/theme/dist/components/tooltip.js",
    // or you can use a glob pattern (multiple component styles)
    "./node_modules/@heroui/theme/dist/components/(toast|button|snippet|code|input|popover|tooltip).js",
  ],
  theme: {
    screens: {
      xxs: "375px", // 22.5rem
      xs: "430px", // 33.75rem
      sm: "560px", // 40rem
      md: "768px", // 48rem
      md2: "900px", // 56.25rem
      lg: "1024px", // 64rem
      xl: "1280px", // 80rem
      "2xl": "1920px", // 114rem
    },
    extend: {
      transitionProperty: {
        width: "width",
      },
      borderRadius: {
        xs: "4px",
        sm: "6px",
        md: "8px",
        lg: "10px",
        xl: "12px",
      },
      animation: {
        "spin-slow": "spin 3s linear infinite",
        ripple: "ripple 0.6s linear",
        "toast-in": "toast-in 0.3s ease-out forwards",
        "toast-out": "toast-out 0.2s ease-in forwards",
      },
      keyframes: {
        "toast-in": {
          "0%": {
            opacity: "0",
            transform: "translateY(-8px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "toast-out": {
          "0%": {
            opacity: "1",
            transform: "translateY(0)",
          },
          "100%": {
            opacity: "0",
            transform: "translateY(-8px)",
          },
        },
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      // 设置默认主题为亮色
      defaultTheme: "light",
      defaultExtendTheme: "light",

      // 配置 HeroUI 的主题和颜色
      themes: {
        light: {
          colors: {
            background: "#FFFEFD",
            backgroundDeep: "#F1EEEC",
            foreground: "#1D1814",
            divider: "#F3F3F3",
            focus: "#4F46E5",
            content1: "#FFFFFF",
            content2: "#F8F8F8",
            content3: "#F0F0F0",
            content4: "#E8E8E8",
            primaryBtn: {
              DEFAULT: "#443AA7",
              hover: "#3F3A37",
              text: "#FFFFFF",
              inactive: "#8D8B88",
            },
            secondaryBtn: {
              DEFAULT: "#1D1814",
              hover: "#3F3A37",
              text: "#F0E8DD",
              inactive: "#8D8B88",
            },
            floatingBar: {
              DEFAULT: "#FFFFFF",
              border: "#F0F0F0",
            },
            card: {
              DEFAULT: "#EBE9E7", // 卡片背景色
              hover: "#DFDDDA", // 卡片悬停背景色
            },
            cardButton: {
              DEFAULT: "#D3D0CC",
            },
            primary: {
              50: "#EEF2FF",
              100: "#E0E7FF",
              200: "#C7D2FE",
              300: "#A5B4FC",
              400: "#818CF8",
              500: "#6366F1",
              600: "#110F60",
              700: "#4338CA",
              800: "#3730A3",
              900: "#312E81",
              DEFAULT: "#110F60",
              foreground: "#FFFFFF",
            },
            secondary: {
              50: "#F5F3FF",
              100: "#EDE9FE",
              200: "#DDD6FE",
              300: "#C4B5FD",
              400: "#A78BFA",
              500: "#8B5CF6",
              600: "#7C3AED",
              700: "#6D28D9",
              800: "#5B21B6",
              900: "#4C1D95",
              DEFAULT: "#110F60",
              foreground: "#FFFFFF",
            },
            success: {
              50: "#ECFDF5",
              100: "#D1FAE5",
              200: "#A7F3D0",
              300: "#6EE7B7",
              400: "#34D399",
              500: "#10B981",
              600: "#059669",
              700: "#047857",
              800: "#065F46",
              900: "#064E3B",
              DEFAULT: "#10B981",
              foreground: "#FFFFFF",
            },
            warning: {
              50: "#FFFBEB",
              100: "#FEF3C7",
              200: "#FDE68A",
              300: "#FCD34D",
              400: "#FBBF24",
              500: "#F59E0B",
              600: "#D97706",
              700: "#B45309",
              800: "#92400E",
              900: "#78350F",
              DEFAULT: "#F59E0B",
              foreground: "#FFFFFF",
            },
            danger: {
              50: "#FEF2F2",
              100: "#FEE2E2",
              200: "#FECACA",
              300: "#FCA5A5",
              400: "#F87171",
              500: "#EF4444",
              600: "#DC2626",
              700: "#B91C1C",
              800: "#991B1B",
              900: "#7F1D1D",
              DEFAULT: "#EF4444",
              foreground: "#FFFFFF",
            },
            default: {
              50: "#F9FAFB",
              100: "#F3F4F6",
              200: "#E5E7EB",
              300: "#D1D5DB",
              400: "#9CA3AF",
              500: "#6B7280",
              600: "#4B5563",
              700: "#374151",
              800: "#1F2937",
              900: "#111827",
              DEFAULT: "#6B7280",
              foreground: "#FFFFFF",
            },
          },
        },
        dark: {
          colors: {
            background: "#201F1E",
            backgroundDeep: "#171614",
            foreground: "#FFFFFF",
            divider: "#262626",
            focus: "#6366F1",
            content1: "#161616",
            content2: "#1C1C1C",
            content3: "#232323",
            content4: "#2D2D2D",
            primaryBtn: {
              DEFAULT: "#F0E8DD",
              hover: "#C8C1B8",
              text: "#1D1814",
              inactive: "#858483",
            },
            secondaryBtn: {
              DEFAULT: "#F0E8DD",
              hover: "#C8C1B8",
              text: "#1D1814",
              inactive: "#858483",
            },
            floatingBar: {
              DEFAULT: "#272726",
              border: "#383837",
            },
            card: {
              DEFAULT: "#1C1F26", // 卡片背景色
              hover: "#252A34", // 卡片悬停背景色
            },
            cardButton: {
              DEFAULT: "#3D424C",
            },
            primary: {
              50: "#EEF2FF",
              100: "#E0E7FF",
              200: "#C7D2FE",
              300: "#A5B4FC",
              400: "#818CF8",
              500: "#6366F1",
              600: "#4F46E5",
              700: "#4338CA",
              800: "#3730A3",
              900: "#312E81",
              DEFAULT: "#443AA7",
              foreground: "#FFFFFF",
            },
            secondary: {
              50: "#F5F3FF",
              100: "#EDE9FE",
              200: "#DDD6FE",
              300: "#C4B5FD",
              400: "#A78BFA",
              500: "#8B5CF6",
              600: "#7C3AED",
              700: "#6D28D9",
              800: "#5B21B6",
              900: "#4C1D95",
              DEFAULT: "#8B5CF6",
              foreground: "#FFFFFF",
            },
            success: {
              50: "#ECFDF5",
              100: "#D1FAE5",
              200: "#A7F3D0",
              300: "#6EE7B7",
              400: "#34D399",
              500: "#10B981",
              600: "#059669",
              700: "#047857",
              800: "#065F46",
              900: "#064E3B",
              DEFAULT: "#10B981",
              foreground: "#FFFFFF",
            },
            warning: {
              50: "#FFFBEB",
              100: "#FEF3C7",
              200: "#FDE68A",
              300: "#FCD34D",
              400: "#FBBF24",
              500: "#F59E0B",
              600: "#D97706",
              700: "#B45309",
              800: "#92400E",
              900: "#78350F",
              DEFAULT: "#F59E0B",
              foreground: "#FFFFFF",
            },
            danger: {
              50: "#FEF2F2",
              100: "#FEE2E2",
              200: "#FECACA",
              300: "#FCA5A5",
              400: "#F87171",
              500: "#EF4444",
              600: "#DC2626",
              700: "#B91C1C",
              800: "#991B1B",
              900: "#7F1D1D",
              DEFAULT: "#EF4444",
              foreground: "#FFFFFF",
            },
            default: {
              50: "#F9FAFB",
              100: "#F3F4F6",
              200: "#E5E7EB",
              300: "#D1D5DB",
              400: "#9CA3AF",
              500: "#6B7280",
              600: "#4B5563",
              700: "#374151",
              800: "#1F2937",
              900: "#111827",
              DEFAULT: "#6B7280",
              foreground: "#FFFFFF",
            },
          },
        },
      },
      // 添加通用颜色
      addCommonColors: true,
    }),
    function ({ addUtilities }) {
      addUtilities({
        ".scrollbar-none": {
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
          "&::-webkit-scrollbar": {
            display: "none",
          },
        },
        ".focus-none": {
          outline: "none !important",
          "box-shadow": "none !important",
        },
      });
    },
  ],
};
