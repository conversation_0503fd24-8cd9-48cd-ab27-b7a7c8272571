# FrameSound Doc View - Frontend Project
# Copyright (c) 2024 FrameSound Tech Inc.

.DEFAULT_GOAL := help
.PHONY: help
.PHONY: init dev build start
.PHONY: lint format check
.PHONY: clean reset deploy
.PHONY: outdated update reinstall
.PHONY: run

# ============================================================================
# Colors and Formatting
# ============================================================================
# Color Usage Rules:
# - CYAN: Commands, actions being performed (🚀 Starting, 🔍 Running, etc.)
# - GREEN: Success messages, completion indicators (✅ Done, 📦 Installing)
# - YELLOW: Warnings, important notes (⚠️ Warning, 💡 Tips)
# - RED: Errors, critical information (❌ Error, 🚨 Critical)
# - BOLD: Titles, headers, important text emphasis
# - Target names in help should use CYAN for consistency

CYAN := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
WHITE := \033[97m
RESET := \033[0m
BOLD := \033[1m

# 自定义颜色
IVORY_WHITE := \033[38;2;240;232;221m
VIOLET := \033[38;2;68;58;167m


# ASCII 艺术文字定义
define QUOTE_ASCII
$(VIOLET)$(BOLD)

           ▄▖         ▄▖       ▌
           ▙▖▛▘▀▌▛▛▌█▌▚ ▛▌▌▌▛▌▛▌
           ▌ ▌ █▌▌▌▌▙▖▄▌▙▌▙▌▌▌▙▌                                                                                                                                                                

 ██████╗ ██╗   ██╗ ██████╗ ████████╗███████╗
██╔═══██╗██║   ██║██╔═══██╗╚══██╔══╝██╔════╝
██║   ██║██║   ██║██║   ██║   ██║   █████╗  
██║▄▄ ██║██║   ██║██║   ██║   ██║   ██╔══╝  
╚██████╔╝╚██████╔╝╚██████╔╝   ██║   ███████╗
 ╚══▀▀═╝  ╚═════╝  ╚═════╝    ╚═╝   ╚══════╝                                                                                                 
                                     
$(RESET)
endef
export QUOTE_ASCII

# ============================================================================
# Environment Configuration
# ============================================================================

# Load environment variables from .env file
ifneq (,$(wildcard ./.env))
    include .env
    export
endif

# Default port for Next.js development server
dev_port ?= 9000
start_port ?= 3000

# ============================================================================
# Help Target
# ============================================================================

help: ## Show this help message
	@echo "$(CYAN)$(BOLD)🎨 FrameSound Doc View - Frontend Development Commands$(RESET)"
	@echo ""
	@echo "$(BOLD)Usage:$(RESET) make <target> [ARGS]"
	@echo ""
	@echo "$(BOLD)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(CYAN)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# ============================================================================
# Installation & Setup
# ============================================================================

init: ## Initialize project for first-time setup
	@echo "$(CYAN)📦 Initializing project...$(RESET)"
	@echo "$(CYAN)🔍 检查环境...$(RESET)"
	@command -v node >/dev/null 2>&1 || { echo "$(RED)❌ 需要安装 Node.js$(RESET)"; exit 1; }
	@echo "$(GREEN)✓ Node.js 已安装$(RESET)"
	@command -v npm >/dev/null 2>&1 || { echo "$(RED)❌ 需要安装 NPM$(RESET)"; exit 1; }
	@echo "$(GREEN)✓ NPM 已安装$(RESET)"
	@echo "$(CYAN)📦 安装项目依赖...$(RESET)"
	npm install
	@if [ ! -f .env ]; then \
		echo "$(YELLOW)⚠️ 未找到 .env 文件，创建示例配置...$(RESET)"; \
		echo "# 项目环境变量\nNEXT_PUBLIC_API_URL=http://localhost:8000/api" > .env; \
		echo "$(GREEN)✓ 已创建示例 .env 文件$(RESET)"; \
	fi
	@echo "$(CYAN)🧹 清理缓存...$(RESET)"
	rm -rf .next/ .eslintcache
	@echo "$(GREEN)✅ 项目初始化完成!$(RESET)"
	@echo "$(YELLOW)💡 提示: 运行 'make dev' 启动开发服务器$(RESET)"

# ============================================================================
# Development
# ============================================================================

dev: ## Start Next.js development server
	@echo "$(CYAN)🚀 Starting development server on port $(dev_port)...$(RESET)"
	@npm run dev & PID=$$!; \
	( sleep 3; \
	  if ps -p $$PID > /dev/null; then \
	    echo "$$QUOTE_ASCII"; \
	    echo "$(GREEN)✅ Dev server has been successfully started on port $(dev_port)$(RESET)"; \
	  fi ) & \
	wait $$PID

build: ## Build Next.js project for production
	@echo "$(CYAN)🏗️ Building project for production...$(RESET)"
	npm run build
	@echo "$(GREEN)✅ Build completed!$(RESET)"

start: ## Start production server
	@echo "$(CYAN)🌐 Starting production server...$(RESET)"
	npm run start

css: ## Rebuild Tailwind CSS
	@echo "$(CYAN)🎨 Building CSS...$(RESET)"
	npm run build:css
	@echo "$(GREEN)✅ CSS built!$(RESET)"

# ============================================================================
# Code Quality
# ============================================================================

lint: ## Run ESLint
	@echo "$(CYAN)🔍 Running ESLint...$(RESET)"
	npm run lint

format: ## Format code with Prettier
	@echo "$(CYAN)🎨 Formatting code...$(RESET)"
	npx prettier --write "src/**/*.{js,jsx,ts,tsx}"
	@echo "$(GREEN)✅ Code formatted!$(RESET)"

check: lint format ## Run all code quality checks
	@echo "$(GREEN)✅ All checks completed!$(RESET)"

# ============================================================================
# Build & Deployment
# ============================================================================

clean: ## Clean build artifacts and caches
	@echo "$(CYAN)🧹 Cleaning build artifacts...$(RESET)"
	rm -rf .next/ out/
	rm -rf .eslintcache
	@echo "$(GREEN)✅ Build artifacts cleaned!$(RESET)"

reset: clean ## Reset project to completely clean state
	@echo "$(CYAN)🧹 Removing node_modules and package-lock.json...$(RESET)"
	rm -rf node_modules/
	rm -f package-lock.json
	@echo "$(YELLOW)⚠️  Node modules and lock file removed! Dependencies will be re-resolved$(RESET)"
	@echo "$(GREEN)✅ Project environment reset completed! Run 'make init' to reinitialize$(RESET)"

deploy: ## Placeholder for deployment command
	@echo "$(YELLOW)⚠️ Deployment configuration not implemented yet$(RESET)"
	@echo "$(CYAN)🚀 To deploy, you would typically:$(RESET)"
	@echo "  1. Build the project (make build)"
	@echo "  2. Deploy to your hosting platform"

# ============================================================================
# Maintenance
# ============================================================================

outdated: ## Check for outdated dependencies
	@echo "$(CYAN)📊 Checking for outdated dependencies...$(RESET)"
	npm outdated

update: ## Update dependencies to latest versions
	@echo "$(CYAN)🔄 Updating dependencies...$(RESET)"
	npm update
	@echo "$(GREEN)✅ Dependencies updated! Check with 'make outdated'$(RESET)"

reinstall: ## Reinstall all dependencies (delete and install)
	@echo "$(CYAN)🧹 Removing node_modules and package-lock.json...$(RESET)"
	rm -rf node_modules/
	@echo "$(CYAN)📦 Reinstalling dependencies...$(RESET)"
	npm install
	@echo "$(GREEN)✅ Dependencies reinstalled successfully!$(RESET)"

# ============================================================================
# Utilities
# ============================================================================

run: ## Run arbitrary NPM command (usage: make run CMD="command")
	@if [ -z "$(CMD)" ]; then \
		echo "$(YELLOW)⚠️ No command specified. Usage: make run CMD=\"command\"$(RESET)"; \
		exit 1; \
	fi
	@echo "$(CYAN)🏃 Running: npm $(CMD)$(RESET)"
	npm $(CMD) 