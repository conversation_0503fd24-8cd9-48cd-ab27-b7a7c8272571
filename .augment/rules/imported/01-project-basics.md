---
type: "always_apply"
---

# framesound-doc-view 项目基础规范

## 技术栈
- React 18
- Next.js 14.1.4 (使用 App Router)
- TypeScript 5+
- Tailwind CSS 3.3（使用内联样式工具类）
- framesound-ui (本地UI组件库)
- HeroUI 组件 (@heroui/react, @heroui/theme, @heroui/toast)
- Framer Motion 12.15.0 (动画库)

## 项目结构
```
/src
  /app             # Next.js 应用目录（使用 App Router）
    /[route]       # 动态路由目录
    layout.tsx     # 根布局
    page.tsx       # 首页
    globals.css    # 全局样式
  /components      # 组件目录
    /editor        # 编辑器相关组件
    /sidebar       # 侧边栏组件
    /top-nav       # 顶部导航组件
    /ui            # 通用UI组件（如按钮、输入框等全局共用的基础组件）
    /ai-panel      # AI面板相关组件
  /utils           # 工具函数
  /hooks           # 自定义React Hooks
  /services        # 服务层代码（API交互等）
  /api             # API路由处理
```

## 命名规范
- 组件使用 PascalCase (例如 `Button.tsx`, `Sidebar.tsx`)
- 工具函数、钩子等使用 camelCase (例如 `useAuth.ts`, `formatDate.ts`)
- 文件名尽量使用有意义的名称，避免缩写
- CSS类名使用 kebab-case 或 Tailwind 类名

## 导入顺序
1. React相关导入
2. 第三方库导入
3. 项目内组件导入
4. 工具函数、钩子等导入
5. 类型导入
6. 样式导入

## 提交规范
- 使用语义化提交信息
- 确保代码通过ESLint检查无警告和错误

## 项目特性
- 支持亮色/暗色模式（通过Tailwind的darkMode: "class"实现）
- 使用@heroui/toast实现消息提示
- 性能优化方面遵循Next.js最佳实践
- 页面布局采用响应式设计

## 技术限制
- 确保兼容主流现代浏览器
- 符合Web无障碍标准(WCAG)
- 确保代码性能和加载速度最优化
