---
type: "agent_requested"
---

# framesound-doc-view API和数据处理规范

> API interaction and environment variables, etc.

## API交互
- 使用标准的fetch API或axios
- API调用应在services目录下组织
- 处理API错误并展示适当的用户提示

## 环境变量管理规范
- 所有API地址、密钥等配置必须通过环境变量管理
- 在项目根目录维护 `.env.example` 文件，列出所有必需的环境变量及说明
- 本地开发使用 `.env.local` 文件（已在 .gitignore 中忽略）
- 严禁在代码中硬编码API地址、密钥等敏感信息
- 严禁在代码中为环境变量设置默认值，例如：
  ```typescript
  // ❌ 错误示例
  const apiUrl = process.env.API_URL || 'https://api.example.com'
  
  // ✅ 正确示例
  const apiUrl = process.env.API_URL
  if (!apiUrl) {
    throw new Error('环境变量 API_URL 未设置')
  }
  ```
- 环境变量命名规范：
  - 使用大写字母和下划线
  - 使用有意义的前缀（如：API_、AUTH_等）
  - 示例：API_BASE_URL、AUTH_TOKEN_SECRET
- 在代码中使用 `process.env` 访问环境变量
- 确保在使用环境变量前进行存在性检查

## 错误处理
- 遵循 fail-fast 原则，尽早将错误暴露出来
- 不对错误进行不必要的二次包装
- 仅在必要的地方使用 try/catch 结构
- API 请求错误处理应直接抛出详细的错误信息
- 使用工具函数显示错误提示（如src/utils/toast.ts中的showErrorToast）
- 在组件中捕获和处理错误时，保留原始错误信息
- 对于需要全局处理的错误，使用专门的错误边界组件
- 避免静默失败，确保用户能够看到错误消息

## 数据流管理
- API响应数据应在服务层进行初步处理和类型转换
- 组件内应避免直接处理原始API数据，应使用经过处理的数据
- 数据缓存策略应根据数据更新频率和重要性来确定
- 大型数据集应考虑分页加载或虚拟滚动等优化方式
