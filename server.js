const { createServer } = require("https");
const { parse } = require("url");
const next = require("next");
const fs = require("fs");
const path = require("path");

const dev = process.env.NODE_ENV !== "production";
const hostname = "quote.framesound.tech";
const port = 9000;

// 创建Next.js应用实例
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

const httpsOptions = {
  key: fs.readFileSync(
    path.join(__dirname, ".cert", "quote.framesound.tech+3-key.pem")
  ),
  cert: fs.readFileSync(
    path.join(__dirname, ".cert", "quote.framesound.tech+3.pem")
  ),
};

app.prepare().then(() => {
  createServer(httpsOptions, (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(`> HTTPS服务已启动: https://${hostname}:${port}`);
  });
});
