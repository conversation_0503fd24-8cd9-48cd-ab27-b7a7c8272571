# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FrameSound Doc View is an AI-powered legal document editing tool designed to be the "Cursor for lawyers". Built with Next.js 14, React 18, and TipTap editor, it provides intelligent document processing, editing, and analysis capabilities for legal professionals.

## Key Development Commands

### Development Workflow
- `npm run dev` - Start development server on port 9000
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run build:css` - Rebuild Tailwind CSS

### Make Commands (Recommended)
- `make dev` - Start development server with formatted output
- `make build` - Build project with progress indication
- `make lint` - Run code quality checks
- `make format` - Format code with Prettier
- `make check` - Run all code quality checks (lint + format)
- `make clean` - Clean build artifacts and caches
- `make init` - Initialize project for first-time setup

## Architecture & Key Components

### Core Application Structure
- **Next.js 14 App Router**: Main application framework with app/ directory structure
- **TipTap Editor**: Rich text editor with custom extensions for legal document editing
- **AI Panel**: Chat interface for AI-powered document assistance
- **Sidebar**: File management and navigation
- **Services Layer**: API interaction and data management

### TipTap Editor Architecture
The editor is the core component with:
- **Custom Extensions**: `title-extension.ts` and `indent-extension.ts` for specialized document formatting
- **Toolbar Components**: Modular UI components for editor controls in `/components/editor/components/editor-toolbar-ui/`
- **Editor Primitives**: Reusable UI primitives in `/components/editor/components/editor-ui-primitive/`
- **Rich Formatting**: Support for headings, lists, text alignment, highlighting, font families

### State Management
- React Context API for editor state
- Zustand for global state management
- Local component state with useState/useReducer

### Styling System
- Tailwind CSS 3.3 for utility-first styling
- SCSS for complex editor styles in `/src/styles/`
- Dark mode support via Tailwind's class-based system
- framesound-ui component library integration

## Development Guidelines

### Component Organization
- `/components/ui/` - Global reusable UI components only
- `/components/[feature]/` - Feature-specific components with their own `/components/` subdirectories
- Component files use PascalCase naming
- Each feature directory contains related components organized by functionality
- Avoid over-nesting components; break down large components into smaller, focused ones
- Follow single responsibility principle for all components and functions

### Code Standards
- TypeScript 5+ with strict type checking
- Interface definitions preferred over type aliases for props
- Functional components with React Hooks
- ESLint configuration with Next.js rules
- Avoid `any` type; use TypeScript's strict mode
- Follow DRY (Don't Repeat Yourself) principle
- Use meaningful variable and function names

### Styling Guidelines
- **Atomic Classes First**: Use Tailwind utility classes exclusively when possible
- Avoid custom CSS/SCSS files unless absolutely necessary
- Use `@apply` directive for complex reusable styles in Tailwind
- Custom colors must be defined in `tailwind.config.js`
- Desktop-first responsive design approach
- Support dark mode via Tailwind's class-based system

### Environment Variables & API Management
- All API endpoints and secrets via environment variables
- **No default values or hardcoded URLs in code**
- Environment variables must be validated before use
- Use `.env.local` for development (gitignored)
- Maintain `.env.example` with all required variables documented
- Variable naming: UPPERCASE_WITH_UNDERSCORES with meaningful prefixes (API_, AUTH_)
- Example validation pattern:
  ```typescript
  const apiUrl = process.env.API_URL
  if (!apiUrl) {
    throw new Error('环境变量 API_URL 未设置')
  }
  ```

### Import Order Convention
1. React imports
2. Third-party library imports  
3. Internal component imports
4. Utility functions and hooks
5. Type imports
6. Style imports

### Performance Optimization
- Use `React.memo()`, `useMemo()`, and `useCallback()` to prevent unnecessary re-renders
- Implement dynamic imports and code splitting for large components
- Use Next.js Image component for optimized images
- Avoid unnecessary state updates and side effects
- Monitor Core Web Vitals (FCP, LCP, CLS)

## AI Chat Service Integration

The application includes a service layer for AI chat functionality:
- **Interface-based design**: `ChatService.interface.ts` defines common interface
- **Multiple implementations**: OpenAI and real backend API services
- **Factory pattern**: `chatServiceFactory.ts` for service instantiation
- **Error handling**: Fail-fast approach with preserved error context

## Local UI Library

Uses `framesound-ui` (local package) located at `../framesound-ui/framesound-ui-0.3.2.tgz` for custom components alongside HeroUI for additional UI elements.

## Error Handling Standards

### Fail-Fast Approach
- Expose errors early rather than hiding them
- Preserve original error information without unnecessary wrapping
- Use try/catch only where necessary
- API errors should throw detailed error information
- Use `src/utils/toast.ts` showErrorToast for user-facing error messages
- Implement error boundaries for global error handling
- Never fail silently - ensure users see error messages

## Testing & Quality Assurance

### Testing Strategy
- Write unit tests for critical functionality using Jest and React Testing Library
- Test complex business logic with detailed test cases
- Maintain reasonable test coverage
- Ensure tests are independent and repeatable
- Focus on testing behavior, not implementation details

### Pre-Commit Checklist
1. Run `make check` to verify linting and formatting
2. Test editor functionality thoroughly
3. Verify responsive design on multiple screen sizes
4. Check dark mode compatibility
5. Validate accessibility features
6. Test error handling paths

## Security & Accessibility

### Security Requirements
- Prevent XSS attacks - never render untrusted content directly
- Use HTTPS for all API communication
- No sensitive data in localStorage or URLs
- Implement proper CORS policies
- Keep dependencies updated and security-audited

### Accessibility (WCAG 2.1)
- All interactive elements must support keyboard navigation
- Use appropriate ARIA attributes
- Ensure color contrast meets standards
- Provide alternative text and labels
- Test with screen readers

## Browser Compatibility & Internationalization

### Browser Support
- Target modern browsers: Chrome, Firefox, Safari, Edge
- Implement responsive design for various screen sizes
- Graceful degradation for unsupported features
- Test critical functionality across different environments

### Internationalization
- **Early Development**: Use pure English for all UI text
- **Later Phases**: Implement i18n solution for multi-language support
- Consider text length variations across languages
- Localize dates, times, and number formats

## Performance Considerations

- TipTap editor configured with `immediatelyRender: false` to prevent SSR hydration issues
- Sticky toolbar positioning for better UX
- Framer Motion for smooth animations
- Use Chrome DevTools and React DevTools for performance analysis