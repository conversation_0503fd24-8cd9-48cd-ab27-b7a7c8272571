{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "A 启动开发服务",
      "type": "shell",
      "command": "make",
      "args": ["dev"],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "dedicated",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "🚀 启动Next.js开发服务器 (端口9000)"
    },
    // {
    //   "label": "构建项目",
    //   "type": "shell",
    //   "command": "make",
    //   "args": ["build"],
    //   "group": "build",
    //   "presentation": {
    //     "echo": true,
    //     "reveal": "always",
    //     "focus": false,
    //     "panel": "shared",
    //     "showReuseMessage": true,
    //     "clear": true
    //   },
    //   "options": {
    //     "cwd": "${workspaceFolder}"
    //   },
    //   "problemMatcher": [],
    //   "detail": "🏗️ 构建Next.js项目"
    // },
    {
      "label": "B1 Lint & Format",
      "type": "shell",
      "command": "make",
      "args": ["check"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": ["$eslint-stylish"],
      "detail": "🎨 检查和格式化代码"
    },
    {
      "label": "B2 仅运行Lint",
      "type": "shell",
      "command": "make",
      "args": ["lint"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": ["$eslint-stylish"],
      "detail": "🔍 仅运行ESLint检查"
    },
    // {
    //   "label": "仅运行Format",
    //   "type": "shell",
    //   "command": "make",
    //   "args": ["format"],
    //   "group": "build",
    //   "presentation": {
    //     "echo": true,
    //     "reveal": "always",
    //     "focus": false,
    //     "panel": "shared",
    //     "showReuseMessage": true,
    //     "clear": true
    //   },
    //   "options": {
    //     "cwd": "${workspaceFolder}"
    //   },
    //   "problemMatcher": [],
    //   "detail": "🎨 仅格式化代码"
    // },
    {
      "label": "C1 清理并重置",
      "type": "shell",
      "command": "make",
      "args": ["reset"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "🧹 清理项目环境并重置项目"
    },
    {
      "label": "C2 初始化项目",
      "type": "shell",
      "command": "make",
      "args": ["init"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "📦 完整初始化项目环境 (依赖安装、环境配置等)"
    },
    {
      "label": "C3 仅清理缓存",
      "type": "shell",
      "command": "make",
      "args": ["clean"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "🧹 仅清理构建缓存"
    },
    {
      "label": "D1 重装依赖",
      "type": "shell",
      "command": "make",
      "args": ["reinstall"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "🔄 删除并重新安装所有依赖"
    },
    {
      "label": "D2 检查依赖更新",
      "type": "shell",
      "command": "make",
      "args": ["outdated"],
      "group": "build",
      "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared",
          "showReuseMessage": true,
          "clear": true
      },
      "options": {
          "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "📊 检查过期依赖"
    },
    {
      "label": "部署",
      "type": "shell",
      "command": "make",
      "args": ["deploy"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "dedicated",
        "showReuseMessage": true,
        "clear": true
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "🚀 部署到生产环境 (未实现)"
    }
  ]
}
