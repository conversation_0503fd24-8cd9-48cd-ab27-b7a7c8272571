{"name": "framesound-ui-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 9000", "dev:https": "node server.js", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build:css": "tailwindcss -i ./src/app/globals.css -o ./src/app/output.css"}, "dependencies": {"@floating-ui/dom": "^1.7.1", "@floating-ui/react": "^0.27.13", "@heroui/react": "^2.7.8", "@heroui/theme": "^2.4.15", "@heroui/toast": "^2.0.9", "@tiptap/core": "^3.0.0-beta.8", "@tiptap/extension-bullet-list": "^3.0.0-beta.8", "@tiptap/extension-font-family": "^3.0.0-next.8", "@tiptap/extension-highlight": "^3.0.0-beta.8", "@tiptap/extension-list": "^3.0.0-beta.8", "@tiptap/extension-ordered-list": "^3.0.0-beta.8", "@tiptap/extension-subscript": "^3.0.0-beta.8", "@tiptap/extension-superscript": "^3.0.0-beta.8", "@tiptap/extension-text-align": "^3.0.0-beta.8", "@tiptap/extension-text-style": "^3.0.0-next.8", "@tiptap/extension-underline": "^3.0.0-beta.8", "@tiptap/extensions": "^3.0.0-beta.8", "@tiptap/pm": "^3.0.0-beta.8", "@tiptap/react": "^3.0.0-beta.8", "@tiptap/starter-kit": "^3.0.0-beta.8", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "framesound-ui": "file:../framesound-ui/framesound-ui-0.3.2.tgz", "next": "14.1.4", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8", "sass": "^1.89.2", "tailwindcss": "^3.3.0", "typescript": "^5"}}