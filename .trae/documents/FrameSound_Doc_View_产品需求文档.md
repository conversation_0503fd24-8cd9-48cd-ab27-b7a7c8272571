## 1. Product Overview

FrameSound Doc View 是一款专为律师打造的 AI 驱动法律文档编辑工具，旨在成为法律领域的 Cursor。通过先进的 AI Agent 技术，为法律专业人士提供智能化的文档处理、编辑和分析体验，显著提升工作效率和文档质量。

* 解决法律文档撰写效率低、格式不规范、法条引用繁琐等问题，为律师、法务人员提供智能化的文档编辑解决方案。

* 目标成为法律行业标准的文档编辑工具，提升整个行业的工作效率和文档质量标准。

## 2. Core Features

### 2.1 User Roles (暂不实现权限系统)

| Role     | Registration Method | Core Permissions            |
| -------- | ------------------- | --------------------------- |
| 默认用户     | 直接访问系统              | 可使用所有核心功能，包括项目管理、文档编辑、AI助手等 |
| 其他（暂不实现） | <br />              | <br />                      |

### 2.2 Feature Module

我们的法律文档编辑平台包含以下主要页面：

1. **首页**：产品介绍、快速入口导航（现在只是快捷入口）
2. **项目管理页面**：项目列表展示、项目创建编辑、搜索筛选功能
3. **文档编辑页面**：智能文档编辑器、文件管理侧边栏、AI助手面板

### 2.3 Page Details

| Page Name | Module Name | Feature description               |
| --------- | ----------- | --------------------------------- |
| 首页        | 产品介绍区       | 展示产品核心价值，提供快速入口按钮跳转到项目页面          |
| 首页        | 导航区域        | 提供登录按钮和进入应用的主要入口                  |
| 项目管理页面    | 项目工具栏       | 项目筛选（全部、收藏、最近）、视图切换（网格/列表）、搜索功能   |
| 项目管理页面    | 项目列表        | 展示项目卡片，支持项目创建、编辑、删除、复制、收藏操作       |
| 项目管理页面    | 项目操作        | 创建新项目、编辑项目信息、删除确认、项目复制功能          |
| 文档编辑页面    | 文件管理侧边栏     | 项目文件树展示、文件创建删除、文件夹管理              |
| 文档编辑页面    | 智能编辑器       | 富文本编辑、Markdown支持、格式化工具栏、字体调整、主题切换 |
| 文档编辑页面    | AI助手面板      | 聊天对话、文档分析、设置配置、待办事项管理             |
| 文档编辑页面    | 响应式布局       | 移动端适配、面板折叠展开、触摸交互优化               |
| <br />    | <br />      | <br />                            |

## 3. Core Process

用户主要操作流程如下：

1. 用户访问首页，了解产品功能后点击"Enter Quote"进入应用
2. 进入项目管理页面，可以查看现有项目或创建新项目
3. 用户可以通过筛选、搜索功能快速找到目标项目
4. 点击项目卡片进入文档编辑页面，开始文档编辑工作
5. 在编辑页面，用户可以通过侧边栏管理文件，使用AI助手获得智能建议
6. 编辑完成后，文档自动保存，用户可以返回项目列表或继续编辑

```mermaid
graph TD
  A[首页] --> B[项目管理页面]
  B --> C[文档编辑页面]
  C --> B
```

## 4. User Interface Design

### 4.1 Design Style

* **强调色**：primaryBtn 系列颜色作为主品牌色（不要高频使用除非强调，主要使用 secondaryBtn 在需要凸显的场景下）

* **次要色**：secondaryBtn 系列颜色用于按钮和强调元素

* 背景色：使用 backgroundDeep 作为主背景色，同时兼容深色背景主题（通过 HeroUI 的 theme 及 next them 实现）

* **按钮样式**：圆角设计，支持悬停效果和过渡动画

* **字体**：系统默认字体，支持多种字号（小、中、大）自适应调整

* **布局风格**：现代化卡片式布局，顶部导航，响应式设计

* **图标风格**：使用 Lucide React 图标库，简洁现代的线性图标

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements                      |
| --------- | ----------- | -------------------------------- |
| 首页        | 产品介绍区       | 居中布局，大标题+描述文字，深色背景，简洁现代风格        |
| 首页        | 导航按钮        | 圆角按钮，悬停效果，过渡动画，secondaryBtn 配色方案 |
| 项目管理页面    | 工具栏         | 水平布局，左侧标题+筛选标签，右侧搜索+视图切换，高度固定    |
| 项目管理页面    | 项目卡片        | 网格/列表双视图，卡片阴影效果，悬停动画，操作按钮        |
| 文档编辑页面    | 侧边栏         | 可折叠设计，文件树结构，深色主题，图标+文字           |
| 文档编辑页面    | 编辑器         | 全屏编辑区域，工具栏浮动，字体大小自适应，主题切换        |
| 文档编辑页面    | AI面板        | 右侧固定，可折叠，标签页切换，圆角设计，过渡动画         |

### 4.3 Responsiveness

产品采用桌面端优先的响应式设计，支持桌面端、平板和手机端访问。在移动端会自动调整字体大小、隐藏部分UI元素、优化触摸交互体验。AI面板在移动端会以全屏模态形式展示，确保良好的使用体验。
