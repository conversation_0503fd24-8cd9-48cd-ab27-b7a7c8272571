# FrameSound Doc View API 文档

## 1. API 概述

FrameSound Doc View 是一个 AI 驱动的法律文档编辑工具，提供完整的 RESTful API 接口，支持用户管理、项目管理、文档编辑和文件处理等核心功能。

* **API 版本**: v1.0

* **基础 URL**: `xxxx/v1`

* **认证方式**: JWT Http-only cookie\*\*（mock 开发环境下需添加\*\* **set\_user\_id​）**

* **数据格式**: JSON

### 一、系统端点（已更新）

#### 1.1 ​**​POST** `/ping`

* 功能：健康检查端点。用于检查服务是否正常运行 不需要认证，可以被负载均衡器或监控系统调用

* 响应：

  ```JSON
  {
    "status": "ok",
    "message": "服务正常运行"
  }
  ```

***

### 二、认证端点（已更新）

#### 2.1 POST `/auth/send_verification`

* 功能：发送验证码端点

* 请求体：application/json（ProfileUpdateRequest）

  ```JSON
  {
    "contact": "string",
    "browser_fingerprinting": "string",
    "device_id": "string"
  }
  ```

* 响应：

  200：成功

  ```JSON
  {
    "success": true,
    "message": "验证码发送成功",
    "expires_in": 0
  }
  ```

  400：Error: Bad Request

  ```JSON
  {
    "detail": "验证码发送失败，请稍后重试"
  }
  ```

  422：Validation Error

  ```JSON
  {
    "detail": [
      {
        "loc": [
          "string",
          0
        ],
        "msg": "string",
        "type": "string"
      }
    ]
  }
  ```

#### 2.2 POST `/auth/sign_in_or_up`

* 功能：无密码登录/注册端点

* 请求体：application/json（ProfileUpdateRequest）

  ```JSON
  {
    "contact": "string",
    "code": "string",
    "browser_fingerprinting": "string",
    "device_id": "string"
  }
  ```

* 响应：

  * 200: 登录成功 (或已有用户登录成功)

    ```JSON
    {
      "success": true,
      "message": "认证成功",
      "user_id": "string"
    }
    ```

  * 201: 注册成功 (新用户注册成功)

  * 400: 请求参数错误 (联系方式格式不正确)

  * 460: 验证码错误

  * 461: 账户不存在 (验证码不存在或已过期)

  * 500: 系统异常 (验证码验证异常、GoTrue 服务异常等)

#### 2.3 POST `/auth/logout`

* 功能：登出端点

* 请求体：application/json（ProfileUpdateRequest）

* 响应：

  200：登出成功

  ```JSON
  {
    "success": true,
    "message": "认证成功",
    "user_id": "string"
  }
  ```

  401：验证失败

  ```JSON
  {
    "success": false,
    "message": "JWT令牌无效或已过期，请重新登录",
    "error_code": "JWT_EXPIRED"
  }
  ```

  500：系统异常

#### 2.4 GET `/auth/verify_status`

* 功能：此端点通过 JWT 中间件验证用户的认证状态。 如果 JWT 有效（包括自动刷新的情况），返回用户认证信息。 如果 JWT 无效或过期且无法刷新，中间件会返回 401 错误。

* 请求体：application/json（ProfileUpdateRequest）

* 响应：

  * 200: 用户认证有效

    ```JSON
    {
      "success": true,
      "message": "认证成功",
      "user_id": "string"
    }
    ```

  * 401: JWT 令牌无效或已过期（由中间件处理）

    ```JSON
    {
      "success": false,
      "message": "JWT令牌无效或已过期，请重新登录",
      "error_code": "JWT_EXPIRED"
    }
    ```

  * 500: 系统异常

***

### 三、用户端点（未更新，不是最终版，不要参考）

#### 3.1 GET `/users/profile`

* 功能：获取用户资料

* 响应：

  * 200：返回用户资料对象

    ```JSON
    {
      "real_name": "string",
      "company_name": "string",
      "company_size": "string",
      "position": "string",
      "avatar": "string",
      "id": "UUID"
    }
    ```

#### 3.2 PATCH `/users/profile`

* 功能：更新用户资料

* 请求体：application/json（ProfileUpdateRequest）

  ```JSON
  {
    "real_name": "string",
    "company_name": "string",
    "company_size": "string",
    "position": "string",
    "avatar": "string"
  }
  ```

* 响应：

  * 200：返回更新后的用户资料对象

  * 422：Validation Error

***

### 四、项目端点（已更新）

#### 4.2 GET `/projects/`

* 功能：获取用户的所有项目列表

* **参数 ​**​：无

* 响应：

  200：成功

  ```JSON
  [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "string",
      "created_at": "2025-07-27T07:23:30.335Z",
      "last_edited_at": "2025-07-27T07:23:30.335Z",
      "description": "string",
      "status": "ARCHIVED",
      "is_favorite": true
    }
    {
      "id": "5fa85f64-5417-4562-b3fc-2c963f66afa6",
      "name": "string",
      "created_at": "2025-07-27T07:23:30.335Z",
      "last_edited_at": "2025-07-27T07:23:30.335Z",
      "description": "string",
      "status": "ARCHIVED",
      "is_favorite": true
    }
  ]
  ```

  401：身份验证失败（JWT 令牌无效或已过期）

  ```JSON
  {
    "success": false,
    "message": "JWT令牌无效或已过期，请重新登录",
    "error_code": "JWT_EXPIRED"
  }
  ```

#### 4.2 POST **`/projects/`**

* ​**​ 功能 ​**​：创建新项目

* ​**​ 请求体 ​**​：

  ```JSON
  {
    "name": "新项目名称",
    "description": "项目描述",
    "status": "ARCHIVED"
  }
  ```

* 响应：

  201：成功

  ```JSON
  {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "name": "string",
    "created_at": "2025-07-27T07:42:42.753Z",
    "last_edited_at": "2025-07-27T07:42:42.753Z",
    "description": "string",
    "status": "ARCHIVED"
  }
  ```

  422：请求体参数验证失败

#### 4.3 **PATCH `/projects/{project_id}`**

* ​**​ 功能 ​**​：更新项目信息

* ​**​ 路径参数 ​**​：

  * `project_id` (UUID) 项目 ID

* **请求体 ​**​：

  ```JSON
  {
    "name": "更新后的名称",
    "description": "更新后的描述",
    "status": "ACTIVE"
  }
  ```

* 响应：

  200：成功

  ```JSON
  {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "name": "更新后的名称",
    "created_at": "2025-07-27T07:41:31.686Z",
    "last_edited_at": "2025-07-27T07:41:31.686Z",
    "description": "更新后的描述",
    "status": "ACTIVE"
  }
  ```

  422：路径参数或请求体验证失败

  ```JSON
  {
    "detail": [
      {
        "loc": [
          "string",
          0
        ],
        "msg": "string",
        "type": "string"
      }
    ]
  }
  ```

<br />

#### 4.4 **DELETE `/projects/{project_id}`**

* ​**​ 功能 ​**​：删除项目

* ​**​ 路径参数 ​**​：

  * `project_id` (UUID) 项目 ID

* ​**​ 响应 ​**​：

  * `204`  成功删除（无响应体）

* ​**​ 错误码 ​**​：

  * `422`  路径参数验证失败

<br />

**4.5** **GET** `/projects/{project_id}/tree`

* **功能 ​**​：获取项目文件树结构（返回项目下所有文件和文档的 ID 和名称）

* ​**​ 路径参数 ​**​：

  * `project_id` (UUID) 项目 ID

* **响应 ​**​：

  ```JSON
  [
    {
      "created_at": "2025-07-27T15:54:15.731Z",
      "updated_at": "2025-07-27T15:54:15.731Z",
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "region": "PERSONAL",
      "name": "string"
    }
  ]
  ```

* ​**​ 错误码 ​**​：

  * `422`  路径参数验证失败

<br />

***

### 五、文档端点（已更新）

#### 5.1 POST `/docs/`

* 功能：创建文档

* 请求体：application/json（DocCreateRequest）

  ```JSON
  {
    "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "region": "PERSONAL",
    "doc_name": "string",
    "content": "string",
    "meta_data": {
      "additionalProp1": {}
    }
  }
  ```

  响应：

  * 200：返回创建的文档对象

    ```JSON
    {
      "created_at": "2025-07-27T17:21:16.267Z",
      "updated_at": "2025-07-27T17:21:16.267Z",
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "region": "PERSONAL",
      "doc_name": "string",
      "content": "string",
      "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "owner_user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    }
    ```

  * 422：Validation Error

<br />

#### 5.2 GET `/docs/{doc_id}`

* 功能：根据 ID 获取文档详细内容

* 路径参数：

  * `doc_id` (必需, string)

* 响应：

  * 200：返回文档对象

    ```JSON
    {
      "created_at": "2025-07-27T17:22:01.757Z",
      "updated_at": "2025-07-27T17:22:01.757Z",
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",  //doc_id
      "region": "PERSONAL",
      "doc_name": "string",
      "content": "string",  //文档内容
      "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "owner_user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    }
    ```

  * 422：Validation Error

#### 5.3 PATCH `/docs/{doc_id}`

* 功能：更新文档内容

* 路径参数：

  * `doc_id` (必需, string)

* 请求体：application/json（DocUpdateRequest）

  ```JSON
  {
    "region": "PERSONAL",
    "doc_name": "string",
    "content": "string"
  }
  ```

* 响应：

  * 200：返回更新后的文档对象

    ```JSON
    {
      "created_at": "2025-07-27T17:22:34.131Z",
      "updated_at": "2025-07-27T17:22:34.131Z",
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",  //doc_id
      "region": "PERSONAL",
      "doc_name": "string",
      "content": "string",  //文档内容
      "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "owner_user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    }
    ```

  * 422：Validation Error

#### 5.4 DELETE `/docs/{doc_id}`

* 功能：删除文档（软删除）

* 路径参数：

  * `doc_id` (必需, string)

* 响应：

  * 200：返回字符串（成功消息）

    ```JSON
    {
      "additionalProp1": "string",
      "additionalProp2": "string",
      "additionalProp3": "string"
    }
    ```

  * 422：Validation Error

<br />

***

### 六、文件端点（已更新）

#### 6.1 GET `/files/{file_id}`

* ​**​ 功能 ​**​：获取文件信息

* ​**​ 路径参数 ​**​：

  * `file_id` (字符串) 文件唯一标识符（UUID）

* ​**​ 响应 ​**​：&#x20;

  ```JSON
  {
    "created_at": "2025-07-27T20:24:54.990Z",
    "updated_at": "2025-07-27T20:24:54.990Z",
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "region": "PERSONAL",
    "file_name": "string",
    "content": "string",
    "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "owner_user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "source_url": "string",
    "file_size": 0
  }
  ```

* ​**​ 错误码 ​**​：

  * `401` 身份验证失败

  * `404` 文件不存在

  * `422` 参数验证失败

***

#### 6.2 PATCH `/files/{file_id}`

* ​**​ 功能 ​**​：更新文件元数据

* ​**​ 路径参数 ​**​：

  * `file_id` (字符串) 文件唯一标识符（UUID）

* ​**​ 请求体 ​**​：&#x20;

  ```JSON
  {
    "region": "PERSONAL",
    "file_name": "string",
    "content": "string"
  }
  ```

* ​**​ 响应 ​**​：&#x20;

  ```JSON
  {
    "created_at": "2025-07-27T20:25:09.122Z",
    "updated_at": "2025-07-27T20:25:09.122Z",
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "region": "PERSONAL",
    "file_name": "string",
    "content": "string",
    "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "owner_user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "source_url": "string",
    "file_size": 0
  }
  ```

* ​**​ 错误码 ​**​：

  * `401` 身份验证失败

  * `422` 请求体验证失败

***

#### 6.3 POST `/files/`

* ​**​ 功能 ​**​：创建新文件

* ​**​ 请求体 ​**​：

  ```JSON
  {
    "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "region": "PERSONAL",
    "file_name": "string",
    "content": "string",
    "source_url": "string",
    "meta_data": {
      "additionalProp1": {}
    }
  }
  ```

* ​**​ 响应 ​**​：&#x20;

  ```JSON
  {
    "created_at": "2025-07-27T20:25:26.635Z",
    "updated_at": "2025-07-27T20:25:26.635Z",
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "region": "PERSONAL",
    "file_name": "string",
    "content": "string",
    "project_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "owner_user_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "source_url": "string",
    "file_size": 0
  }
  ```

* ​**​ 错误码 ​**​：

  * `401` 身份验证失败

  * `422` 参数验证失败

***

### 七、公共错误响应

* ​**​401 Unauthorized​**​：

  ```JSON
  {
    "success": false,
    "message": "JWT令牌无效或已过期，请重新登录",
    "error_code": "JWT_EXPIRED"
  }
  ```

* ​**​422 Validation Error​**​：

  ```JSON
  {
    "detail": [
      {
        "loc": ["字段位置"],
        "msg": "错误信息",
        "type": "错误类型"
      }
    ]
  }
  ```

* ​**​404 Not Found​**​ (文件相关)：

  ```
  {
    "error": "file_not_found",
    "message": "请求的文件不存在或已被删除"
  }
  ```

***

### 八、公共数据结构

#### 枚举类型

* `ProjectStatus`: `"archived"`（文档中仅列出此状态）

* `Region`: `"personal"`（文档中仅列出此区域）

#### 对象结构

* ​**​ProjectItem​**​（项目条目）:

  ```JSON
  {
    "created_at": "datetime",
    "updated_at": "datetime",
    "id": "UUID",
    "name": "string",
    "last_edited_at": "datetime",
    "description": "string",
    "status": "archived",
    "is_favorite": true
  }
  ```

* ​**​TreeItem​**​（文件树条目）:

  ```JSON
  {
    "created_at": "datetime",
    "updated_at": "datetime",
    "project_id": "UUID",
    "region": "PERSONAL",
    "name": "string",
    "entity_type": "string"
  }
  ```

#### 错误响应

所有端点的 422 错误均返回相同结构：

```JSON
{
  "detail": [
    {
      "loc": ["string", 0],
      "msg": "string",
      "type": "string"
    }
  ]
}
```

***

### 关键注意事项

1. ​**​ 路径参数 ​**​：所有带`{id}`的端点均需在 URL 中传递 ID

2. ​**​HTTP 方法语义 ​**​：

   * `POST`：创建资源

   * `GET`：获取资源

   * `PATCH`：部分更新

   * `PUT`：完整替换

   * `DELETE`：删除资源

3. ​**​ 日期格式 ​**​：所有日期字段使用 ISO 8601 格式（如`2025-07-22T14:23:09.678Z`）

4. ​**​UUID 格式 ​**​：所有 ID 字段使用 UUIDv4 格式（如`3fa85f64-5717-4562-b3fc-2c963f66afa6`）

<br />

## 2. 版本更新

* **v1**: 初始版本，包含基础的用户、项目、文档和文件管理功能

* 后续版本将添加更多 AI 功能和协作特性

***

**注意**: 本 API 文档基于 FrameSound Doc View 产品需求文档生成，实际实现可能会有所调整。
​**​**
