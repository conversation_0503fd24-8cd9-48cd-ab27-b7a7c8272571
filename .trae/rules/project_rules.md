# framesound-doc-view 项目基础规范

## 技术栈
- React 18
- Next.js 14.1.4 (使用 App Router)
- TypeScript 5+
- Tailwind CSS 3.3（使用内联样式工具类）
- framesound-ui (本地UI组件库)
- HeroUI 组件 (@heroui/react, @heroui/theme, @heroui/toast)
- Framer Motion 12.15.0 (动画库)

## 项目结构
```
/src
  /app             # Next.js 应用目录（使用 App Router）
    /[route]       # 动态路由目录
    layout.tsx     # 根布局
    page.tsx       # 首页
    globals.css    # 全局样式
  /components      # 组件目录
    /editor        # 编辑器相关组件
    /sidebar       # 侧边栏组件
    /top-nav       # 顶部导航组件
    /ui            # 通用UI组件（如按钮、输入框等全局共用的基础组件）
    /ai-panel      # AI面板相关组件
  /utils           # 工具函数
  /hooks           # 自定义React Hooks
  /services        # 服务层代码（API交互等）
  /api             # API路由处理
```

## 命名规范
- 组件使用 PascalCase (例如 `Button.tsx`, `Sidebar.tsx`)
- 工具函数、钩子等使用 camelCase (例如 `useAuth.ts`, `formatDate.ts`)
- 文件名尽量使用有意义的名称，避免缩写
- CSS类名使用 kebab-case 或 Tailwind 类名

## 导入顺序
1. React相关导入
2. 第三方库导入
3. 项目内组件导入
4. 工具函数、钩子等导入
5. 类型导入
6. 样式导入

## 提交规范
- 使用语义化提交信息
- 确保代码通过ESLint检查无警告和错误

## 项目特性
- 支持亮色/暗色模式（通过Tailwind的darkMode: "class"实现）
- 使用@heroui/toast实现消息提示
- 性能优化方面遵循Next.js最佳实践
- 页面布局采用响应式设计

## 技术限制
- 确保兼容主流现代浏览器
- 符合Web无障碍标准(WCAG)
- 确保代码性能和加载速度最优化


# framesound-doc-view 前端开发规范

## 组件开发规范
- 优先使用函数式组件和React Hooks
- 优先使用 TypeScript 类型定义而不是PropTypes
- 组件props类型应明确定义，使用interface而非type
- 尽可能使用memo、useCallback和useMemo优化性能
- 大型组件应拆分成多个小组件

## 样式开发规范
- 原子类优先：全部采用Tailwind工具类，尽可能不要使用CSS/SCSS文件
- 复杂样式可使用Tailwind的@apply指令创建可复用类
- 自定义颜色使用tailwind.config.js中定义的颜色变量
- 遵循桌面端优先、兼容移动端的响应式设计原则

## UI组件
- 优先使用framesound-ui组件库中的组件
- 其次使用HeroUI等第三方组件库提供的组件
- 组件分类和存放规范：
  - `/components/ui`：仅存放全局通用的基础UI组件（如按钮、输入框、模态框等）
  - `/components/[feature-name]`：特定功能相关的组件应创建独立的目录（如 editor、sidebar 等）
    - `/components/[feature-name]/components`：用于存放构成该功能大组件的子组件
    - 示例结构：
      ```
      /components
        /editor
          Editor.tsx          # 主组件
          /components         # 编辑器的子组件
            ToolBar.tsx
            Canvas.tsx
            PropertyPanel.tsx
      ```
  - 组件目录命名应清晰表达其功能用途
  - 如果某个功能只需要一个简单组件，可直接在 components 目录下创建单文件组件

## 状态管理
- 简单状态使用useState和useReducer
- 复杂状态或跨组件共享状态使用React Context API
- 避免不必要的全局状态

## 注释规范
- 复杂逻辑需添加注释说明
- 公共API和关键函数需添加JSDoc注释
- TODO和FIXME使用标准格式：// TODO: 内容 或 // FIXME: 内容



# framesound-doc-view API和数据处理规范

> API interaction and environment variables, etc.

## API交互
- 使用标准的fetch API或axios
- API调用应在services目录下组织
- 处理API错误并展示适当的用户提示

## 环境变量管理规范
- 所有API地址、密钥等配置必须通过环境变量管理
- 在项目根目录维护 `.env.example` 文件，列出所有必需的环境变量及说明
- 本地开发使用 `.env.local` 文件（已在 .gitignore 中忽略）
- 严禁在代码中硬编码API地址、密钥等敏感信息
- 严禁在代码中为环境变量设置默认值，例如：
  ```typescript
  // ❌ 错误示例
  const apiUrl = process.env.API_URL || 'https://api.example.com'
  
  // ✅ 正确示例
  const apiUrl = process.env.API_URL
  if (!apiUrl) {
    throw new Error('环境变量 API_URL 未设置')
  }
  ```
- 环境变量命名规范：
  - 使用大写字母和下划线
  - 使用有意义的前缀（如：API_、AUTH_等）
  - 示例：API_BASE_URL、AUTH_TOKEN_SECRET
- 在代码中使用 `process.env` 访问环境变量
- 确保在使用环境变量前进行存在性检查

## 错误处理
- 遵循 fail-fast 原则，尽早将错误暴露出来
- 不对错误进行不必要的二次包装
- 仅在必要的地方使用 try/catch 结构
- API 请求错误处理应直接抛出详细的错误信息
- 使用工具函数显示错误提示（如src/utils/toast.ts中的showErrorToast）
- 在组件中捕获和处理错误时，保留原始错误信息
- 对于需要全局处理的错误，使用专门的错误边界组件
- 避免静默失败，确保用户能够看到错误消息

## 数据流管理
- API响应数据应在服务层进行初步处理和类型转换
- 组件内应避免直接处理原始API数据，应使用经过处理的数据
- 数据缓存策略应根据数据更新频率和重要性来确定
- 大型数据集应考虑分页加载或虚拟滚动等优化方式



# framesound-doc-view 质量和性能规范

## 性能优化
- 使用React.memo()、useMemo()和useCallback()避免不必要的重渲染
- 图片应进行适当优化，使用Next.js的Image组件
- 大型组件应使用动态导入和代码分割
- 避免不必要的状态更新和副作用
- 使用Chrome DevTools和React DevTools进行性能分析
- 优先考虑用户体验的关键指标（FCP、LCP、CLS等）

## 代码质量
- 使用ESLint和Prettier保持代码风格一致
- 遵循DRY（Don't Repeat Yourself）原则
- 组件和函数应遵循单一职责原则
- 避免过深的组件嵌套
- 定期进行代码审查和重构
- 使用TypeScript的严格模式，避免any类型

## 错误处理
- 遵循 fail-fast 原则，尽早将错误暴露出来
- 不对错误进行不必要的二次包装
- 仅在必要的地方使用 try/catch 结构
- API 请求错误处理应直接抛出详细的错误信息
- 使用工具函数显示错误提示（如src/utils/toast.ts中的showErrorToast）
- 在组件中捕获和处理错误时，保留原始错误信息
- 对于需要全局处理的错误，使用专门的错误边界组件
- 避免静默失败，确保用户能够看到错误消息

## 测试
- 编写单元测试验证关键功能
- 使用Jest和React Testing Library进行组件测试
- 对复杂逻辑编写详细的测试用例
- 保持测试覆盖率在合理水平
- 测试应独立且可重复运行

## 安全性
- 防止XSS攻击，不直接渲染不受信任的内容
- 使用HTTPS进行API通信
- 敏感数据不应存储在本地存储或URL中
- 实施适当的CORS策略
- 使用安全的依赖包，定期更新

## 可访问性（A11y）
- 遵循WCAG 2.1标准
- 所有交互元素应支持键盘导航
- 使用适当的ARIA属性
- 确保颜色对比度符合标准
- 提供替代文本和标签

## 国际化和本地化
- 早期开发阶段：完全使用纯英文实现界面文本语言
- 后期完善阶段：文本内容应使用i18n解决方案
- 日期、时间和数字格式应考虑本地化
- 考虑不同语言文本长度的变化

## 兼容性
- 支持主流现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计适应不同屏幕尺寸
- 优雅降级处理不支持的功能
- 测试关键功能在不同环境下的表现

