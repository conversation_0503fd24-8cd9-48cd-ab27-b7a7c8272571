"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import TopNav from "@/components/top-nav/TopNav";
import { EventPanelContainer } from "@/components/projects";
import { useEventPanelState } from "@/hooks/projects/useEventPanelState";
import { useProjects } from "@/hooks/projects/data/useProjects";
import { Toolbar, ViewType } from "@/components/projects/Toolbar";
import {
  ViewBox,
  ProjectModal,
  CopyProjectModal,
  DeleteConfirmModal,
} from "@/components/projects/Viewlist";
import { motion } from "framer-motion";

export default function ProjectsPage() {
  const router = useRouter();
  // 客户端渲染状态
  const [mounted, setMounted] = useState(false);

  // 使用 useProjects Hook 管理项目数据和状态
  const {
    // 数据状态
    filteredProjects,
    isLoading,
    error,

    // UI 状态
    viewType,
    searchQuery,
    activeFilter,
    isCreateModalOpen,
    isEditModal<PERSON><PERSON>,
    isCopy<PERSON>odal<PERSON><PERSON>,
    isDeleteModalOpen,
    selectedProject,

    // 操作方法
    handleSearch,
    handleFilterChange,
    handleProjectCreate,
    handleProjectEdit,
    handleProjectCopy,
    handleProjectDelete,
    handleFavoriteToggle,
    setViewType,
    openCreateModal,
    openEditModal,
    openCopyModal,
    openDeleteModal,
    closeAllModals,
  } = useProjects();

  // 使用自定义hook管理EventPanel的折叠状态
  const {
    isCollapsed: isEventPanelCollapsed,
    toggleCollapsed: toggleEventPanel,
    isMobile: isEventPanelMobile,
    viewMode: eventPanelViewMode,
  } = useEventPanelState();

  // 检测屏幕尺寸
  const [isMobileScreen, setIsMobileScreen] = useState(false);

  // 标记客户端渲染完成
  useEffect(() => {
    setMounted(true);
  }, []);

  // 在客户端检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobileScreen(window.innerWidth < 560); // sm断点通常是640px
    };

    // 只在客户端运行
    if (typeof window !== "undefined") {
      // 初始检查
      checkScreenSize();

      // 监听窗口大小变化
      window.addEventListener("resize", checkScreenSize);

      // 清理
      return () => window.removeEventListener("resize", checkScreenSize);
    }
  }, []);

  // 处理视图类型变化
  const handleViewTypeChange = (type: ViewType) => {
    setViewType(type);
  };

  // 处理项目点击，跳转到项目详情/文档编辑页面
  const handleProjectClick = (projectId: string) => {
    // 这里可以根据实际情况跳转到相应的页面
    router.push(`/project/${projectId}`);
    // 临时使用固定的 project_id 进行测试
    // const TEMP_PROJECT_ID = '00000002-1234-5678-9abc-def012345678';
    // router.push(`/project/${TEMP_PROJECT_ID}`);
  };

  // 适配器函数：将 projectId 转换为 project 对象
  const handleDeleteProject = (projectId: string) => {
    const project = filteredProjects.find((p) => p.id === projectId);
    if (project) {
      openDeleteModal(project);
    }
  };

  // 适配器函数：将 projectId 和 isFavorite 转换为 project 对象
  const handleToggleFavorite = (projectId: string, isFavorite: boolean) => {
    // 临时测试：如果传入的是固定测试ID，则使用当前选中的项目或第一个项目
    if (projectId === '00000002-1234-5678-9abc-def012345678') {
      // 使用第一个项目作为测试目标
      const targetProject = filteredProjects[0];
      if (targetProject) {
        const updatedProject = { ...targetProject, is_favorite: isFavorite };
        handleFavoriteToggle(updatedProject);
      }
      return;
    }
    
    const project = filteredProjects.find((p) => p.id === projectId);
    if (project) {
      // 创建一个临时的项目对象，包含更新后的 favorite 状态
      const updatedProject = { ...project, is_favorite: isFavorite };
      handleFavoriteToggle(updatedProject);
    }
  };

  // 适配器函数：将复制项目的名称转换为完整的项目数据
  const handleCopyProject = (name: string) => {
    if (selectedProject) {
      handleProjectCopy({
        name: name.trim(),
        description: selectedProject.description,
      });
    }
  };

  // 客户端渲染检查 - 返回加载占位符
  if (!mounted) {
    return (
      <div className="flex flex-col h-screen bg-backgroundDeep">
        <div className="flex-shrink-0 border-b border-secondaryBtn/5">
          {/* 占位符导航栏 */}
          <div className="w-full h-12 px-4 flex items-center justify-between"></div>
        </div>
        <main className="flex-1 px-3 pb-3 overflow-hidden flex">
          {/* 占位符内容区域 */}
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-backgroundDeep">
      {/* 顶部导航 */}
      <div className="flex-shrink-0 border-b border-secondaryBtn/5">
        <TopNav
          isEventPanelCollapsed={isEventPanelCollapsed}
          isEventPanelMobile={isEventPanelMobile}
          eventPanelViewMode={eventPanelViewMode}
          onToggleEventPanel={toggleEventPanel}
        />
      </div>

      {/* 主要内容区域 */}
      <main className="flex-1 pl-3 pr-1 pb-3 overflow-hidden flex">
        {/* 工作区 - 使用 motion.div 实现平滑过渡 */}
        <motion.div
          className="pr-0 flex flex-col w-full"
          // 默认占据全宽
          initial={{ width: "100%" }}
          animate={{
            width:
              isEventPanelCollapsed || eventPanelViewMode !== "desktop"
                ? "100%"
                : `calc(100% - 410px)`,
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          {/* 项目工具栏 - 固定不滚动 */}
          <div className="flex-shrink-0 pt-8 pb-2 xxs:pl-2 xs:pl-2 sm:pl-12 xxs:pr-4 xs:pr-4 sm:pr-14">
            <Toolbar
              activeFilter={activeFilter}
              viewType={viewType}
              searchQuery={searchQuery}
              onFilterChange={handleFilterChange}
              onViewTypeChange={
                isMobileScreen ? undefined : handleViewTypeChange
              }
              onSearchChange={isMobileScreen ? undefined : handleSearch}
              onCreateProject={openCreateModal}
            />
          </div>
          <div className="max-w-full h-full flex flex-col bg-transparent overflow-hidden xxs:pl-2 xs:pl-2 sm:pl-12 pr-0">
            {/* 项目集合 - 可滚动区域 */}
            <div className="flex-grow overflow-hidden">
              <ViewBox
                projects={filteredProjects}
                viewType={viewType}
                onEditProject={openEditModal}
                onDeleteProject={handleDeleteProject}
                onToggleFavorite={handleToggleFavorite}
                onProjectClick={handleProjectClick}
                onCopyProject={openCopyModal}
                isLoading={isLoading}
                error={error}
              />
            </div>
          </div>
        </motion.div>

        {/* 使用 EventPanelContainer 替代原来的条件渲染 */}
        <EventPanelContainer isCollapsed={isEventPanelCollapsed} />

        {/* 项目创建/编辑模态框 */}
        <ProjectModal
          isOpen={isCreateModalOpen || isEditModalOpen}
          onClose={closeAllModals}
          onSave={selectedProject ? handleProjectEdit : handleProjectCreate}
          project={selectedProject || undefined}
          title={selectedProject ? "Edit Project Info" : "Create New Project"}
        />

        {/* 项目复制模态框 */}
        <CopyProjectModal
          isOpen={isCopyModalOpen}
          onClose={closeAllModals}
          onCopy={handleCopyProject}
          project={selectedProject!}
        />

        {/* 项目删除确认模态框 */}
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={closeAllModals}
          onConfirm={handleProjectDelete}
          projectName={selectedProject?.name || ""}
        />
      </main>
    </div>
  );
}
