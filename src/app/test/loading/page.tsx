"use client";

import React from "react";
import LoadingSpinner from "../../../components/ui/loading/LoadingSpinner";

export default function LoadingTestPage() {
  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center p-4">
      <h1 className="mb-8 text-2xl font-bold">加载动画测试页面</h1>

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        <div className="flex flex-col items-center rounded-lg border border-gray-200 p-10 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold">默认样式</h2>
          <LoadingSpinner />
        </div>

        <div className="flex flex-col items-center rounded-lg border border-gray-200 p-10 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold">大尺寸</h2>
          <LoadingSpinner size={64} text="加载中" />
        </div>

        <div className="flex flex-col items-center rounded-lg border border-gray-200 p-10 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold">自定义动画颜色</h2>
          <LoadingSpinner animColor="#FF5733" text="自定义动画" />
        </div>

        <div className="flex flex-col items-center rounded-lg border border-gray-200 p-10 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold">自定义文字颜色</h2>
          <LoadingSpinner textColor="text-blue-600" text="自定义文字" />
        </div>

        <div className="flex flex-col items-center rounded-lg border border-gray-200 p-10 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold">全部自定义</h2>
          <LoadingSpinner
            size={48}
            animColor="#10B981"
            textColor="text-emerald-600"
            text="全部自定义"
          />
        </div>

        <div className="flex flex-col items-center rounded-lg border border-gray-200 p-10 shadow-sm dark bg-gray-800">
          <h2 className="mb-4 text-xl font-semibold text-white">暗色模式</h2>
          <LoadingSpinner
            animColor="#ffffff"
            textColor="text-white"
            text="暗色模式"
          />
        </div>
      </div>

      <div className="mt-8">
        <a href="/" className="text-blue-500 hover:underline">
          返回首页
        </a>
      </div>
    </div>
  );
}
