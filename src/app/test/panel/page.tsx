"use client";

import AIPanel from "../../../components/ai-panel/AI-panel";
import Chat from "../../../components/ai-panel/panels/ChatPanel";
import Docs from "../../../components/ai-panel/panels/DocsPanel";
import Settings from "../../../components/ai-panel/panels/SettingsPanel";
import Todo from "../../../components/ai-panel/panels/TodoPanel";
import { useState } from "react";

export default function PanelTestPage() {
  // 简化版本：使用状态管理面板折叠
  const [isContentVisible, setIsContentVisible] = useState(true);


  return (
    <div className="h-screen pr-2 pl-4 py-4 bg-backgroundDeep">
      <AIPanel 
        isContentVisible={isContentVisible}
        defaultTab="chat"
      >
        {/* 注册面板 */}
        <Chat />
        <Docs />
        <Settings />
        <Todo />
      </AIPanel>
    </div>
  );
}
