@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  overflow: hidden;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: theme("fontFamily.sans");
}

/* 去除按钮焦点状态边框 */
button:focus,
[role="button"]:focus,
a:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 自定义组件样式 */
@layer components {
  .card {
    @apply p-6 border border-gray-200 rounded-lg transition-all duration-300 bg-white relative overflow-hidden;
  }

  .card-title {
    @apply text-xl font-bold mb-4 text-indigo-700 flex items-center;
  }
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* 加载动画波浪效果 - 全局定义确保在页面加载时就能使用 */
@keyframes wave-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.5);
  }
}

.wave-dot-0-0 {
  animation: wave-pulse 1.2s 0s infinite ease-in-out;
}
.wave-dot-0-1 {
  animation: wave-pulse 1.2s 0.1s infinite ease-in-out;
}
.wave-dot-1-0 {
  animation: wave-pulse 1.2s 0.1s infinite ease-in-out;
}
.wave-dot-0-2 {
  animation: wave-pulse 1.2s 0.2s infinite ease-in-out;
}
.wave-dot-1-1 {
  animation: wave-pulse 1.2s 0.2s infinite ease-in-out;
}
.wave-dot-2-0 {
  animation: wave-pulse 1.2s 0.2s infinite ease-in-out;
}
.wave-dot-1-2 {
  animation: wave-pulse 1.2s 0.3s infinite ease-in-out;
}
.wave-dot-2-1 {
  animation: wave-pulse 1.2s 0.3s infinite ease-in-out;
}
.wave-dot-2-2 {
  animation: wave-pulse 1.2s 0.4s infinite ease-in-out;
}

@layer utilities {
  .scrollbar-hide {
    /* Firefox */
    scrollbar-width: none;
    /* IE and Edge */
    -ms-overflow-style: none;
  }
  /* WebKit (Chrome, Safari, newer Edge) */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .custom-scrollbar {
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #bdbdbd #f5f5f5; /* Firefox */
  }
  .dark .custom-scrollbar {
    scrollbar-color: #444 #222;
  }
  .scrollbar-hide {
    /* Firefox */
    scrollbar-width: none;
    /* IE and Edge */
    -ms-overflow-style: none;
  }
  .transition-height {
    transition-property: height;
  }
}

/* Webkit 浏览器全局滚动条样式（用于 Editor） */
::-webkit-scrollbar {
  width: 11px;
  height: 11px;
  background: #f5f5f500;
}
::-webkit-scrollbar-thumb {
  background: #d3d3d350;
  border-radius: 6px;
  border: 3px solid #fffefd;
}
.dark ::-webkit-scrollbar {
  background: #f5f5f500;
}
.dark ::-webkit-scrollbar-thumb {
  background: #4444445f;
  border: 3px solid #201f1e;
}

/* 细窄滚动条的Webkit样式 */
.slim-scrollbar::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
  background: transparent !important;
}

.slim-scrollbar::-webkit-scrollbar-track {
  background: transparent !important;
}

.slim-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(131, 123, 119, 0.1) !important;
  border-radius: 4px !important;
  border: none !important;
}

.dark .slim-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(130, 125, 119, 0.2) !important;
}

/* 常规滚动条的Webkit样式 */
.normal-scrollbar::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background: transparent !important;
}

.normal-scrollbar::-webkit-scrollbar-track {
  background: transparent !important;
}

.normal-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(131, 123, 119, 0.1) !important;
  border-radius: 6px !important;
  border: none !important;
}

.dark .normal-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(130, 125, 119, 0.1) !important;
}

/* 自动隐藏滚动条样式 */
.auto-hide-scrollbar::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background: transparent !important;
  opacity: 0;
  transition: opacity 0.3s;
}

.auto-hide-scrollbar::-webkit-scrollbar-track {
  background: transparent !important;
}

.auto-hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(131, 123, 119, 0.1) !important;
  border-radius: 6px !important;
  border: none !important;
  opacity: 0;
}

.auto-hide-scrollbar.scrolling::-webkit-scrollbar-thumb {
  opacity: 1;
}

.dark .auto-hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(130, 125, 119, 0.1) !important;
}

/* Firefox 自动隐藏滚动条 */
.auto-hide-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s;
}

.auto-hide-scrollbar.scrolling {
  scrollbar-color: rgba(131, 123, 119, 0.1) transparent;
}

.dark .auto-hide-scrollbar.scrolling {
  scrollbar-color: rgba(130, 125, 119, 0.1) transparent;
}

.scrollbar-hide {
  /* Firefox */
  scrollbar-width: none;
  /* IE and Edge */
  -ms-overflow-style: none;
}
/* WebKit (Chrome, Safari, newer Edge) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Chat部分使用原生滚动条样式，覆盖全局滚动条定义 */
.use-native-scrollbar {
  scrollbar-width: thin !important; /* Firefox */
  -ms-overflow-style: scrollbar !important; /* IE and Edge */
  overflow: auto !important;
}

.use-native-scrollbar::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
  display: block !important;
  background: transparent !important;
}

.use-native-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px !important;
  border: none !important;
}

.dark .use-native-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2) !important;
}

.use-native-scrollbar * ::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
  display: block !important;
  background: transparent !important;
}

.use-native-scrollbar * ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px !important;
  border: none !important;
}

.dark .use-native-scrollbar * ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2) !important;
}
