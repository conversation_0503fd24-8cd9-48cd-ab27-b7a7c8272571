"use client";

import Link from "next/link";
import LoginButton from "@/components/ui/LoginButton";
import { showSuccessToast } from "@/utils/toast";

export default function HomePage() {
  return (
    <main className="min-h-screen flex flex-col items-center justify-center bg-backgroundDeep p-4">
      <div className="max-w-3xl w-full text-center space-y-6">
        <h1 className="text-4xl font-bold text-foreground">Welcome to Quote</h1>
        <p className="text-md text-secondaryBtn/60 max-w-lg mx-auto">
          A modern legal document editor, integrated with powerful AI agents,
          helps you handle legal documents more efficiently.
        </p>
        <div className="pt-6 flex gap-4 justify-center">
          <Link
            href="/projects"
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-secondaryBtn-text bg-secondaryBtn hover:bg-secondaryBtn-hover transition-colors duration-200"
          >
            Enter Quote
          </Link>
          <LoginButton variant="default" size="md" />
          <button
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-secondaryBtn-text bg-secondaryBtn hover:bg-secondaryBtn-hover transition-colors duration-200"
            onClick={() => showSuccessToast("Hello, world!")}
          >
            toast test
          </button>
        </div>
      </div>
    </main>
  );
}
