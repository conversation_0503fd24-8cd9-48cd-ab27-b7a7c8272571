/**
 * API 代理路由
 * 将客户端请求代理到真实的后端 API，隐藏真实的后端地址
 */

import { NextRequest, NextResponse } from "next/server";
import logger from "@/utils/logger";

const systemLogger = logger.createPrefixed("System");

// 获取真实的后端 API 地址（仅在服务端可用）
const getBackendApiUrl = (): string => {
  const baseUrl = process.env.API_BASE_URL;
  if (!baseUrl) {
    throw new Error("环境变量 API_BASE_URL 未设置");
  }
  return baseUrl;
};

// 获取API路径前缀
const getApiPathPrefix = (): string => {
  const prefix = process.env.API_PATH_PREFIX;
  if (!prefix) {
    throw new Error("环境变量 API_PATH_PREFIX 未设置");
  }
  return prefix;
};

// 处理所有 HTTP 方法
async function handleRequest(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const backendUrl = getBackendApiUrl();
    const apiPathPrefix = getApiPathPrefix();

    // 构建目标 URL
    const path = params.path.join("/");
    const searchParams = request.nextUrl.searchParams.toString();

    // 系统端点（set_user_id, health）需要直接访问根路径，不加版本前缀
    const isSystemEndpoint = ["set_user_id", "health"].includes(path);
    let targetUrl: string;

    if (isSystemEndpoint) {
      // 系统端点：直接使用基础URL，不加版本前缀
      targetUrl = `${backendUrl}${path}${
        searchParams ? `?${searchParams}` : ""
      }`;
      systemLogger.log(`系统端点 ${path} -> ${targetUrl}`);
    } else {
      // 业务端点：添加版本前缀
      targetUrl = `${backendUrl}${apiPathPrefix}/${path}${
        searchParams ? `?${searchParams}` : ""
      }`;
      systemLogger.log(`业务端点 ${path} -> ${targetUrl}`);
    }

    // 准备请求头，移除一些不需要的头部
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 过滤掉一些不需要转发的头部
      if (
        ![
          "host",
          "connection",
          "x-forwarded-for",
          "x-forwarded-proto",
        ].includes(key.toLowerCase())
      ) {
        headers.set(key, value);
      }
    });

    // 准备请求体
    let body: string | FormData | null = null;
    if (request.method !== "GET" && request.method !== "HEAD") {
      const contentType = request.headers.get("content-type");
      if (contentType?.includes("application/json")) {
        body = await request.text();
      } else if (contentType?.includes("multipart/form-data")) {
        body = await request.formData();
      } else {
        body = await request.text();
      }
    }

    // 发送请求到后端
    const response = await fetch(targetUrl, {
      method: request.method,
      headers,
      body,
    });

    // 获取响应数据 - 正确处理压缩响应
    const contentEncoding = response.headers.get("content-encoding");
    const contentType = response.headers.get("content-type");

    systemLogger.log(`响应编码: ${contentEncoding}, 内容类型: ${contentType}`);

    // fetch API 会自动处理 gzip 解压缩，我们直接使用 text() 即可
    // 不需要特殊处理压缩响应
    const responseData = await response.text();
    systemLogger.log(`响应数据长度: ${responseData.length} 字符`);

    // 准备响应头
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      const lowerKey = key.toLowerCase();
      // 过滤掉压缩相关的头部，因为我们已经解压了数据
      if (
        !["connection", "transfer-encoding", "content-encoding"].includes(
          lowerKey
        )
      ) {
        responseHeaders.set(key, value);
      }
    });

    // 对于已解压的响应，移除 content-encoding 头部
    if (contentEncoding) {
      systemLogger.log(`移除 content-encoding 头部: ${contentEncoding}`);
    }

    // 返回响应 - 特殊处理204状态码
    if (response.status === 204) {
      // 204 No Content 不应该有响应体
      return new NextResponse(null, {
        status: 204,
        statusText: response.statusText,
        headers: responseHeaders,
      });
    }

    return new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error("API 代理错误:", error);

    return NextResponse.json(
      {
        error: "Internal Server Error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// 导出所有 HTTP 方法处理器
export const GET = handleRequest;
export const POST = handleRequest;
export const PUT = handleRequest;
export const PATCH = handleRequest;
export const DELETE = handleRequest;
export const HEAD = handleRequest;
export const OPTIONS = handleRequest;
