import { Metadata } from "next";

// 项目ID验证函数暂时注释掉，避免SSR中的认证问题
// import NotFoundUI from "@/components/ui/NotFoundUI";
// import { projectService } from "@/lib/api/services/projectService";

// async function isValidProjectId(projectId: string): Promise<boolean> {
//   try {
//     // 获取用户的所有项目列表
//     const projects = await projectService.getProjects();
//     // 检查项目ID是否在列表中存在
//     return projects.some((project) => project.id === projectId);
//   } catch (error) {
//     // 如果获取项目列表失败，返回false
//     return false;
//   }
// }

// 这个函数会在构建时和请求时都执行
// 它可以根据当前路由参数动态生成元数据
export async function generateMetadata({
  params,
}: {
  params: { projectId: string };
}): Promise<Metadata> {
  // 由于API不提供单个项目查询端点，使用通用标题
  return {
    title: "Project Editor | Quote",
    description: "Editor for your legal documents",
  };
}

export default function ProjectLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { projectId: string };
}) {
  // 暂时移除服务端项目验证，避免SSR中的认证问题
  // 项目验证将在客户端组件中进行
  // TODO: 考虑在客户端组件中添加项目验证逻辑

  return children;
}
