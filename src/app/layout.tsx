import "./globals.css";
import "framesound-ui/dist/framesound-ui.css";
import "@/styles/_variables.scss";
import Providers from "./providers";
import { Metadata } from "next";

// 默认元数据
export const metadata: Metadata = {
  title: "Quote | AI 驱动法律知识引擎",
  description:
    "Quote 是一款 AI 驱动的法律知识引擎，高效编辑、共享知识，赋能精准决策。",
};

// Cache busting version - update this when favicon changes
const FAVICON_VERSION = "v=1.0.0";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" spellCheck="false" suppressHydrationWarning>
      <head>
        <link
          rel="apple-touch-icon"
          href={`/favicon-imgs/apple-icon.png?${FAVICON_VERSION}`}
        />
        <link
          rel="icon"
          href={`/favicon-imgs/favicon.ico?${FAVICON_VERSION}`}
          type="image/x-icon"
        />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600;700;800;900&display=swap"
        />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700;900&display=auto"
        />
      </head>
      <body className="text-foreground bg-background overflow-hidden">
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
