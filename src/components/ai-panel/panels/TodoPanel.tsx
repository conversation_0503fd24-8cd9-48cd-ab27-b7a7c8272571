"use client";

import * as React from "react";
import { useState } from "react";
import { ListTodo, Save } from "lucide-react";
import { usePanelActions } from "../context/PanelContext";
import PanelHeader from "../components/PanelHeader";
import Panel from "../components/Panel";

/**
 * 工具面板内容组件
 */
export function ToolsPanelContent(): React.ReactElement {
  const [tools, setTools] = useState({
    theme: "system",
    fontSize: "medium",
    autoSave: true,
    notifications: true,
    compactMode: false,
  });

  // 注册面板操作
  const { executePanelAction } = usePanelActions({
    SAVE_SETTINGS: () => {
      console.log("保存设置", tools);
      // 这里可以添加保存设置的逻辑
    },
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;
    const checked =
      type === "checkbox" ? (e.target as HTMLInputElement).checked : undefined;

    setTools((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  return (
    <div className="flex-1 overflow-auto p-4">
      <form className="space-y-6">
        {/* 外观设置 */}
        <section>
          <h3 className="text-lg font-medium mb-4">外观</h3>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="theme"
                className="block text-sm font-medium text-foreground mb-1"
              >
                主题
              </label>
              <select
                id="theme"
                name="theme"
                value={tools.theme}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-background border border-input rounded-md"
              >
                <option value="system">跟随系统</option>
                <option value="light">浅色</option>
                <option value="dark">深色</option>
              </select>
            </div>

            <div>
              <label
                htmlFor="fontSize"
                className="block text-sm font-medium text-foreground mb-1"
              >
                字体大小
              </label>
              <select
                id="fontSize"
                name="fontSize"
                value={tools.fontSize}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-background border border-input rounded-md"
              >
                <option value="small">小</option>
                <option value="medium">中</option>
                <option value="large">大</option>
              </select>
            </div>
          </div>
        </section>

        {/* 编辑器设置 */}
        <section>
          <h3 className="text-lg font-medium mb-4">编辑器</h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoSave"
                name="autoSave"
                checked={tools.autoSave}
                onChange={handleChange}
                className="h-4 w-4 text-primary border-gray-300 rounded"
              />
              <label
                htmlFor="autoSave"
                className="ml-2 block text-sm text-foreground"
              >
                自动保存
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="compactMode"
                name="compactMode"
                checked={tools.compactMode}
                onChange={handleChange}
                className="h-4 w-4 text-primary border-gray-300 rounded"
              />
              <label
                htmlFor="compactMode"
                className="ml-2 block text-sm text-foreground"
              >
                紧凑模式
              </label>
            </div>
          </div>
        </section>

        {/* 通知设置 */}
        <section>
          <h3 className="text-lg font-medium mb-4">通知</h3>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="notifications"
              name="notifications"
              checked={tools.notifications}
              onChange={handleChange}
              className="h-4 w-4 text-primary border-gray-300 rounded"
            />
            <label
              htmlFor="notifications"
              className="ml-2 block text-sm text-foreground"
            >
              启用通知
            </label>
          </div>
        </section>
      </form>
    </div>
  );
}

/**
 * 面板Header组件
 */
export function ToolsPanelHeader(): React.ReactElement {
  const { executePanelAction } = usePanelActions({});

  return (
    <PanelHeader>
      <div className="flex items-center">
        <h2 className="text-md font-semibold text-foreground">Todo</h2>
      </div>
      <div className="flex items-center space-x-1.5">
        <button
          className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 text-secondaryBtn/80 hover:text-secondaryBtn"
          onClick={() => executePanelAction("SAVE_SETTINGS")}
          aria-label="Save Settings"
          title="Save Settings"
        >
          <Save size={14} />
        </button>
      </div>
    </PanelHeader>
  );
}

/* ==================== 面板导出配置 ==================== */

/**
 * 导航图标
 */
export function ToolsPanelIcon(): React.ReactElement {
  return <ListTodo size={16} />;
}

/**
 * 面板导出
 * 用于在页面中以组件方式注册面板
 */
export default function Tools({ projectId }: { projectId?: string }) {
  return (
    <Panel
      id="todo"
      header={<ToolsPanelHeader />}
      icon={<ToolsPanelIcon />}
      label="Todo"
    >
      <ToolsPanelContent />
    </Panel>
  );
}
