"use client";

import * as React from "react";
import { FileText, ExternalLink } from "lucide-react";
import { registerGlobalPanel } from "../context/PanelContext";
import PanelHeader from "../components/PanelHeader";
import Panel from "../components/Panel";

/**
 * 文档面板内容组件
 */
export function DocsPanelContent({
  projectId,
}: {
  projectId?: string;
}): React.ReactElement {
  return (
    <div className="flex-1 overflow-auto p-4">
      <div className="space-y-6">
        {projectId && (
          <section>
            <h2 className="text-lg font-semibold mb-3">项目文档</h2>
            <div className="space-y-2">
              <DocLink title={`项目 ${projectId} 的文档`} />
            </div>
          </section>
        )}
        <section>
          <h2 className="text-lg font-semibold mb-3">快速入门</h2>
          <div className="space-y-2">
            <DocLink title="安装指南" />
            <DocLink title="基础教程" />
            <DocLink title="常见问题" />
          </div>
        </section>

        <section>
          <h2 className="text-lg font-semibold mb-3">API 文档</h2>
          <div className="space-y-2">
            <DocLink title="核心 API" />
            <DocLink title="插件开发" />
            <DocLink title="高级功能" />
          </div>
        </section>

        <section>
          <h2 className="text-lg font-semibold mb-3">示例项目</h2>
          <div className="space-y-2">
            <DocLink title="基础示例" />
            <DocLink title="高级示例" />
          </div>
        </section>
      </div>
    </div>
  );
}

// 文档链接组件
function DocLink({ title }: { title: string }): React.ReactElement {
  return (
    <a
      href="#"
      className="block p-3 rounded-lg border border-border hover:bg-muted transition-colors"
    >
      <div className="flex items-center">
        <FileText size={14} className="mr-2 text-primary" />
        <span>{title}</span>
      </div>
    </a>
  );
}

/**
 * 面板Header组件
 */
export function DocsPanelHeader(): React.ReactElement {
  return (
    <PanelHeader>
      <div className="flex items-center">
        <h2 className="text-md font-semibold text-foreground">Documentation</h2>
      </div>
      <div className="flex items-center space-x-1.5">
        <a
          href="https://example.com/docs"
          target="_blank"
          rel="noopener noreferrer"
          className="p-1.5 rounded-md hover:bg-secondaryBtn/5 text-secondaryBtn/80 hover:text-secondaryBtn"
          aria-label="Open external documentation"
          title="Open external documentation"
        >
          <ExternalLink size={14} />
        </a>
      </div>
    </PanelHeader>
  );
}

/* ==================== 面板导出配置 ==================== */

/**
 * 导航图标
 */
export function DocsPanelIcon(): React.ReactElement {
  return <FileText size={16} />;
}

/**
 * 面板导出
 * 用于在页面中以组件方式注册面板
 */
export default function Docs({ projectId }: { projectId?: string }) {
  return (
    <Panel
      id="docs"
      header={<DocsPanelHeader />}
      icon={<DocsPanelIcon />}
      label="Documentation"
    >
      <DocsPanelContent projectId={projectId} />
    </Panel>
  );
}
