"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { MessageSquare, Plus, Trash2 } from "lucide-react";
import { usePanelActions } from "../context/PanelContext";
import PanelHeader from "../components/PanelHeader";
import ChatSkeleton from "../components/chat/ChatSkeleton";
import Panel from "../components/Panel";
import PanelTabSwitcher from "../components/PanelTabSwitcher";

// 聊天面板标签类型
type ChatTabType = "copilot" | "chat";

// 聊天面板标签选项
const CHAT_TABS = [
  { id: "copilot" as const, label: "Copilot" },
  { id: "chat" as const, label: "Chat" },
];

// 使用两个全局变量分别跟踪两种聊天组件是否已经加载过
let hasEnhancedChatLoaded = false;
let hasNormalChatLoaded = false;

// 动态导入 EnhancedChat 组件
const EnhancedChatPageContent = dynamic(
  () => import("../components/chat/EnhancedChat"),
  {
    ssr: false,
    loading: () => null, // 不使用默认loading，我们自己控制加载状态
  }
);

const NormalChatPageContent = dynamic(
  () => import("../components/chat/NormalChat"),
  {
    ssr: false,
    loading: () => null, // 不使用默认loading，我们自己控制加载状态
  }
);

/**
 * 聊天面板内容组件
 */
export function ChatPanelContent({
  activeTab,
  projectId,
}: {
  activeTab: ChatTabType;
  projectId?: string;
}): React.ReactElement {
  const [isEnhancedChatLoaded, setIsEnhancedChatLoaded] = useState(
    hasEnhancedChatLoaded
  );
  const [isNormalChatLoaded, setIsNormalChatLoaded] =
    useState(hasNormalChatLoaded);
  const [showSkeleton, setShowSkeleton] = useState(
    !hasEnhancedChatLoaded || !hasNormalChatLoaded
  );

  // 注册面板操作
  const { executePanelAction } = usePanelActions({
    NEW_CHAT: () => {
      console.log("创建新聊天");
      // 这里可以添加创建新聊天的逻辑
    },
    CLEAR_CHAT: () => {
      console.log("清空聊天");
      // 这里可以添加清空聊天的逻辑
    },
  });

  // 组件加载状态控制
  useEffect(() => {
    if (activeTab === "copilot" && !hasEnhancedChatLoaded) {
      // EnhancedChat 组件挂载后，设置一个短暂的延迟来模拟加载
      const loadTimer = setTimeout(() => {
        setIsEnhancedChatLoaded(true);
        hasEnhancedChatLoaded = true;
      }, 500); // 给动态导入一些时间加载

      return () => clearTimeout(loadTimer);
    }

    if (activeTab === "chat" && !hasNormalChatLoaded) {
      // NormalChat 组件挂载后，设置一个短暂的延迟来模拟加载
      const loadTimer = setTimeout(() => {
        setIsNormalChatLoaded(true);
        hasNormalChatLoaded = true;
      }, 500); // 给动态导入一些时间加载

      return () => clearTimeout(loadTimer);
    }
  }, [activeTab]);

  // 处理骨架屏淡出
  useEffect(() => {
    const isCurrentTabLoaded =
      activeTab === "copilot" ? isEnhancedChatLoaded : isNormalChatLoaded;

    if (isCurrentTabLoaded && showSkeleton) {
      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isEnhancedChatLoaded, isNormalChatLoaded, showSkeleton, activeTab]);

  // 切换标签时重新显示骨架屏
  useEffect(() => {
    const isCurrentTabLoaded =
      activeTab === "copilot" ? hasEnhancedChatLoaded : hasNormalChatLoaded;
    if (!isCurrentTabLoaded) {
      setShowSkeleton(true);
    }
  }, [activeTab]);

  const isCurrentTabLoaded =
    activeTab === "copilot" ? isEnhancedChatLoaded : isNormalChatLoaded;

  return (
    <div className="flex-1 overflow-hidden relative">
      {/* 骨架屏 - 仅在当前标签页首次加载时显示 */}
      {showSkeleton && (
        <div
          className={`absolute inset-0 z-10 transition-opacity duration-500 ease-in-out ${
            !isCurrentTabLoaded ? "opacity-100" : "opacity-0"
          }`}
        >
          <ChatSkeleton />
        </div>
      )}

      {/* 实际内容 - 带有淡入效果 */}
      <div
        className={`h-full w-full transition-opacity duration-500 ease-in-out ${
          isCurrentTabLoaded ? "opacity-100" : "opacity-0"
        }`}
      >
        {activeTab === "copilot" ? (
          <EnhancedChatPageContent />
        ) : (
          <NormalChatPageContent />
        )}
      </div>
    </div>
  );
}

/**
 * 面板Header组件
 */
export function ChatPanelHeader({
  activeTab,
  setActiveTab,
}: {
  activeTab: ChatTabType;
  setActiveTab: (tab: ChatTabType) => void;
}): React.ReactElement {
  const { executePanelAction } = usePanelActions({});

  return (
    <PanelHeader>
      <PanelTabSwitcher
        tabs={CHAT_TABS}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
      <div className="flex items-center space-x-1.5">
        <button
          className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 text-secondaryBtn/80 hover:text-secondaryBtn"
          onClick={() => executePanelAction("NEW_CHAT")}
          aria-label="New Chat"
          title="New Chat"
        >
          <Plus size={14} />
        </button>
        <button
          className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 text-secondaryBtn/80 hover:text-secondaryBtn"
          onClick={() => executePanelAction("CLEAR_CHAT")}
          aria-label="Clear Chat"
          title="Clear Chat"
        >
          <Trash2 size={14} />
        </button>
      </div>
    </PanelHeader>
  );
}

/* ==================== 面板导出配置 ==================== */

/**
 * 导航图标
 */
export function ChatPanelIcon(): React.ReactElement {
  return <MessageSquare size={16} />;
}

/**
 * 面板导出
 * 用于在页面中以组件方式注册面板
 */
export default function Chat({ projectId }: { projectId?: string }) {
  const [activeTab, setActiveTab] = useState<ChatTabType>("copilot");

  return (
    <Panel
      id="chat"
      header={
        <ChatPanelHeader activeTab={activeTab} setActiveTab={setActiveTab} />
      }
      icon={<ChatPanelIcon />}
      label="Chat"
    >
      <ChatPanelContent activeTab={activeTab} projectId={projectId} />
    </Panel>
  );
}
