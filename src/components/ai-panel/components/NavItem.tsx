"use client";

import * as React from "react";

interface NavItemProps {
  id: string;
  isActive: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  isCollapsed?: boolean;
}

/**
 * 面板导航项组件
 * 用于在侧边导航栏中显示切换不同面板的按钮
 * 样式与原 AIPanelNav 保持一致
 */
export function NavItem({
  id,
  isActive,
  onClick,
  icon,
  label,
  isCollapsed = false,
}: NavItemProps): React.ReactElement {
  return (
    <button
      className={`w-8 h-8 mb-2 flex items-center justify-center rounded-lg transition-all duration-300 ${
        isActive && !isCollapsed
          ? "bg-primary/10 text-primary hover:bg-primary/15"
          : isCollapsed
          ? "text-secondaryBtn/80 hover:bg-secondaryBtn/5 hover:text-secondaryBtn"
          : "text-muted-foreground hover:bg-secondaryBtn/5 hover:text-secondaryBtn"
      }`}
      onClick={onClick}
      aria-label={label}
      title={label}
    >
      <span className="sr-only">{label}</span>
      {icon}
    </button>
  );
}

export default NavItem;
