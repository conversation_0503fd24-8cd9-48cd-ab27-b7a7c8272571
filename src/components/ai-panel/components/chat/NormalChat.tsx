"use client";

import React, { useState, useCallback } from "react";
import dynamic from "next/dynamic";
import { ExternalApplyProvider } from "framesound-ui";
import { RealChatService } from "../../../../services/chat";
import {
  showToast,
  showSuccessToast,
  showErrorToast,
} from "../../../../utils/toast";

// 创建一个NoSSR组件包装器
const NoSSR = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  return mounted ? children : null;
};

// 动态导入 EnhancedChatCore 组件
const EnhancedChatCoreComponent = dynamic(
  () => import("framesound-ui").then((mod) => mod.EnhancedChatCore),
  { ssr: false }
);

export default function NormalChat() {
  const [title, setTitle] = useState("Standard Chat");
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [externalApplyStatus, setExternalApplyStatus] = useState<
    "idle" | "applying" | "awaitingUserConfirm" | "confirmed" | "cancelled"
  >("idle");
  const [activeBlockIds, setActiveBlockIds] = useState<Set<string>>(new Set());
  const [externalApplyError, setExternalApplyError] = useState<string>();
  const chatService = new RealChatService();

  // 处理外部应用请求
  const handleRequestApply = useCallback((content: string, blockId: string) => {
    console.log("Requesting to apply content:", content);
    setActiveBlockIds((prev) => {
      const next = new Set(prev);
      next.add(blockId);
      return next;
    });
    // 模拟异步操作
    setExternalApplyStatus("applying");
    setTimeout(() => {
      setExternalApplyStatus("awaitingUserConfirm");
      showToast("Please confirm whether to apply changes");
    }, 1000);
  }, []);

  // 处理确认应用
  const handleExternalConfirm = useCallback((blockId: string) => {
    setActiveBlockIds((prev) => {
      const next = new Set(prev);
      next.delete(blockId);
      // 如果没有更多激活的块，重置状态
      if (next.size === 0) {
        setExternalApplyStatus("confirmed");
        showSuccessToast("All changes confirmed");
        setExternalApplyStatus("idle");
      }
      return next;
    });
    showSuccessToast(`Confirmed changes for block ${blockId}`);
  }, []);

  // 处理取消应用
  const handleExternalCancel = useCallback((blockId: string) => {
    setActiveBlockIds((prev) => {
      const next = new Set(prev);
      next.delete(blockId);
      // 如果没有更多激活的块，重置状态
      if (next.size === 0) {
        setExternalApplyStatus("cancelled");
        showToast("All changes cancelled");
        setExternalApplyStatus("idle");
      }
      return next;
    });
    showToast(`Cancelled changes for block ${blockId}`);
  }, []);

  // 处理应用开始
  const handleApplyStart = useCallback(() => {
    showToast("Starting to apply changes...");
  }, []);

  // 处理应用完成
  const handleApplyComplete = useCallback(
    (success: boolean, error?: string) => {
      if (success) {
        showSuccessToast("Changes applied successfully");
      } else {
        setExternalApplyError(error);
        showErrorToast(`Application failed: ${error}`);
      }
    },
    []
  );

  const handleStreamStart = useCallback(() => {
    // Stream start handler
  }, []);

  const handleStreamEnd = useCallback(() => {
    // Stream end handler
  }, []);

  const handleStreamError = useCallback((error: Error) => {
    console.error("Stream error:", error);
    showErrorToast(`Error: ${error.message}`);
  }, []);

  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
    showToast(`Title updated: ${newTitle}`);
  }, []);

  const handleBookmarkChange = useCallback((bookmarked: boolean) => {
    setIsBookmarked(bookmarked);
    showToast(bookmarked ? "Bookmarked" : "Unbookmarked");
  }, []);

  return (
    <div className="h-full w-full flex flex-col overflow-hidden">
      <NoSSR>
        <ExternalApplyProvider
          value={{
            status: externalApplyStatus,
            activeBlockIds,
            onRequestApply: handleRequestApply,
            onConfirm: handleExternalConfirm,
            onCancel: handleExternalCancel,
            onApplyStart: handleApplyStart,
            onApplyComplete: handleApplyComplete,
            error: externalApplyError,
          }}
        >
          <div className="flex-1 h-full w-full overflow-hidden">
            <EnhancedChatCoreComponent
              chatService={chatService}
              className="h-full use-native-scrollbar overflow-auto"
              showEventBlocks={false}
              defaultBlockExpanded={true}
              isChatNavVisible={false}
              title={title}
              isBookmarked={isBookmarked}
              isSidebar={true}
              onTitleChange={handleTitleChange}
              onBookmarkChange={handleBookmarkChange}
              onStreamStart={handleStreamStart}
              onStreamEnd={handleStreamEnd}
              onStreamError={handleStreamError}
              inputPlaceholder="Enter your message..."
              autoSize={{ minRows: 1, maxRows: 4 }}
              uploadAreaVariant="compact"
              emptyComponent={
                <div className="flex h-full flex-col items-center justify-center p-8 text-center">
                  <h3 className="mb-2 text-xl font-medium">Standard Chat</h3>
                  <p className="mb-4 text-gray-500">
                    This is a simplified chat interface with basic features.
                  </p>
                  <p className="mt-4 text-sm text-gray-500">
                    Please enter your question to start a conversation
                  </p>
                </div>
              }
            />
          </div>
        </ExternalApplyProvider>
      </NoSSR>
    </div>
  );
}
