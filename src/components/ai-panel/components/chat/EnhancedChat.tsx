"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import dynamic from "next/dynamic";
import {
  type FileInfo,
  type FolderInfo,
  WorkflowOption,
  ExternalApplyProvider,
} from "framesound-ui";
import { RealChatService } from "../../../../services/chat";
import {
  showToast,
  showSuccessToast,
  showErrorToast,
  showInfoToast,
} from "../../../../utils/toast";
import Image from "next/image";
import LoadingSpinner from "../../../ui/loading/LoadingSpinner";

// 创建一个NoSSR组件包装器
const NoSSR = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  return mounted ? children : null;
};

// 动态导入 EnhancedChatCore 组件
const EnhancedChatCoreComponent = dynamic(
  () => import("framesound-ui").then((mod) => mod.EnhancedChatCore),
  { ssr: false }
);

// 模拟的上下文文件和文件夹数据
const mockContextFiles: FileInfo[] = [
  {
    id: "1",
    name: "证据11-2024年零售用户结算单.pdf",
    path: "证据/结算单/证据11-2024年零售用户结算单.pdf",
    type: "pdf",
    icon: "FileText",
  },
  {
    id: "2",
    name: "证据8-2024年零售用户结算单.pdf",
    path: "证据/结算单/证据8-2021年零售用户结算单.pdf",
    type: "pdf",
    icon: "FileText",
  },
  {
    id: "3",
    name: "证据6-2024年零售用户结算单测试.docx",
    path: "证据/证据6-2021年零售用户结算单测试.docx",
    type: "word",
    icon: "FileText",
  },
  {
    id: "4",
    name: "README.md",
    path: "docs/README.md",
    type: "markdown",
    icon: "FileText",
  },
  {
    id: "5",
    name: "tsconfig.json",
    path: "src/tsconfig.json",
    type: "json",
    icon: "FileText",
  },
  {
    id: "6",
    name: "index.ts",
    path: "src/components/index.ts",
    type: "typescript",
    icon: "FileText",
  },
  {
    id: "7",
    name: "package.json",
    path: "package.json",
    type: "json",
    icon: "FileText",
  },
  {
    id: "8",
    name: "jest.config.js",
    path: "test/jest.config.js",
    type: "javascript",
    icon: "FileText",
  },
  {
    id: "9",
    name: "诉状1-2024年零售用户结算单.pdf",
    path: "诉状/法庭诉状/法庭诉状2/法庭纪要3/诉状1-2021年零售用户结算单.pdf",
    type: "pdf",
    icon: "FileText",
  },
  {
    id: "10",
    name: "案情说明1-2024年零售用户结算单.pdf",
    path: "案情说明/案情说明1-2021年零售用户结算单.pdf",
    type: "pdf",
    icon: "FileText",
  },
  {
    id: "11",
    name: "案情说明2-2024年零售用户结算单.pdf",
    path: "案情说明/案情说明2-2021年零售用户结算单.pdf",
    type: "pdf",
    icon: "FileText",
  },
  {
    id: "12",
    name: "案情说明4-2024年零售用户结算单.pdf",
    path: "Folders/案情说明4-2021年零售用户结算单.pdf",
    type: "pdf",
    icon: "FileText",
  },
];

export default function EnhancedChatPageContent() {
  const [selectedWorkflow, setSelectedWorkflow] = useState("general");
  const [title, setTitle] = useState("AI 助手对话");
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);
  const [externalApplyStatus, setExternalApplyStatus] = useState<
    "idle" | "applying" | "awaitingUserConfirm" | "confirmed" | "cancelled"
  >("idle");
  const [activeBlockIds, setActiveBlockIds] = useState<Set<string>>(new Set());
  const [externalApplyError, setExternalApplyError] = useState<string>();
  const [currentContents, setCurrentContents] = useState<Map<string, string>>(
    new Map()
  );
  const chatService = new RealChatService();

  // 处理外部应用请求
  const handleRequestApply = useCallback((content: string, blockId: string) => {
    console.log("Requesting to apply content:", content);
    setCurrentContents((prev) => {
      const next = new Map(prev);
      next.set(blockId, content);
      return next;
    });
    setActiveBlockIds((prev) => {
      const next = new Set(prev);
      next.add(blockId);
      return next;
    });
    // 模拟异步操作
    setExternalApplyStatus("applying");
    setTimeout(() => {
      setExternalApplyStatus("awaitingUserConfirm");
      showInfoToast("请确认是否应用更改");
    }, 1000);
  }, []);

  // 处理确认应用
  const handleExternalConfirm = useCallback((blockId: string) => {
    setActiveBlockIds((prev) => {
      const next = new Set(prev);
      next.delete(blockId);
      // 如果没有更多激活的块，重置状态
      if (next.size === 0) {
        setExternalApplyStatus("confirmed");
        showSuccessToast("所有更改已确认应用");
        setExternalApplyStatus("idle");
      }
      return next;
    });
    setCurrentContents((prev) => {
      const next = new Map(prev);
      next.delete(blockId);
      return next;
    });
    showSuccessToast(`已确认应用块 ${blockId} 的更改`);
  }, []);

  // 处理取消应用
  const handleExternalCancel = useCallback((blockId: string) => {
    setActiveBlockIds((prev) => {
      const next = new Set(prev);
      next.delete(blockId);
      // 如果没有更多激活的块，重置状态
      if (next.size === 0) {
        setExternalApplyStatus("cancelled");
        showInfoToast("所有更改已取消");
        setExternalApplyStatus("idle");
      }
      return next;
    });
    setCurrentContents((prev) => {
      const next = new Map(prev);
      next.delete(blockId);
      return next;
    });
    showInfoToast(`已取消块 ${blockId} 的更改`);
  }, []);

  // 处理应用开始
  const handleApplyStart = useCallback(() => {
    showInfoToast("开始应用更改...");
  }, []);

  // 处理应用完成
  const handleApplyComplete = useCallback(
    (success: boolean, error?: string) => {
      if (success) {
        showSuccessToast("更改应用完成");
      } else {
        setExternalApplyError(error);
        showErrorToast(`应用失败: ${error}`);
      }
    },
    []
  );

  // 定义 ResultBlock 菜单项
  const resultBlockMenuItems = [
    {
      key: "ApplyToProject",
      label: "Apply to Project",
      onClick: (content: string, blockId: string) =>
        handleRequestApply(content, blockId),
    },
    {
      key: "Reapply",
      label: "Reapply",
      onClick: (content: string) => {
        console.log("Reapplying content:", content);
        showToast("Clicked to reapply");
      },
    },
    {
      key: "Quote",
      label: "Quote",
      onClick: (content: string) => {
        console.log("Quoting content:", content);
        showToast("Clicked to quote");
      },
    },
  ];

  const handleStreamStart = useCallback(() => {
    // Stream start handler
  }, []);

  const handleStreamEnd = useCallback(() => {
    // Stream end handler
  }, []);

  const handleStreamError = useCallback((error: Error) => {
    console.error("Stream error:", error);
  }, []);

  const handleFileUpload = useCallback((files: File[]) => {
    console.log("Uploaded files:", files);
    showToast(`Uploaded ${files.length} files`);
  }, []);

  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
    showToast(`Title updated: ${newTitle}`);
  }, []);

  const handleBookmarkChange = useCallback((bookmarked: boolean) => {
    setIsBookmarked(bookmarked);
    showToast(bookmarked ? "Bookmarked" : "Unbookmarked");
  }, []);

  const handleGenerateTitle = useCallback(async () => {
    setIsGeneratingTitle(true);
    // 模拟标题生成
    const titleCandidates = [
      "AI 助手：代码分析与优化",
      "AI 助手：数据处理与可视化",
      "AI 助手：文档生成与编辑",
      "AI 助手：问题诊断与解决",
      "AI 助手：技术探讨与学习",
    ];
    await new Promise((resolve) => setTimeout(resolve, 1500));
    const generatedTitle =
      titleCandidates[Math.floor(Math.random() * titleCandidates.length)];
    setTitle(generatedTitle);
    setIsGeneratingTitle(false);
    showToast(`AI generated title: ${generatedTitle}`);
  }, []);

  const handleBracketLinkClick = useCallback((content: string) => {
    showToast(`Clicked: ${content}`);
  }, []);

  const handleBracketLinkHover = useCallback((content: string) => {
    console.log(`Hovered: ${content}`);
  }, []);

  const handleClickFileName = useCallback(() => {
    showSuccessToast("Successfully clicked file name");
  }, []);

  const handleContextFileSelect = useCallback((file: FileInfo) => {
    showToast(`Selected context file: ${file.name}`);
  }, []);

  const handleContextSearch = useCallback((keyword: string) => {
    console.log(`Searching context: ${keyword}`);
  }, []);

  const handleContextFilter = useCallback((filters: string[]) => {
    console.log(`Filtering context: ${filters.join(", ")}`);
  }, []);

  return (
    <div className="h-full w-full flex flex-col overflow-hidden">
      <NoSSR>
        <ExternalApplyProvider
          value={{
            status: externalApplyStatus,
            activeBlockIds,
            onRequestApply: handleRequestApply,
            onConfirm: handleExternalConfirm,
            onCancel: handleExternalCancel,
            onApplyStart: handleApplyStart,
            onApplyComplete: handleApplyComplete,
            error: externalApplyError,
          }}
        >
          <div className="flex-1 h-full w-full overflow-hidden">
            <EnhancedChatCoreComponent
              chatService={chatService}
              className="h-full use-native-scrollbar overflow-auto"
              showEventBlocks={true}
              defaultBlockExpanded={true}
              defaultEventExpanded={false}
              isChatNavVisible={false}
              title={title}
              isBookmarked={isBookmarked}
              isSidebar={true}
              onTitleChange={handleTitleChange}
              onBookmarkChange={handleBookmarkChange}
              isGeneratingTitle={isGeneratingTitle}
              onGenerateTitle={handleGenerateTitle}
              onStreamStart={handleStreamStart}
              onStreamEnd={handleStreamEnd}
              onStreamError={handleStreamError}
              onBracketLinkClick={handleBracketLinkClick}
              onBracketLinkHover={handleBracketLinkHover}
              onFileUpload={handleFileUpload}
              inputPlaceholder="请输入修改要求，@已有文件或手动上传文件..."
              allowedFileTypes={[
                ".txt",
                ".pdf",
                ".jpg",
                ".png",
                ".mp3",
                ".m4a",
                ".csv",
              ]}
              maxFileSize={5 * 1024 * 1024}
              autoSize={{ minRows: 1, maxRows: 4 }}
              uploadAreaVariant="compact"
              emptyComponent={
                <div className="flex h-full flex-col items-center justify-center p-8 text-center">
                  <h3 className="mb-2 text-xl font-medium">AI 助手</h3>
                  <p className="mb-4 text-gray-500">
                    这是一个使用真实 API 的智能助手，可以：
                  </p>
                  <ul className="list-disc text-left text-sm text-gray-600">
                    <li>回答问题和提供建议</li>
                    <li>分析和处理上传的文件</li>
                    <li>支持多种工作流模式</li>
                    <li>实时流式对话响应</li>
                    <li>保存和管理对话记录</li>
                  </ul>
                  <p className="mt-4 text-sm text-gray-500">
                    请输入您的问题或上传文件开始对话
                  </p>
                </div>
              }
              resultBlockMenuItems={resultBlockMenuItems}
              onClickFileName={handleClickFileName}
              contextFiles={mockContextFiles}
              onContextFileSelect={handleContextFileSelect}
              onContextSearch={handleContextSearch}
              onContextFilter={handleContextFilter}
            />
          </div>
        </ExternalApplyProvider>
      </NoSSR>
    </div>
  );
}
