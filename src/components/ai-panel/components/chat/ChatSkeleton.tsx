"use client";

import React from "react";
import { Skeleton, SkeletonProvider } from "../../../ui/skeleton";
import LoadingSpinner from "../../../ui/loading/LoadingSpinner";

interface ChatSkeletonProps {
  className?: string;
}

const ChatSkeleton: React.FC<ChatSkeletonProps> = ({ className = "" }) => {
  return (
    <div className={`relative h-full w-full ${className}`}>
      {/* 骨架屏背景 */}
      <div
        className={`absolute inset-0 flex flex-col h-full w-full py-4 px-3 overflow-hidden`}
      >
        <SkeletonProvider>
          {/* 模拟聊天消息区域 */}
          <div className="flex-1 flex flex-col space-y-4 overflow-hidden">
            {/* 用户消息 */}
            <Skeleton className="flex justify-end">
              <div className="w-full h-28 bg-backgroundDeep/50 rounded-xl" />
            </Skeleton>

            {/* AI消息 */}
            <Skeleton className="flex justify-start">
              <div className="w-full h-8 bg-backgroundDeep/50 rounded-xl" />
            </Skeleton>
            <Skeleton className="flex justify-start gap-2">
              <div className="w-1/3 h-8 bg-backgroundDeep/50 rounded-xl" />
              <div className="w-2/3 h-8 bg-backgroundDeep/50 rounded-xl" />
            </Skeleton>
            <Skeleton className="flex justify-start">
              <div className="w-3/5 h-8 bg-backgroundDeep/50 rounded-xl" />
            </Skeleton>
            <Skeleton className="flex justify-start">
              <div className="w-full h-16 bg-backgroundDeep/50 rounded-xl" />
            </Skeleton>
            <Skeleton className="flex justify-start gap-2">
              <div className="w-1/2 h-8 bg-backgroundDeep/50 rounded-xl" />
            </Skeleton>
          </div>

          {/* 模拟输入区域 */}
          <Skeleton className="h-[142px] mt-4 bg-backgroundDeep/50 rounded-3xl relative">
            <div className="absolute top-4 left-4 w-28 h-6 bg-backgroundDeep/80 rounded-lg " />
            <div className="absolute top-12 left-4 right-16 h-6 bg-backgroundDeep/80 rounded-lg" />
            <div className="absolute bottom-3.5 left-3.5 w-20 h-8 bg-backgroundDeep/80 rounded-xl" />
            {/* 发送按钮 */}
            <div className="absolute bottom-3.5 right-3.5 w-8 h-8 bg-backgroundDeep/90 rounded-xl" />
          </Skeleton>
        </SkeletonProvider>

        <div className="absolute inset-0 flex items-center justify-center translate-y-[80px]">
          <LoadingSpinner size={24} />
        </div>
      </div>
    </div>
  );
};

export default ChatSkeleton;
