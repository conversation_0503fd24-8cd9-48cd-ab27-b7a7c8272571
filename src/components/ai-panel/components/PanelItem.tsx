"use client";

import * as React from "react";
import { usePanelContext, getGlobalPanel } from "../context/PanelContext";

interface PanelItemProps {
  id: string;
  header?: React.ReactNode;
  icon?: React.ReactNode;
  label?: string;
  children?: React.ReactNode;
}

/**
 * 面板项组件
 * 用于注册一个面板到面板系统中
 * 不渲染自身，仅用于注册
 *
 * 支持两种使用方式：
 * 1. 完整方式：提供所有属性 <PanelItem id="chat" header={...} icon={...} label="Chat">{...}</PanelItem>
 * 2. 简化方式：仅提供ID <PanelItem id="chat" />，其他信息通过全局注册表获取
 */
export function PanelItem({
  id,
  header,
  icon,
  label,
  children,
}: PanelItemProps): null {
  const panelContext = usePanelContext();

  // 使用 useLayoutEffect 确保在渲染前注册面板
  React.useLayoutEffect(() => {
    if (panelContext) {
      // 如果只提供了ID，尝试从全局注册表获取其他信息
      if (!header && !children && !icon) {
        const panelInfo = getGlobalPanel(id);
        if (panelInfo) {
          console.log(`PanelItem 从全局注册表获取面板信息: ${id}`);
          panelContext.registerPanel(id, {
            header: panelInfo.header,
            content: panelInfo.content,
            navItem: panelInfo.icon,
          });
        } else {
          console.warn(`未找到ID为 ${id} 的面板信息`);
        }
      } else {
        // 使用传入的属性注册面板
        console.log(`PanelItem 注册面板: ${id}`);
        panelContext.registerPanel(id, {
          header: header || <div>未提供标题</div>,
          content: children || <div>未提供内容</div>,
          navItem: icon,
        });
      }
    }
  }, [id, header, children, panelContext, icon, label]);

  return null;
}

export default PanelItem;
