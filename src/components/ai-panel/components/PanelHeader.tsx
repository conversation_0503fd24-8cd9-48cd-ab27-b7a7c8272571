"use client";

import * as React from "react";

interface PanelHeaderProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * 面板头部组件，用于显示标题和操作按钮
 * 高度固定，保持一致的设计风格
 */
export function PanelHeader({ children, className = "" }: PanelHeaderProps) {
  return (
    <div
      className={`flex items-center justify-between p-3 h-12 bg-background ${className}`}
    >
      {children}
    </div>
  );
}

export default PanelHeader;
