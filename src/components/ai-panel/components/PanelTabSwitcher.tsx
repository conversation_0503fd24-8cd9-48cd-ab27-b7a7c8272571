"use client";

import * as React from "react";

export type TabOption<T extends string> = {
  id: T;
  label: string;
};

interface PanelTabSwitcherProps<T extends string> {
  tabs: TabOption<T>[];
  activeTab: T;
  onTabChange: (tabId: T) => void;
  className?: string;
}

/**
 * 面板标签切换组件
 * 用于在面板顶部创建标签切换按钮
 */
export function PanelTabSwitcher<T extends string>({
  tabs,
  activeTab,
  onTabChange,
  className = "",
}: PanelTabSwitcherProps<T>): React.ReactElement {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`px-3 py-1 text-[13px] font-medium rounded-lg transition-colors ${
            activeTab === tab.id
              ? "bg-secondaryBtn text-secondaryBtn-text"
              : "text-secondaryBtn/90 bg-secondaryBtn/5 hover:bg-secondaryBtn/10 hover:text-secondaryBtn"
          }`}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
}

export default PanelTabSwitcher;
