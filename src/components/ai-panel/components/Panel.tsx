"use client";

import * as React from "react";
import { usePanelContext } from "../context/PanelContext";

/**
 * 面板组件
 * 用于以组件方式注册和渲染面板
 * 每个面板应该导出一个使用此组件的默认组件
 */
export function Panel({
  id,
  header,
  icon,
  label,
  children,
}: {
  id: string;
  header: React.ReactNode;
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
}): null {
  const panelContext = usePanelContext();

  // 使用 useLayoutEffect 确保在渲染前注册面板
  React.useLayoutEffect(() => {
    if (panelContext) {
      console.log(`Panel 注册面板: ${id}`);
      panelContext.registerPanel(id, {
        header,
        content: children,
        navItem: icon,
      });
    }
  }, [id, header, children, panelContext, icon, label]);

  return null;
}

export default Panel;
