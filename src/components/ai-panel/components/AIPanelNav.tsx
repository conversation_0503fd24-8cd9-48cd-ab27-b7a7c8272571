"use client";

import * as React from "react";
import { MessageSquare, Settings, Code, FileText } from "lucide-react";

interface AIPanelNavProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  className?: string;
  isCollapsed?: boolean;
  toggleCollapse?: () => void;
}

const AIPanelNav: React.FC<AIPanelNavProps> = ({
  activeTab,
  onTabChange,
  className = "",
  isCollapsed = false,
  toggleCollapse,
}) => {
  const tabs = [
    { id: "chat", icon: MessageSquare, label: "Chat" },
    { id: "code", icon: Code, label: "Code" },
    { id: "docs", icon: FileText, label: "Docs" },
    { id: "settings", icon: Settings, label: "Settings" },
  ];

  const handleTabClick = (tabId: string) => {
    // 如果没有提供 toggleCollapse 函数，则只执行普通的 tab 切换
    if (!toggleCollapse) {
      onTabChange(tabId);
      return;
    }

    // 根据当前状态执行不同的操作
    if (isCollapsed) {
      // 折叠状态：点击任意按钮都会展开并切换到对应 tab
      toggleCollapse(); // 先展开
      onTabChange(tabId); // 然后切换到对应 tab
    } else {
      // 展开状态：
      if (activeTab === tabId) {
        // 如果点击的是当前激活的 tab，则折叠
        toggleCollapse();
      } else {
        // 如果点击的是其他 tab，则切换到对应 tab
        onTabChange(tabId);
      }
    }
  };

  return (
    <div className={`flex flex-col items-center py-4 ${className}`}>
      {tabs.map((tab) => {
        const Icon = tab.icon;
        // 折叠状态下所有按钮显示未激活状态
        const isActive = !isCollapsed && activeTab === tab.id;
        return (
          <button
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            className={`w-8 h-8 mb-2 flex items-center justify-center rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-primary/10 text-primary hover:bg-primary/15"
                : isCollapsed
                ? "text-secondaryBtn/80 hover:bg-secondaryBtn/5 hover:text-secondaryBtn"
                : "text-muted-foreground hover:bg-secondaryBtn/5 hover:text-secondaryBtn"
            }`}
            aria-label={tab.label}
            title={tab.label}
          >
            <Icon size={16} />
          </button>
        );
      })}
    </div>
  );
};

export default AIPanelNav;
