"use client";

import * as React from "react";
import { createContext, useState, useCallback, useContext } from "react";

// 全局面板注册表
const globalPanelRegistry = new Map<
  string,
  {
    header: React.ReactNode;
    content: React.ReactNode;
    icon: React.ReactNode;
    label: string;
  }
>();

// 注册面板到全局注册表
export function registerGlobalPanel(
  id: string,
  header: React.ReactNode,
  content: React.ReactNode,
  icon: React.ReactNode,
  label: string
): void {
  console.log(`注册面板 ${id} 到全局注册表`);
  globalPanelRegistry.set(id, { header, content, icon, label });
}

// 获取已注册的面板信息
export function getGlobalPanel(id: string) {
  return globalPanelRegistry.get(id);
}

export type PanelRegistry = {
  [id: string]: {
    header: React.ReactNode;
    content: React.ReactNode;
    navItem?: React.ReactNode; // 导航项
  };
};

interface PanelContextType {
  registerPanel: (id: string, panel: PanelRegistry[string]) => void;
  panels: PanelRegistry;
  activeTab: string;
  setActiveTab: (id: string) => void;
  executePanelAction: (action: string, payload?: any) => void;
}

export const PanelContext = createContext<PanelContextType | null>(null);

export function usePanelContext() {
  const context = useContext(PanelContext);
  if (!context) {
    throw new Error("usePanelContext must be used within a PanelProvider");
  }
  return context;
}

// 用于面板内容组件订阅和响应操作的Hook
export function usePanelActions(
  handlers: Record<string, (payload?: any) => void>
) {
  const { executePanelAction } = usePanelContext();

  // 注册操作处理程序
  React.useEffect(() => {
    const handleAction = (action: string, payload?: any) => {
      if (handlers[action]) {
        handlers[action](payload);
        return true;
      }
      return false;
    };

    // 这里可以添加订阅逻辑，如果需要的话

    return () => {
      // 清理订阅
    };
  }, [handlers]);

  return { executePanelAction };
}
