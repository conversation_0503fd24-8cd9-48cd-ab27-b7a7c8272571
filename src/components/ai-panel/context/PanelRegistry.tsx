"use client";

import * as React from "react";

export interface PanelDefinition {
  id: string;
  header: React.ReactNode;
  content: React.ReactNode;
  icon: React.ReactNode;
  label: string;
}

// 全局注册表，存储所有已注册的面板
const panelRegistry = new Map<string, PanelDefinition>();

/**
 * 注册面板到全局注册表
 */
export function registerPanel(panelDef: PanelDefinition): void {
  console.log(`注册面板 ${panelDef.id} 到全局注册表`);
  panelRegistry.set(panelDef.id, panelDef);
}

/**
 * 获取已注册的面板定义
 */
export function getPanelDefinition(id: string): PanelDefinition | undefined {
  return panelRegistry.get(id);
}

/**
 * 获取所有已注册的面板
 */
export function getAllPanels(): PanelDefinition[] {
  return Array.from(panelRegistry.values());
}

/**
 * 检查面板是否已注册
 */
export function isPanelRegistered(id: string): boolean {
  return panelRegistry.has(id);
}
