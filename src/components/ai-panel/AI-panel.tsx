"use client";

import * as React from "react";
import { useState, useCallback, useEffect, useRef } from "react";
import { PanelContext, PanelRegistry } from "./context/PanelContext";
import {
  PanelErrorBoundary,
  PanelError,
} from "./components/PanelErrorBoundary";
import NavItem from "./components/NavItem";

interface AIPanelProps {
  className?: string;
  isCollapsed?: boolean;
  isContentVisible?: boolean;
  toggleCollapse?: () => void;
  defaultTab?: string;
  children?: React.ReactNode;
  projectId?: string; // 项目ID，用于AI面板获取项目相关的上下文
}

/**
 * AI面板容器组件
 * 负责管理面板状态、注册和切换不同的面板
 */
const AIPanel: React.FC<AIPanelProps> = ({
  className = "",
  isCollapsed = false,
  isContentVisible = true,
  toggleCollapse,
  defaultTab = "chat",
  children,
  projectId,
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [panels, setPanels] = useState<PanelRegistry>({});
  const [actionQueue, setActionQueue] = useState<
    { action: string; payload?: any }[]
  >([]);
  const childrenRef = useRef<React.ReactNode>(null);

  // 保存 children 引用，以便在 useEffect 中使用
  childrenRef.current = children;

  // 注册面板
  const registerPanel = useCallback(
    (id: string, panel: PanelRegistry[string]) => {
      console.log(`注册面板 ${id}:`, panel);
      setPanels((prev) => {
        // 如果面板已经存在且内容相同，则不更新
        if (
          prev[id] &&
          prev[id].header === panel.header &&
          prev[id].content === panel.content &&
          prev[id].navItem === panel.navItem
        ) {
          return prev;
        }

        const newPanels = {
          ...prev,
          [id]: panel,
        };

        console.log("更新后的面板:", Object.keys(newPanels));
        return newPanels;
      });
    },
    []
  );

  // 执行面板操作
  const executePanelAction = useCallback((action: string, payload?: any) => {
    setActionQueue((prev) => [...prev, { action, payload }]);
  }, []);

  // 处理面板操作队列
  useEffect(() => {
    if (actionQueue.length > 0) {
      // 处理队列中的第一个操作
      const { action, payload } = actionQueue[0];

      // 从队列中移除已处理的操作
      setActionQueue((prev) => prev.slice(1));

      // 这里可以添加默认的操作处理逻辑
      console.log(`执行操作: ${action}`, payload);
    }
  }, [actionQueue]);

  // 处理导航项点击
  const handleTabClick = (tabId: string) => {
    // 如果没有提供 toggleCollapse 函数，则只执行普通的 tab 切换
    if (!toggleCollapse) {
      setActiveTab(tabId);
      return;
    }

    // 根据当前状态执行不同的操作
    if (isCollapsed) {
      // 折叠状态：点击任意按钮都会展开并切换到对应 tab
      toggleCollapse(); // 先展开
      setActiveTab(tabId); // 然后切换到对应 tab
    } else {
      // 展开状态：
      if (activeTab === tabId) {
        // 如果点击的是当前激活的 tab，则折叠
        toggleCollapse();
      } else {
        // 如果点击的是其他 tab，则切换到对应 tab
        setActiveTab(tabId);
      }
    }
  };

  // 调试输出
  useEffect(() => {
    console.log("注册的面板:", Object.keys(panels));
    console.log("当前激活的面板:", activeTab);

    // 如果没有面板，但有默认标签，则设置激活标签
    if (Object.keys(panels).length > 0 && !panels[activeTab]) {
      const firstPanelId = Object.keys(panels)[0];
      console.log(
        `没有找到激活的面板 ${activeTab}，切换到第一个面板 ${firstPanelId}`
      );
      setActiveTab(firstPanelId);
    }
  }, [panels, activeTab]);

  // 创建上下文值
  const contextValue = React.useMemo(
    () => ({
      panels,
      registerPanel,
      activeTab,
      setActiveTab,
      executePanelAction,
    }),
    [panels, registerPanel, activeTab, setActiveTab, executePanelAction]
  );

  return (
    <PanelContext.Provider value={contextValue}>
      <div className={`h-full flex flex-row ${className} relative`}>
        {/* 主内容区域 - 在折叠时隐藏，并添加淡入淡出效果 */}
        {!isCollapsed && (
          <div
            className={`flex-1 flex flex-col overflow-hidden transition-all duration-150 bg-background rounded-xl ${
              isContentVisible ? "opacity-100 mr-2" : "opacity-0"
            }`}
          >
            {/* 当前激活面板的Header */}
            {panels[activeTab]?.header && (
              <div className="pt-0">{panels[activeTab].header}</div>
            )}

            {/* 当前激活面板的Content */}
            <div className="flex-1 flex flex-col overflow-hidden relative">
              <PanelErrorBoundary fallback={<PanelError />}>
                <div className="h-full flex-1 flex flex-col overflow-hidden">
                  {panels[activeTab]?.content}
                </div>
              </PanelErrorBoundary>
            </div>
          </div>
        )}

        {/* 右侧导航 - 始终显示，样式与 AIPanelNav 保持一致 */}
        <div className="flex flex-col items-center py-4 w-8 shrink-0">
          {Object.entries(panels).map(([id, panel]) => (
            <NavItem
              key={id}
              id={id}
              isActive={activeTab === id}
              onClick={() => handleTabClick(id)}
              icon={panel.navItem || <DefaultIcon />}
              label={id.charAt(0).toUpperCase() + id.slice(1)}
              isCollapsed={isCollapsed}
            />
          ))}

          {/* 折叠/展开按钮 */}
          {toggleCollapse && (
            <button
              className="w-8 h-8 mt-auto flex items-center justify-center rounded-lg transition-all duration-300 text-muted-foreground hover:bg-secondaryBtn/5 hover:text-secondaryBtn"
              onClick={toggleCollapse}
              aria-label={isCollapsed ? "Expand panel" : "Collapse panel"}
              title={isCollapsed ? "Expand" : "Collapse"}
            >
              <CollapseIcon isCollapsed={isCollapsed} />
            </button>
          )}
        </div>
      </div>

      {/* 渲染 children 以注册面板 */}
      {children}
    </PanelContext.Provider>
  );
};

// 默认图标组件
function DefaultIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="18" height="18" x="3" y="3" rx="2" />
      <path d="M9 9h.01" />
      <path d="M15 9h.01" />
      <path d="M9 15h.01" />
      <path d="M15 15h.01" />
    </svg>
  );
}

// 折叠/展开图标
function CollapseIcon({ isCollapsed }: { isCollapsed: boolean }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      {isCollapsed ? (
        <polyline points="15 18 9 12 15 6" />
      ) : (
        <polyline points="9 18 15 12 9 6" />
      )}
    </svg>
  );
}

export default AIPanel;
