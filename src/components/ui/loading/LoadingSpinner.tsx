import React, { useEffect, useState } from "react";
import { useTheme } from "next-themes";

type LoadingSpinnerProps = {
  size?: number;
  text?: string;
  className?: string;
  animColor?: string;
  textColor?: string;
  gap?: number;
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 24,
  text = "",
  className = "",
  animColor,
  textColor = "text-secondaryBtn/90",
}) => {
  const [dots, setDots] = useState("..");
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // 确保组件只在客户端渲染后处理主题
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // 如果文字为空，则不需要点动画
    if (!text) return;

    let count = 2; // 从2个点开始
    const interval = setInterval(() => {
      count = (count + 1) % 4;
      setDots(".".repeat(count));
    }, 300);

    return () => clearInterval(interval);
  }, [text]);

  // 获取颜色
  const getColor = () => {
    if (animColor) return animColor;
    return mounted && resolvedTheme === "dark" ? "#F0E8DD" : "#1D1814";
  };

  // 计算实际尺寸
  const actualSize = size;
  const gridSize = actualSize / 3; // 将容器分为3x3网格
  const dotSize = gridSize * 0.8; // 点的大小为网格的80%
  const color = getColor();

  // 生成9个点的样式和动画，使用预定义的CSS类
  const generateDots = () => {
    const dots = [];

    for (let row = 0; row < 3; row++) {
      for (let col = 0; col < 3; col++) {
        const index = row * 3 + col;
        // 使用预定义的CSS类，这些类在全局CSS中已定义好动画
        const animationClass = `wave-dot-${row}-${col}`;

        dots.push(
          <div
            key={index}
            className={`absolute rounded-[1px] ${animationClass}`}
            style={{
              width: `${dotSize}px`,
              height: `${dotSize}px`,
              left: `${col * gridSize + (gridSize - dotSize) / 2}px`,
              top: `${row * gridSize + (gridSize - dotSize) / 2}px`,
              backgroundColor: color,
            }}
          />
        );
      }
    }
    return dots;
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div
        className={`relative ${text ? "mb-4" : ""}`}
        style={{ width: actualSize, height: actualSize }}
      >
        {generateDots()}
      </div>
      {text && (
        <h1 className={`text-md font-semibold ${textColor}`}>
          {text}
          <span className="inline-block min-w-[24px]">{dots}</span>
        </h1>
      )}
    </div>
  );
};

export default LoadingSpinner;
