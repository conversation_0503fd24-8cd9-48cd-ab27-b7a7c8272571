/**
 * Logger使用示例组件
 */
import React, { useEffect, useState } from "react";
import useLogger from "../../hooks/useLogger";

interface LoggerExampleProps {
  userId?: string;
}

export const LoggerExample: React.FC<LoggerExampleProps> = ({ userId }) => {
  const logger = useLogger("LoggerExample");
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<Error | null>(null);

  // 组件挂载时记录日志
  useEffect(() => {
    logger.log("组件已挂载");

    // 模拟API请求
    const fetchData = async () => {
      try {
        logger.info("开始请求数据", { userId });

        // 模拟API调用
        const response = await mockApiCall(userId);

        logger.debug("收到API响应", {
          responseSize: JSON.stringify(response).length,
        });
        setData(response);

        logger.info("数据加载成功");
      } catch (err) {
        const error = err as Error;
        logger.error("数据加载失败", { error: error.message });
        setError(error);
      }
    };

    fetchData();

    // 组件卸载时记录日志
    return () => {
      logger.log("组件已卸载");
    };
  }, [userId, logger]);

  // 用户交互时记录日志
  const handleButtonClick = () => {
    logger.debug("按钮被点击");
    // 处理点击事件...
  };

  if (error) {
    logger.warn("渲染错误状态");
    return <div>加载失败</div>;
  }

  if (!data) {
    logger.debug("渲染加载状态");
    return <div>加载中...</div>;
  }

  logger.debug("渲染数据", { dataItems: data.length });

  return (
    <div>
      <h2>Logger示例组件</h2>
      <button onClick={handleButtonClick}>点击记录日志</button>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};

// 模拟API调用
const mockApiCall = async (userId?: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (!userId) {
        reject(new Error("缺少用户ID"));
        return;
      }

      resolve({
        id: userId,
        name: "测试用户",
        items: [1, 2, 3, 4, 5],
      });
    }, 1000);
  });
};

export default LoggerExample;
