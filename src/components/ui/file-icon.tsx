import React from "react";
import dynamic from "next/dynamic";
import {
  FileText,
  FileCode,
  FileImage,
  FileSpreadsheet,
  FileJson,
  File,
  Folder,
  FolderOpen,
} from "lucide-react";

// 文件类型与图标映射
const fileIconMap: Record<string, React.FC<{ className?: string }>> = {
  // 文档类型
  docx: FileText,
  doc: FileText,
  pdf: FileText,
  md: FileText,
  txt: FileText,

  // 代码类型
  ts: FileCode,
  tsx: FileCode,
  js: FileCode,
  jsx: FileCode,
  css: FileCode,
  scss: FileCode,
  html: FileCode,
  json: FileJson,

  // 图片类型
  png: FileImage,
  jpg: FileImage,
  jpeg: FileImage,
  gif: FileImage,
  svg: FileImage,

  // 数据类型
  csv: FileSpreadsheet,
  xlsx: FileSpreadsheet,
  xls: FileSpreadsheet,

  // 默认类型
  default: File,
  folder: Folder,
  folderOpen: FolderOpen,
};

export interface FileIconProps {
  fileName: string;
  filePath?: string;
  fileType?: "file" | "folder";
  className?: string;
  isFolderOpen?: boolean;
}

/**
 * 获取文件扩展名
 * @param filePath 文件路径
 * @returns 文件扩展名
 */
const getFileExtension = (filePath: string): string => {
  const fileName = filePath.split("/").pop() || "";
  return fileName.split(".").pop()?.toLowerCase() || "";
};

/**
 * 通用文件图标组件
 * 根据文件路径或文件类型显示对应的图标
 */
const FileIcon: React.FC<FileIconProps> = ({
  fileName,
  filePath,
  fileType = "file",
  className = "h-4 w-4 mr-2",
  isFolderOpen = false,
}) => {
  if (fileType === "folder") {
    const IconComponent = isFolderOpen
      ? fileIconMap["folderOpen"]
      : fileIconMap["folder"];
    return <IconComponent className={className} />;
  }

  // 优先使用文件路径获取扩展名
  let extension = "";
  if (filePath) {
    extension = getFileExtension(filePath);
  } else {
    // 兼容旧的使用方式，如果没有提供路径则使用文件名
    extension = fileName.split(".").pop()?.toLowerCase() || "";
  }

  // 根据扩展名获取对应的图标组件
  const IconComponent = fileIconMap[extension] || fileIconMap["default"];

  return <IconComponent className={className} />;
};

export default FileIcon;
