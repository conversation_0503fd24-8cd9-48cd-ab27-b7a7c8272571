"use client";

import React from "react";
import { Loader2 } from "lucide-react";

interface LoadingStateProps {
  /** 加载文本 */
  text?: string;
  /** 是否显示加载图标 */
  showIcon?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 加载状态组件
 * 用于显示数据加载中的状态
 */
const LoadingState: React.FC<LoadingStateProps> = ({
  text = "Loading projects...",
  showIcon = true,
  className = "",
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-16 ${className}`}>
      {/* 加载图标 */}
      {showIcon && (
        <div className="flex items-center justify-center w-16 h-16 mb-4">
          <Loader2 className="w-8 h-8 text-secondaryBtn/40 animate-spin" />
        </div>
      )}

      {/* 加载文本 */}
      <p className="text-sm text-secondaryBtn/60">{text}</p>
    </div>
  );
};

export default LoadingState;
