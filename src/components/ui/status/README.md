# Status Components

这个目录包含了用于显示不同状态的可复用组件。

## 组件列表

### ErrorState
用于显示错误状态的组件。

```tsx
import { ErrorState } from "@/components/ui/status";

// 基本用法
<ErrorState message="Failed to load data" />

// 自定义标题和重试回调
<ErrorState 
  title="Connection Failed"
  message="Unable to connect to server"
  retryText="Retry Connection"
  onRetry={() => refetch()}
/>

// 覆盖层模式
<ErrorState 
  message="Failed to load projects"
  overlay={true}
/>
```

**Props:**
- `title?: string` - 错误标题 (默认: "Failed to Load Projects")
- `message: string` - 错误消息 (必需)
- `retryText?: string` - 重试按钮文本 (默认: "Try Again")
- `onRetry?: () => void` - 重试回调函数 (默认: 刷新页面)
- `overlay?: boolean` - 是否显示为覆盖层模式 (默认: false)
- `className?: string` - 自定义类名

### EmptyState
用于显示空状态的组件。

```tsx
import { EmptyState } from "@/components/ui/status";

// 基本用法
<EmptyState />

// 自定义内容和操作
<EmptyState 
  title="No Files Found"
  description="Upload your first file to get started"
  actionText="Upload File"
  onAction={() => openUploadDialog()}
/>

// 自定义图标
<EmptyState 
  icon={<FileIcon className="w-8 h-8" />}
  title="No Documents"
  description="Create your first document"
/>
```

**Props:**
- `title?: string` - 空状态标题 (默认: "No Projects Found")
- `description?: string` - 空状态描述 (默认: "Create a new project to get started")
- `actionText?: string` - 操作按钮文本 (默认: "Create Project")
- `onAction?: () => void` - 操作回调函数
- `icon?: React.ReactNode` - 自定义图标
- `className?: string` - 自定义类名

### LoadingState
用于显示加载状态的组件。

```tsx
import { LoadingState } from "@/components/ui/status";

// 基本用法
<LoadingState />

// 自定义文本
<LoadingState text="Uploading files..." />

// 不显示图标
<LoadingState 
  text="Processing..."
  showIcon={false}
/>
```

**Props:**
- `text?: string` - 加载文本 (默认: "Loading projects...")
- `showIcon?: boolean` - 是否显示加载图标 (默认: true)
- `className?: string` - 自定义类名

## 设计原则

1. **一致性**: 所有状态组件使用相同的设计语言和颜色系统
2. **可定制**: 提供足够的 props 来满足不同场景的需求
3. **可访问性**: 确保良好的对比度和键盘导航支持
4. **响应式**: 适配不同屏幕尺寸
5. **主题支持**: 支持亮色和暗色主题
