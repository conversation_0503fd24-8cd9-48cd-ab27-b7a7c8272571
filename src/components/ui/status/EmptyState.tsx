"use client";

import React from "react";
import { FolderOpen, Plus } from "lucide-react";

interface EmptyStateProps {
  /** 空状态标题 */
  title?: string;
  /** 空状态描述 */
  description?: string;
  /** 操作按钮文本 */
  actionText?: string;
  /** 操作回调函数 */
  onAction?: () => void;
  /** 自定义图标 */
  icon?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 右键菜单回调函数 */
  onContextMenu?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
}

/**
 * 空状态组件
 * 用于显示无数据时的提示信息
 */
const EmptyState: React.FC<EmptyStateProps> = ({
  title = "No Projects Found",
  description = "Create a new project to get started",
  actionText = "Create Project",
  onAction,
  icon,
  className = "",
  onContextMenu,
}) => {
  const defaultIcon = <FolderOpen className="w-8 h-8 text-secondaryBtn/40" />;

  return (
    <div
      className={`flex flex-col items-center justify-center py-16 xxs:pr-2 xs:pr-2 sm:pr-12 ${className}`}
      onContextMenu={onContextMenu}
    >
      {/* 空状态图标 */}
      <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-secondaryBtn/5 mb-4">
        {icon || defaultIcon}
      </div>

      {/* 空状态内容 */}
      <h3 className="text-lg font-medium text-secondaryBtn/80 mb-2">{title}</h3>
      <p className="text-sm text-secondaryBtn/60 text-center max-w-md mb-6 leading-relaxed">
        {description}
      </p>

      {/* 操作按钮 */}
      {onAction && (
        <button
          onClick={onAction}
          className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-primaryBtn bg-primaryBtn/10 hover:bg-primaryBtn/20 rounded-lg transition-colors duration-200"
        >
          <Plus className="w-4 h-4" />
          {actionText}
        </button>
      )}
    </div>
  );
};

export default EmptyState;
