"use client";

import React from "react";
import { CloudAlert, RefreshCw } from "lucide-react";

interface ErrorStateProps {
  /** 错误标题 */
  title?: string;
  /** 错误消息 */
  message: string;
  /** 重试按钮文本 */
  retryText?: string;
  /** 重试回调函数 */
  onRetry?: () => void;
  /** 是否显示为覆盖层模式 */
  overlay?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 错误状态组件
 * 用于显示错误信息和重试操作
 */
const ErrorState: React.FC<ErrorStateProps> = ({
  title = "",
  message,
  retryText = "Try Again",
  onRetry,
  overlay = false,
  className = "",
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // 默认重试行为：刷新页面
      window.location.reload();
    }
  };

  const contentClasses = `flex flex-col items-center justify-center py-16 ${className}`;
  const overlayClasses = overlay
    ? "absolute inset-0 bg-gradient-to-b from-backgroundDeep/10 to-backgroundDeep"
    : "";

  return (
    <div className={`${contentClasses} ${overlayClasses}`}>
      {/* 错误图标 */}
      <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-secondaryBtn/5 mb-4">
        <CloudAlert className="w-8 h-8 text-secondaryBtn/60" />
      </div>

      {/* 错误内容 */}
      <h3 className="text-xl font-bold text-foreground mb-4">{title}</h3>
      <p className="text-secondaryBtn/60 mb-12 text-sm leading-relaxed text-center max-w-md">
        {message}
      </p>

      {/* 重试按钮 */}
      <button
        onClick={handleRetry}
        className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-secondaryBtn bg-secondaryBtn/5 hover:bg-secondaryBtn/10 rounded-lg transition-colors duration-200"
      >
        <RefreshCw className="w-4 h-4" />
        {retryText}
      </button>
    </div>
  );
};

export default ErrorState;
