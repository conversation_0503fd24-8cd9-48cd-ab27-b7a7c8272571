import React, { ReactNode, useEffect, useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useDismiss,
  useInteractions,
  FloatingPortal,
  VirtualElement,
} from "@floating-ui/react";

interface ContextMenuProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  referenceElement?: HTMLElement | null;
  children: ReactNode;
  width?: string;
  placement?:
    | "bottom-end"
    | "bottom-start"
    | "top-end"
    | "top-start"
    | "right-start"
    | "left-start";
  clickPosition?: { x: number; y: number } | null;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  isOpen,
  setIsOpen,
  referenceElement,
  children,
  width = "w-[160px]",
  placement = "bottom-end",
  clickPosition = null,
}) => {
  // 创建虚拟元素用于点击位置定位
  const virtualElement: VirtualElement | null = useMemo(() => {
    if (!clickPosition) return null;

    return {
      getBoundingClientRect() {
        return {
          width: 0,
          height: 0,
          x: clickPosition.x,
          y: clickPosition.y,
          top: clickPosition.y,
          left: clickPosition.x,
          right: clickPosition.x,
          bottom: clickPosition.y,
        };
      },
    };
  }, [clickPosition]);

  // 使用 FloatingUI 设置浮动菜单
  const floating = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    middleware: [offset(8), flip(), shift({ padding: 8 })],
    whileElementsMounted: autoUpdate,
    placement,
    // 优先使用 clickPosition（虚拟元素），其次使用 referenceElement
    ...(virtualElement && {
      elements: {
        reference: virtualElement as Element,
      },
    }),
    ...(referenceElement &&
      !virtualElement && {
        elements: {
          reference: referenceElement,
        },
      }),
  });

  const { refs, context, x, y, strategy } = floating;

  const dismiss = useDismiss(context);

  const { getFloatingProps } = useInteractions([dismiss]);

  return (
    <FloatingPortal>
      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              className="fixed inset-0 z-40 bg-transparent"
              onClick={(e) => {
                e.stopPropagation();
                setIsOpen(false);
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.15 }}
            />
            <motion.div
              ref={refs.setFloating}
              style={{
                position: strategy,
                top: y ?? 0,
                left: x ?? 0,
              }}
              className={`z-50 p-0 bg-floatingBar border border-floatingBar-border rounded-xl shadow-lg ${width} overflow-hidden`}
              onClick={(e) => e.stopPropagation()}
              {...getFloatingProps()}
              initial={{ opacity: 0, scale: 0.95, y: -5 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -5 }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 30,
                duration: 0.2,
              }}
            >
              <div className="p-1 rounded-none">{children}</div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </FloatingPortal>
  );
};

export default ContextMenu;
