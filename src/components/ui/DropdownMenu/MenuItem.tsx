import React, { ReactNode } from "react";
import { motion } from "framer-motion";

interface MenuItemProps {
  onClick: (e: React.MouseEvent) => void;
  children: ReactNode;
  className?: string;
  textColor?: string;
  disabled?: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({
  onClick,
  children,
  className = "",
  textColor = "",
  disabled = false,
}) => {
  const handleClick = (e: React.MouseEvent) => {
    if (disabled) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    e.stopPropagation();
    onClick(e);
  };

  // 根据文本颜色确定悬停背景色
  const getHoverClass = () => {
    if (textColor === "text-red-500") {
      return "hover:bg-red-50 dark:hover:bg-red-400/10";
    }
    return "hover:bg-secondaryBtn/5 dark:hover:bg-gray-800/30";
  };

  return (
    <motion.button
      className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-all duration-150 ${
        disabled 
          ? "opacity-50 cursor-not-allowed" 
          : getHoverClass()
      } ${textColor} ${className}`}
      onClick={handleClick}
      disabled={disabled}
      transition={{ duration: 0.1 }}
    >
      {children}
    </motion.button>
  );
};

export default MenuItem;
