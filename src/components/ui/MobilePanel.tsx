import React from "react";
import { PanelLeft, PanelRight, X } from "lucide-react";
import Sidebar from "../sidebar/sidebar";
import AIPanel from "../ai-panel/AI-panel";

interface MobilePanelProps {
  /**
   * 是否显示面板
   */
  isOpen: boolean;
  /**
   * 当前显示的面板类型
   */
  activePanel: "sidebar" | "aiPanel" | null;
  /**
   * 关闭面板的回调
   */
  onClose: () => void;
  /**
   * 文件路径变化回调（传递给Sidebar）
   */
  onFilePathChange?: (filePath: string) => void;
  /**
   * 项目ID，用于传递给子组件
   */
  projectId?: string;
}

/**
 * 移动端浮层面板，固定在编辑器上方
 * 可以切换显示 Sidebar 或 AI Panel
 */
const MobilePanel: React.FC<MobilePanelProps> = ({
  isOpen,
  activePanel,
  onClose,
  onFilePathChange,
  projectId,
}) => {
  // 确定面板标题和图标
  const getPanelTitle = () => {
    if (activePanel === "sidebar") return "File Explorer";
    if (activePanel === "aiPanel") return "AI Panel";
    return "";
  };

  const getPanelIcon = () => {
    if (activePanel === "sidebar")
      return (
        <PanelLeft size={18} className="text-gray-600 dark:text-gray-400" />
      );
    if (activePanel === "aiPanel")
      return (
        <PanelRight size={18} className="text-gray-600 dark:text-gray-400" />
      );
    return null;
  };

  return (
    <>
      {/* 背景遮罩 - 点击关闭面板 */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-40 sm:hidden"
          onClick={onClose}
          aria-hidden="true"
        />
      )}

      {/* 浮层面板 - 现在固定在顶部 */}
      <div
        className={`fixed top-12 left-0 right-0 z-50 bg-background rounded-t-2xl shadow-lg transform transition-all duration-300 ease-in-out sm:hidden ${
          isOpen
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-full pointer-events-none"
        }`}
        style={{
          height: "calc(100% - 48px)", // 减去顶部导航栏的高度(12*4=48px)
        }}
      >
        {/* 面板头部 */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            {getPanelIcon()}
            <h3 className="text-sm font-medium">{getPanelTitle()}</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            aria-label="Close panel"
          >
            <X size={18} className="text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* 面板内容 - 占满剩余高度 */}
        <div className="overflow-y-auto h-[calc(100%-44px)] px-2 pb-8">
          {" "}
          {/* 44px 是面板头部高度 */}
          {activePanel === "sidebar" && (
            <div className="h-full">
              <Sidebar
                onFilePathChange={onFilePathChange}
                projectId={projectId}
              />
            </div>
          )}
          {activePanel === "aiPanel" && (
            <div className="h-full">
              <AIPanel projectId={projectId} />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default MobilePanel;
