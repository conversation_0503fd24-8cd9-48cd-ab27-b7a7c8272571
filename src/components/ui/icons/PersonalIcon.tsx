import React from "react";

interface PersonalIconProps {
  size?: number;
  className?: string;
}

export const PersonalIcon: React.FC<PersonalIconProps> = ({
  size = 18,
  className = "",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
    >
      <path
        d="M20.5303,8.469673440475464Q20.75,8.689343440475465,20.75,9.000003440475464Q20.75,9.061403440475463,20.756,9.122523440475465Q20.762,9.183633440475464,20.774,9.243863440475465Q20.786,9.304093440475464,20.8038,9.362853440475464Q20.8216,9.421623440475464,20.8451,9.478353440475463Q20.8686,9.535083440475464,20.8976,9.589243440475464Q20.9265,9.643403440475463,20.9607,9.694463440475463Q20.9948,9.745523440475463,21.0337,9.792993440475463Q21.0727,9.840463440475464,21.1161,9.883883440475465Q21.1595,9.927303440475463,21.207,9.966263440475464Q21.2545,10.005223440475465,21.3055,10.039333440475463Q21.3566,10.073453440475465,21.4108,10.102403440475463Q21.4649,10.131353440475465,21.5216,10.154843440475464Q21.5784,10.178353440475464,21.6371,10.196173440475464Q21.6959,10.214003440475464,21.7561,10.225983440475463Q21.8164,10.237963440475465,21.8775,10.243983440475464Q21.9386,10.250003440475464,22,10.250003440475464Q22.0614,10.250003440475464,22.1225,10.243983440475464Q22.1836,10.237963440475465,22.2439,10.225983440475463Q22.3041,10.214003440475464,22.3629,10.196173440475464Q22.4216,10.178353440475464,22.4784,10.154853440475463Q22.5351,10.131353440475465,22.5892,10.102403440475463Q22.6434,10.073453440475465,22.6945,10.039333440475463Q22.7455,10.005223440475465,22.793,9.966263440475464Q22.8405,9.927303440475463,22.8839,9.883883440475465Q22.9273,9.840463440475464,22.9663,9.792993440475463Q23.0052,9.745523440475463,23.0393,9.694463440475463Q23.0735,9.643403440475463,23.1024,9.589243440475464Q23.1313,9.535083440475464,23.1548,9.478353440475463Q23.1784,9.421623440475464,23.1962,9.362853440475464Q23.214,9.304093440475464,23.226,9.243863440475465Q23.238,9.183633440475464,23.244,9.122523440475465Q23.25,9.061403440475463,23.25,9.000003440475464Q23.25,7.653803440475464,22.2981,6.701903440475464Q21.3462,5.750003440475464,20,5.750003440475464L12.07,5.750003440475464Q11.6663,5.750003440475464,11.44384,5.412333440475464L10.62606,4.200659440475464Q9.65954,2.732693440475464,7.88775,2.750063440475464L4,2.7500034404754636Q2.653806,2.7500034404754636,1.701903,3.701903440475464Q0.75,4.653805440475464,0.75,6.000003440475464L0.75,16.999903440475464Q0.75,18.346103440475463,1.701903,19.298003440475462Q2.653806,20.249903440475464,4,20.249903440475464L7,20.249903440475464Q7.06141,20.249903440475464,7.12252,20.243903440475464Q7.18363,20.237903440475463,7.24386,20.225903440475463Q7.30409,20.213903440475463,7.36286,20.196103440475465Q7.42162,20.178203440475464,7.47835,20.154703440475465Q7.53509,20.131203440475463,7.58925,20.102303440475463Q7.6434,20.073403440475463,7.69446,20.039203440475465Q7.74552,20.005103440475462,7.79299,19.966203440475464Q7.84046,19.927203440475466,7.88388,19.883803440475464Q7.92731,19.840403440475463,7.96626,19.792903440475463Q8.00522,19.745403440475464,8.03934,19.69440344047546Q8.073450000000001,19.643303440475464,8.1024,19.589103440475462Q8.131350000000001,19.535003440475464,8.15485,19.478303440475464Q8.17835,19.421503440475462,8.19617,19.362803440475464Q8.214,19.304003440475462,8.22598,19.243803440475464Q8.237960000000001,19.183503440475462,8.24398,19.122403440475466Q8.25,19.061303440475463,8.25,18.999903440475464Q8.25,18.938503440475465,8.24398,18.87740344047546Q8.237960000000001,18.816303440475465,8.22598,18.756003440475464Q8.214,18.695803440475466,8.19617,18.637003440475464Q8.17835,18.578303440475466,8.15485,18.521503440475463Q8.131350000000001,18.464803440475464,8.1024,18.410703440475466Q8.073450000000001,18.356503440475464,8.03934,18.305403440475466Q8.00522,18.254403440475464,7.96626,18.206903440475465Q7.92731,18.159403440475465,7.88388,18.116003440475463Q7.84046,18.07260344047546,7.79299,18.033603440475463Q7.74552,17.99470344047546,7.69446,17.960603440475463Q7.6434,17.926403440475465,7.58925,17.897503440475464Q7.53509,17.868603440475464,7.47835,17.845003440475466Q7.42162,17.821603440475464,7.36286,17.803703440475466Q7.30409,17.785903440475465,7.24386,17.773903440475465Q7.18363,17.761903440475464,7.12252,17.755903440475464Q7.06141,17.749903440475464,7,17.749903440475464L4,17.749903440475464Q3.25,17.749903440475464,3.25,16.999903440475464L3.25,6.000003440475464Q3.25,5.250003440475464,4,5.250003440475464L7.91225,5.249943440475464Q8.32113,5.245933440475464,8.55394,5.599343440475463L9.35616,6.787673440475464Q10.31952,8.250003440475464,12.07,8.250003440475464L20,8.250003440475464Q20.3107,8.250003440475464,20.5303,8.469673440475464Z"
        fillRule="evenodd"
        fill="currentColor"
        fillOpacity="1"
      />
      <ellipse
        cx="16.5"
        cy="14.5"
        rx="3.5"
        ry="3.5"
        fillOpacity="0"
        strokeOpacity="1"
        stroke="currentColor"
        fill="none"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.444406,19.06628Q9.75,19.96179,9.75,21Q9.75,21.06141,9.75602,21.12252Q9.76204,21.18363,9.77402,21.24386Q9.786,21.30409,9.80382,21.36286Q9.82165,21.42162,9.84515,21.47835Q9.86865,21.53509,9.8976,21.58925Q9.92655,21.6434,9.96066,21.69446Q9.99478,21.74552,10.033737,21.79299Q10.072694,21.84046,10.116117,21.88388Q10.159539,21.92731,10.207008,21.96626Q10.254478,22.00522,10.305537,22.03934Q10.356597,22.07345,10.410754,22.1024Q10.464912,22.13135,10.521646,22.15485Q10.57838,22.178350000000002,10.637144,22.196170000000002Q10.695909,22.214,10.756137,22.22598Q10.816366,22.23796,10.877479,22.24398Q10.9385914,22.25,11,22.25Q11.0614086,22.25,11.122521,22.24398Q11.183634,22.23796,11.243863,22.22598Q11.304091,22.214,11.362856,22.19618Q11.42162,22.178350000000002,11.478354,22.15485Q11.535088,22.13135,11.589246,22.1024Q11.643403,22.07345,11.694463,22.03934Q11.745522,22.00522,11.792992,21.96626Q11.840461,21.92731,11.883883,21.88388Q11.927306,21.84046,11.966263,21.79299Q12.00522,21.74552,12.03934,21.69446Q12.07345,21.6434,12.1024,21.58925Q12.13135,21.53509,12.15485,21.47835Q12.17835,21.42162,12.19618,21.36286Q12.214,21.30409,12.22598,21.24386Q12.23796,21.18363,12.24398,21.12252Q12.25,21.06141,12.25,21Q12.25,20.817520000000002,12.42003,20.59825Q12.67749,20.26622,13.20948,19.97605Q14.54057,19.25,16.5,19.25Q18.45943,19.25,19.79052,19.97605Q20.32251,20.26622,20.57997,20.59825Q20.75,20.81751,20.75,21Q20.75,21.06141,20.75602,21.12252Q20.76204,21.18363,20.77402,21.24386Q20.786,21.30409,20.80382,21.36286Q20.821649999999998,21.42162,20.84515,21.47835Q20.868650000000002,21.53509,20.8976,21.58925Q20.92655,21.6434,20.96066,21.69446Q20.99478,21.74552,21.0337,21.79299Q21.072699999999998,21.84046,21.1161,21.88388Q21.1595,21.92731,21.207,21.96626Q21.2545,22.00522,21.305500000000002,22.03934Q21.3566,22.07345,21.410800000000002,22.1024Q21.4649,22.13135,21.5216,22.15485Q21.578400000000002,22.178350000000002,21.6371,22.196170000000002Q21.6959,22.214,21.7561,22.22598Q21.8164,22.23796,21.877499999999998,22.24398Q21.9386,22.25,22,22.25Q22.0614,22.25,22.122500000000002,22.24398Q22.1836,22.23796,22.2439,22.22598Q22.3041,22.214,22.3629,22.19618Q22.421599999999998,22.178350000000002,22.4784,22.15485Q22.5351,22.13135,22.589199999999998,22.1024Q22.6434,22.07345,22.694499999999998,22.03934Q22.7455,22.00522,22.793,21.96626Q22.8405,21.92731,22.8839,21.88388Q22.927300000000002,21.84046,22.9663,21.79299Q23.005200000000002,21.74552,23.0393,21.69446Q23.0735,21.6434,23.1024,21.58925Q23.1313,21.53509,23.1548,21.47835Q23.1783,21.42162,23.196199999999997,21.36286Q23.214,21.30409,23.226,21.24386Q23.238,21.18363,23.244,21.12252Q23.25,21.06141,23.25,21L23.25,20.99655Q23.2488,19.9603,22.5556,19.06628Q21.978099999999998,18.32156,20.987650000000002,17.78131Q19.09692,16.75,16.5,16.75Q13.90308,16.75,12.01235,17.78131Q11.0218892,18.32156,10.444406,19.06628Z"
        fillRule="evenodd"
        fill="currentColor"
        fillOpacity="1"
      />
    </svg>
  );
};

export default PersonalIcon;
