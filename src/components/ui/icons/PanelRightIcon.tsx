import React from "react";

interface PanelRightIconProps {
  size?: number;
  className?: string;
  isCollapsed?: boolean;
}

export const PanelRightIcon: React.FC<PanelRightIconProps> = ({
  size = 18,
  className = "",
  isCollapsed = false,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`transition-all duration-300 ease-in-out ${className}`}
    >
      <rect width="18" height="18" x="3" y="3" rx="2" />
      {isCollapsed ? (
        <line
          x1="15"
          y1="3"
          x2="15"
          y2="21"
          className="transition-opacity duration-300 ease-in-out"
        />
      ) : (
        <>
          <path
            d="M15 3v18"
            className="transition-opacity duration-300 ease-in-out"
          />
          <path
            d="M15 3h6v18h-6z"
            fill="currentColor"
            stroke="none"
            className="transition-all duration-300 ease-in-out"
          />
        </>
      )}
    </svg>
  );
};

export default PanelRightIcon;
