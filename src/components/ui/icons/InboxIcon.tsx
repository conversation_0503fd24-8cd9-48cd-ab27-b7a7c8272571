import React from "react";

interface InboxIconProps {
  size?: number;
  className?: string;
}

export const InboxIcon: React.FC<InboxIconProps> = ({
  size = 18,
  className = "",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
    >
      <path
        d="M21.0779,5.44805Q19.5772,2.75,16.4899,2.75L7.51014,2.75Q4.42282,2.75,2.922111,5.44804L1.411969,8.16305Q0.75,9.35317,0.75,10.71501L0.75,16Q0.75,18.174599999999998,2.28769,19.7123Q3.82538,21.25,6,21.25L18,21.25Q20.1746,21.25,21.7123,19.7123Q23.25,18.174599999999998,23.25,16L23.25,10.71501Q23.25,9.35317,22.588,8.16305L21.0779,5.44805ZM7.51014,5.25L16.4899,5.25Q18.107,5.25,18.8931,6.66326L20.4033,9.37827Q20.75,10.001660000000001,20.75,10.71501L20.75,16Q20.75,18.75,18,18.75L6,18.75Q3.25,18.75,3.25,16L3.25,10.71501Q3.25,10.00167,3.59675,9.37827L5.10689,6.66326Q5.89297,5.25,7.51014,5.25Z"
        fillRule="evenodd"
        fill="currentColor"
        />
        <path
          d="M17.355600000000003,12.25L21.4444,12.25Q21.5059,12.25,21.567,12.24398Q21.6281,12.23796,21.6883,12.22598Q21.7485,12.214,21.8073,12.19618Q21.8661,12.17835,21.9228,12.15485Q21.9795,12.13135,22.0337,12.1024Q22.0878,12.07345,22.1389,12.03934Q22.19,12.00522,22.2374,11.966263Q22.2849,11.927306,22.3283,11.883883Q22.3717,11.840461,22.4107,11.792992Q22.4497,11.745522,22.4838,11.694463Q22.5179,11.643403,22.5468,11.589246Q22.5758,11.535088,22.5993,11.478354Q22.6228,11.42162,22.6406,11.362856Q22.6584,11.304091,22.6704,11.243863Q22.6824,11.183634,22.6884,11.122521Q22.6944,11.0614086,22.6944,11Q22.6944,10.9385914,22.6884,10.877479Q22.6824,10.816366,22.6704,10.756137Q22.6584,10.695909,22.6406,10.637144Q22.6228,10.57838,22.5993,10.521646Q22.5758,10.464912,22.5468,10.410754Q22.5179,10.356597,22.4838,10.305537Q22.4497,10.254478,22.4107,10.207008Q22.3717,10.159539,22.3283,10.116117Q22.2849,10.072694,22.2374,10.033737Q22.19,9.99478,22.1389,9.96066Q22.0878,9.92655,22.0337,9.8976Q21.9795,9.86865,21.9228,9.84515Q21.8661,9.82165,21.8073,9.80382Q21.7485,9.786,21.6883,9.77402Q21.6281,9.76204,21.567,9.75602Q21.5059,9.75,21.4444,9.75L17.355600000000003,9.75Q16.5131,9.75,15.7648,10.110184Q14.9953,10.48061,14.5024,11.145744L13.5994,12.36675Q13.3152,12.750070000000001,12.7557,12.75L10.68889,12.75Q10.12905,12.75,9.84429,12.36575L8.94281,11.146754Q8.4489,10.480492,7.67946,10.11011Q6.93114,9.74989,6.08872,9.75L2,9.75Q1.9385914,9.75,1.877479,9.75602Q1.816366,9.76204,1.756137,9.77402Q1.6959089999999999,9.786,1.637144,9.80382Q1.5783800000000001,9.82165,1.521646,9.84515Q1.464912,9.86865,1.4107539999999998,9.8976Q1.356597,9.92655,1.305537,9.96066Q1.254478,9.99478,1.207008,10.033737Q1.159539,10.072694,1.116117,10.116117Q1.072694,10.159539,1.033737,10.207008Q0.99478,10.254478,0.9606600000000001,10.305537Q0.92655,10.356597,0.8976,10.410754Q0.8686499999999999,10.464912,0.8451500000000001,10.521646Q0.82165,10.57838,0.80382,10.637144Q0.786,10.695909,0.7740199999999999,10.756137Q0.76204,10.816366,0.7560199999999999,10.877479Q0.75,10.9385914,0.75,11Q0.75,11.0614086,0.7560199999999999,11.122521Q0.76204,11.183634,0.7740199999999999,11.243863Q0.786,11.304091,0.80382,11.362856Q0.82165,11.42162,0.8451500000000001,11.478354Q0.8686499999999999,11.535088,0.8976,11.589246Q0.92655,11.643403,0.9606600000000001,11.694463Q0.99478,11.745522,1.033737,11.792992Q1.072694,11.840461,1.116117,11.883883Q1.159539,11.927306,1.207008,11.966263Q1.254478,12.00522,1.305537,12.03934Q1.356597,12.07345,1.4107539999999998,12.1024Q1.464912,12.13135,1.521646,12.15485Q1.5783800000000001,12.17835,1.637144,12.19618Q1.6959089999999999,12.214,1.756137,12.22598Q1.816366,12.23796,1.877479,12.24398Q1.9385914,12.25,2,12.25L6.08906,12.25Q6.64858,12.249929999999999,6.93275,12.63325L7.83572,13.85425Q8.32863,14.51939,9.09816,14.88982Q9.846409999999999,15.25,10.68889,15.25L12.7554,15.25Q13.5978,15.25011,14.3461,14.88989Q15.1156,14.51951,15.6095,13.85325L16.5109,12.63425Q16.7957,12.25,17.355600000000003,12.25Z"
          fillRule="evenodd"
          fill="currentColor"
        />
        <path
          d="M9,6.75L15,6.75Q15.06141,6.75,15.12252,6.7560199999999995Q15.18363,6.76204,15.24386,6.77402Q15.30409,6.786,15.362860000000001,6.80382Q15.42162,6.82165,15.478349999999999,6.84515Q15.53509,6.86865,15.58925,6.8976Q15.6434,6.92655,15.69446,6.96066Q15.745519999999999,6.99478,15.79299,7.033737Q15.84046,7.072694,15.883880000000001,7.116117Q15.92731,7.159539,15.96626,7.207008Q16.00522,7.254478,16.03934,7.305537Q16.07345,7.356597,16.1024,7.410754Q16.13135,7.464912,16.15485,7.521646Q16.178350000000002,7.57838,16.19618,7.637144Q16.214,7.695909,16.22598,7.756137Q16.23796,7.816366,16.24398,7.877479Q16.25,7.9385914,16.25,8Q16.25,8.0614086,16.24398,8.122521Q16.23796,8.183634,16.22598,8.243863Q16.214,8.304091,16.19618,8.362856Q16.178350000000002,8.42162,16.15485,8.478354Q16.13135,8.535088,16.1024,8.589246Q16.07345,8.643403,16.03934,8.694463Q16.00522,8.745522,15.96626,8.792992Q15.92731,8.840461,15.883880000000001,8.883883Q15.84046,8.927306,15.79299,8.966263Q15.745519999999999,9.00522,15.69446,9.03934Q15.6434,9.07345,15.58925,9.1024Q15.53509,9.13135,15.478349999999999,9.15485Q15.42162,9.17835,15.362860000000001,9.19618Q15.30409,9.214,15.24386,9.22598Q15.18363,9.23796,15.12252,9.24398Q15.06141,9.25,15,9.25L9,9.25Q8.9385914,9.25,8.877479,9.24398Q8.816366,9.23796,8.756137,9.22598Q8.695909,9.214,8.637144,9.19618Q8.57838,9.17835,8.521646,9.15485Q8.464912,9.13135,8.410754,9.1024Q8.356597,9.07345,8.305537,9.03934Q8.254478,9.00522,8.207008,8.966263Q8.159539,8.927306,8.116117,8.883883Q8.072694,8.840461,8.033737,8.792992Q7.99478,8.745522,7.96066,8.694463Q7.92655,8.643403,7.8976,8.589246Q7.86865,8.535088,7.84515,8.478354Q7.82165,8.42162,7.80382,8.362856Q7.786,8.304091,7.77402,8.243863Q7.76204,8.183634,7.7560199999999995,8.122521Q7.75,8.0614086,7.75,8Q7.75,7.9385914,7.7560199999999995,7.877479Q7.76204,7.816366,7.77402,7.756137Q7.786,7.695909,7.80382,7.637144Q7.82165,7.57838,7.84515,7.521646Q7.86865,7.464912,7.8976,7.410754Q7.92655,7.356597,7.96066,7.305537Q7.99478,7.254478,8.033737,7.207008Q8.072694,7.159539,8.116117,7.116117Q8.159539,7.072694,8.207008,7.033737Q8.254478,6.99478,8.305537,6.96066Q8.356597,6.92655,8.410754,6.8976Q8.464912,6.86865,8.521646,6.84515Q8.57838,6.82165,8.637144,6.80382Q8.695909,6.786,8.756137,6.77402Q8.816366,6.76204,8.877479,6.7560199999999995Q8.9385914,6.75,9,6.75Z"
          fillRule="evenodd"
          fill="currentColor"
        />
    </svg>
  );
};

export default InboxIcon;
