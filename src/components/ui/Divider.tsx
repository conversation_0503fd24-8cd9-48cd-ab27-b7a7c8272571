import React, { useState, forwardRef } from "react";

interface DividerProps {
  onDragStart: (e: React.MouseEvent) => void;
  onContextMenu: (e: React.MouseEvent) => void;
  isDragging: boolean;
  isResetting: boolean;
  isMenuOpen: boolean;
  className?: string;
}

/**
 * 可调整宽度的分隔条组件
 */
const Divider = forwardRef<HTMLDivElement, DividerProps>(
  (
    {
      onDragStart,
      onContextMenu,
      isDragging,
      isResetting,
      isMenuOpen,
      className = "",
    },
    ref
  ) => {
    const [isHovered, setIsHovered] = useState(false);
    const [isDividerHovered, setIsDividerHovered] = useState(false);

    return (
      <div
        ref={ref}
        className={`flex items-center justify-center w-6 -mx-3 z-10 cursor-col-resize ${className}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => {
          setIsHovered(false);
          setIsDividerHovered(false);
        }}
        onMouseDown={onDragStart}
        onContextMenu={onContextMenu}
      >
        <div
          className={`w-1 h-20 rounded-full transition-all duration-200 ${
            isDragging
              ? "bg-secondaryBtn opacity-50"
              : isResetting || isMenuOpen
              ? "bg-secondaryBtn opacity-50"
              : isDividerHovered
              ? "bg-secondaryBtn opacity-50"
              : isHovered
              ? "bg-secondaryBtn/10 opacity-100"
              : "bg-secondaryBtn/10 opacity-0"
          }`}
          onMouseEnter={() => setIsDividerHovered(true)}
          onMouseLeave={() => setIsDividerHovered(false)}
        ></div>
      </div>
    );
  }
);

Divider.displayName = "Divider";

export default Divider;
