import React from "react";
import { motion } from "framer-motion";

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger" | "ghost";
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  type?: "button" | "submit" | "reset";

  // 宽度控制
  fullWidth?: boolean;
  minWidth?: boolean;

  // 快捷键支持
  shortcut?: string;
  showShortcut?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
  loading = false,
  className = "",
  type = "button",
  fullWidth = false,
  minWidth = true,
  shortcut,
  showShortcut = true,
}) => {
  const baseClasses =
    "inline-flex items-center justify-center font-semibold rounded-lg transition-colors focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed";

  const variantClasses = {
    primary:
      "border border-transparent bg-primaryBtn hover:bg-primaryBtn/90 text-white focus:ring-blue-500",
    secondary:
      "border border-secondaryBtn/10 hover:border-secondaryBtn/15 bg-transparent hover:bg-backgroundDeep/30 text-gray-700 dark:text-gray-300 focus:ring-gray-500",
    danger:
      "border border-transparent bg-red-500/95 hover:bg-red-600/95 text-white focus:ring-red-500",
    ghost:
      "border border-transparent hover:bg-gray-100 text-gray-700 dark:hover:bg-gray-800 dark:text-gray-300 focus:ring-gray-500",
  };

  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-1.5 text-sm",
    lg: "px-6 py-3 text-base",
  };

  // 宽度控制
  const widthClasses = fullWidth
    ? "w-full"
    : minWidth
    ? "min-w-[100px] justify-center"
    : "";

  const combinedClasses =
    `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClasses} ${className}`.trim();

  return (
    <motion.button
      type={type}
      className={combinedClasses}
      onClick={onClick}
      disabled={disabled || loading}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      transition={{ duration: 0.1 }}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      )}

      {/* 按钮内容 */}
      <span className="flex items-center justify-center w-full">
        <span>{children}</span>
        {shortcut && showShortcut && (
          <span className="ml-2 flex items-center gap-0.5">
            {(() => {
              // 智能分割快捷键，支持特殊符号和单词
              const keys = shortcut.match(
                /⌘|⌥|⌃|⇧|↵|Cmd|Alt|Ctrl|Shift|Enter|Esc|Tab|Space|[A-Za-z0-9]|./g
              ) || [shortcut];

              // 检测操作系统
              const isMac =
                typeof window !== "undefined" &&
                (window.navigator.userAgent.includes("Mac") ||
                  window.navigator.userAgent.includes("iPhone") ||
                  window.navigator.userAgent.includes("iPad"));

              // 快捷键符号映射
              const getKeyDisplay = (key: string) => {
                switch (key) {
                  case "⌘":
                  case "Cmd":
                    return isMac ? (
                      <svg
                        viewBox="0 0 24 24"
                        width="12"
                        height="12"
                        fill="currentColor"
                      >
                        <path d="M10 8H14V6.5C14 4.567 15.567 3 17.5 3C19.433 3 21 4.567 21 6.5C21 8.433 19.433 10 17.5 10H16V14H17.5C19.433 14 21 15.567 21 17.5C21 19.433 19.433 21 17.5 21C15.567 21 14 19.433 14 17.5V16H10V17.5C10 19.433 8.433 21 6.5 21C4.567 21 3 19.433 3 17.5C3 15.567 4.567 14 6.5 14H8V10H6.5C4.567 10 3 8.433 3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5V8ZM8 8V6.5C8 5.67157 7.32843 5 6.5 5C5.67157 5 5 5.67157 5 6.5C5 7.32843 5.67157 8 6.5 8H8ZM8 16H6.5C5.67157 16 5 16.6716 5 17.5C5 18.3284 5.67157 19 6.5 19C7.32843 19 8 18.3284 8 17.5V16ZM16 8H17.5C18.3284 8 19 7.32843 19 6.5C19 5.67157 18.3284 5 17.5 5C16.6716 5 16 5.67157 16 6.5V8ZM16 16V17.5C16 18.3284 16.6716 19 17.5 19C18.3284 19 19 18.3284 19 17.5C19 16.6716 18.3284 16 17.5 16H16ZM10 10V14H14V10H10Z" />
                      </svg>
                    ) : (
                      <span className="font-mono text-xs leading-none font-semibold">
                        Ctrl
                      </span>
                    );
                  case "↵":
                  case "Enter":
                    return (
                      <svg
                        viewBox="0 0 24 24"
                        width="12"
                        height="12"
                        fill="currentColor"
                      >
                        <path d="M19.0001 13.9999L19.0002 5L17.0002 4.99997L17.0001 11.9999L6.8283 12L10.778 8.05024L9.36382 6.63603L2.99986 13L9.36382 19.364L10.778 17.9497L6.82826 14L19.0001 13.9999Z" />
                      </svg>
                    );
                  case "⌥":
                  case "Alt":
                    return isMac ? (
                      <span className="font-mono text-xs leading-none">⌥</span>
                    ) : (
                      <span className="font-mono text-xs leading-none">
                        Alt
                      </span>
                    );
                  case "⇧":
                  case "Shift":
                    return (
                      <svg
                        viewBox="0 0 24 24"
                        width="12"
                        height="12"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 4L18 10H14V20H10V10H6L12 4Z" />
                      </svg>
                    );
                  case "⌃":
                  case "Ctrl":
                    return isMac ? (
                      <span className="font-mono text-xs leading-none">⌃</span>
                    ) : (
                      <span className="font-mono text-xs leading-none">
                        Ctrl
                      </span>
                    );
                  default:
                    return (
                      <span className="font-mono text-xs leading-none">
                        {key}
                      </span>
                    );
                }
              };

              return keys.map((key, index) => (
                <span
                  key={index}
                  className="w-4 h-4 bg-white/10 text-white/70 rounded-[3px] flex items-center justify-center"
                >
                  {getKeyDisplay(key)}
                </span>
              ));
            })()}
          </span>
        )}
      </span>
    </motion.button>
  );
};

export default Button;
