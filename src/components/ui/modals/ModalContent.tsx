import React from "react";

interface ModalContentProps {
  children: React.ReactNode;
  className?: string;
  padding?: "none" | "sm" | "md" | "lg";
  maxHeight?: boolean;
}

const ModalContent: React.FC<ModalContentProps> = ({
  children,
  className = "",
  padding = "md",
  maxHeight = false,
}) => {
  const paddingClasses = {
    none: "",
    sm: "p-2",
    md: "p-4",
    lg: "p-6",
  };

  const baseClasses = "text-gray-700 dark:text-gray-300";
  const heightClasses = maxHeight ? "max-h-96 overflow-y-auto" : "";
  const combinedClasses = `${baseClasses} ${paddingClasses[padding]} ${heightClasses} ${className}`.trim();

  return (
    <div className={combinedClasses}>
      {children}
    </div>
  );
};

export default ModalContent;