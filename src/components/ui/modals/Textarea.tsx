import React, { forwardRef } from "react";

interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  isInvalid?: boolean;
  errorMessage?: string;
  variant?: "default" | "filled";
  size?: "sm" | "md" | "lg";
  fullWidth?: boolean;
  resize?: "none" | "vertical" | "horizontal" | "both";
  fixedHeight?: boolean;
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      error,
      helperText,
      isInvalid = false,
      errorMessage,
      variant = "filled",
      size = "md",
      fullWidth = true,
      resize = "none",
      fixedHeight = true,
      className = "",
      id,
      rows = 4,
      ...props
    },
    ref
  ) => {
    const textareaId =
      id || `textarea-${Math.random().toString(36).substring(2, 11)}`;
    const hasError = isInvalid || !!error || !!errorMessage;
    const displayError = error || errorMessage;

    const sizeClasses = {
      sm: "px-3 py-2 text-sm",
      md: "px-4 py-3 text-sm",
      lg: "px-4 py-3 text-sm",
    };

    const resizeClasses = {
      none: "resize-none",
      vertical: "resize-y",
      horizontal: "resize-x",
      both: "resize",
    };

    // 当 fixedHeight 为 true 时，强制禁用调整大小并启用滚动
    const finalResize = fixedHeight ? "none" : resize;
    const scrollClasses = fixedHeight ? "overflow-y-auto normal-scrollbar" : "";

    const baseClasses = `
      w-full rounded-lg transition-colors duration-200 
      placeholder:text-gray-400 dark:placeholder:text-gray-500
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
      disabled:opacity-50 disabled:cursor-not-allowed
    `.trim();

    const variantClasses = {
      default: `
        border border-gray-300 dark:border-gray-600 
        bg-white dark:bg-gray-900 
        text-gray-900 dark:text-white
        hover:border-gray-400 dark:hover:border-gray-500
      `,
      filled: `
        border-0
        bg-backgroundDeep/50
        text-gray-900 dark:text-white
        focus:bg-backgroundDeep/90
      `,
    };

    const errorClasses = hasError
      ? "ring-2 ring-red-500 ring-opacity-50 border-red-500 dark:border-red-500"
      : "";

    const combinedClasses = `
      ${baseClasses}
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${resizeClasses[finalResize]}
      ${scrollClasses}
      ${errorClasses}
      ${fullWidth ? "w-full" : ""}
      ${className}
    `
      .replace(/\s+/g, " ")
      .trim();

    return (
      <div className={fullWidth ? "w-full" : ""}>
        {label && (
          <label
            htmlFor={textareaId}
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            {label}
          </label>
        )}

        <textarea
          ref={ref}
          id={textareaId}
          rows={rows}
          className={combinedClasses}
          {...props}
        />

        {(displayError || helperText) && (
          <div className="mt-2">
            {hasError && displayError && (
              <p className="text-sm text-red-600 dark:text-red-400">
                {displayError}
              </p>
            )}
            {!hasError && helperText && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = "Textarea";

export default Textarea;
