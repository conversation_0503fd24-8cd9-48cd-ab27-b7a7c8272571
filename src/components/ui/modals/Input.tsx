import React, { forwardRef } from "react";

interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size"> {
  label?: string;
  error?: string;
  helperText?: string;
  isInvalid?: boolean;
  errorMessage?: string;
  variant?: "default" | "filled";
  size?: "sm" | "md" | "lg";
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      isInvalid = false,
      errorMessage,
      variant = "filled",
      size = "md",
      fullWidth = true,
      className = "",
      id,
      ...props
    },
    ref
  ) => {
    const inputId =
      id || `input-${Math.random().toString(36).substring(2, 11)}`;
    const hasError = isInvalid || !!error || !!errorMessage;
    const displayError = error || errorMessage;

    const sizeClasses = {
      sm: "px-3 py-2 text-sm",
      md: "px-4 py-3 text-sm",
      lg: "px-4 py-3 text-sm",
    };

    const baseClasses = `
      w-full rounded-lg transition-colors duration-200 
      placeholder:text-gray-400 dark:placeholder:text-gray-500
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
      disabled:opacity-50 disabled:cursor-not-allowed
    `.trim();

    const variantClasses = {
      default: `
        border border-gray-300 dark:border-gray-600 
        bg-white dark:bg-gray-900 
        text-gray-900 dark:text-white
        hover:border-gray-400 dark:hover:border-gray-500
      `,
      filled: `
        border-0
        bg-backgroundDeep/50
        text-gray-900 dark:text-white
        focus:bg-backgroundDeep/90
      `,
    };

    const errorClasses = hasError
      ? "ring-2 ring-red-500 ring-opacity-50 border-red-500 dark:border-red-500"
      : "";

    const combinedClasses = `
      ${baseClasses} 
      ${variantClasses[variant]} 
      ${sizeClasses[size]} 
      ${errorClasses} 
      ${fullWidth ? "w-full" : ""} 
      ${className}
    `
      .replace(/\s+/g, " ")
      .trim();

    return (
      <div className={fullWidth ? "w-full" : ""}>
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            {label}
          </label>
        )}

        <input ref={ref} id={inputId} className={combinedClasses} {...props} />

        {(displayError || helperText) && (
          <div className="mt-2">
            {hasError && displayError && (
              <p className="text-sm text-red-600 dark:text-red-400">
                {displayError}
              </p>
            )}
            {!hasError && helperText && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
