import React, { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import Button from "./Button";

// 标准化的模态框变体
type ModalVariant = "form" | "confirm" | "info";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;

  // 模态框变体 - 决定按钮配置和行为
  variant?: ModalVariant;

  // 表单模态框配置 (variant="form")
  onSave?: () => void;
  saveText?: string;
  isSaveDisabled?: boolean;
  isSaveLoading?: boolean;

  // 确认模态框配置 (variant="confirm")
  onConfirm?: () => void;
  confirmText?: string;
  confirmColor?: "primary" | "danger" | "warning";
  isConfirmDisabled?: boolean;
  isConfirmLoading?: boolean;

  // 通用配置
  cancelText?: string;
  size?: "sm" | "md" | "lg" | "xl";
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;

  // 向后兼容：自定义 actions（优先级最高）
  actions?: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  variant = "info",
  onSave,
  saveText = "Save",
  isSaveDisabled = false,
  isSaveLoading = false,
  onConfirm,
  confirmText = "Confirm",
  confirmColor = "primary",
  isConfirmDisabled = false,
  isConfirmLoading = false,
  cancelText = "Cancel",
  size = "md",
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  actions,
}) => {
  // 生成标准化的按钮组
  const renderActions = () => {
    // 如果提供了自定义 actions，优先使用
    if (actions) {
      return actions;
    }

    // 根据变体生成标准按钮
    switch (variant) {
      case "form":
        return (
          <>
            <Button variant="secondary" onClick={onClose} minWidth={true}>
              {cancelText}
            </Button>
            <Button
              variant="primary"
              onClick={onSave}
              disabled={isSaveDisabled}
              loading={isSaveLoading}
              minWidth={true}
              // shortcut="Cmd↵"
            >
              {saveText}
            </Button>
          </>
        );

      case "confirm":
        return (
          <>
            <Button variant="secondary" onClick={onClose} minWidth={true}>
              {cancelText}
            </Button>
            <Button
              variant={confirmColor === "danger" ? "danger" : "primary"}
              onClick={onConfirm}
              disabled={isConfirmDisabled}
              loading={isConfirmLoading}
              minWidth={true}
            >
              {confirmText}
            </Button>
          </>
        );

      case "info":
      default:
        return (
          <Button
            variant="primary"
            onClick={onClose}
            className="font-bold"
            minWidth={true}
          >
            OK
          </Button>
        );
    }
  };
  // Handle escape key press
  useEffect(() => {
    if (!closeOnEscape) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose, closeOnEscape]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const sizeClasses = {
    sm: "max-w-md",
    md: "max-w-lg",
    lg: "max-w-2xl",
    xl: "max-w-4xl",
  };

  const overlayVariants = {
    hidden: {
      opacity: 0,
    },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.15,
        ease: "easeIn",
      },
    },
  };

  const modalVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95,
      y: 20,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: 20,
      transition: {
        duration: 0.15,
        ease: "easeIn",
      },
    },
  };

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (closeOnOverlayClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          onClick={handleOverlayClick}
        >
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/50" />

          {/* Modal Content */}
          <motion.div
            className={`relative w-full ${sizeClasses[size]} bg-background dark:bg-gray-900 rounded-2xl shadow-xl overflow-hidden`}
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Header */}
            {(title || showCloseButton) && (
              <div className="flex items-center justify-between px-8 py-6">
                {title && (
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    {title}
                  </h2>
                )}
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    aria-label="Close modal"
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            )}

            {/* Content */}
            <div className="px-8 py-4">{children}</div>

            {/* Actions */}
            <div className="flex items-center justify-end gap-3 px-8 py-6">
              {renderActions()}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Modal;
