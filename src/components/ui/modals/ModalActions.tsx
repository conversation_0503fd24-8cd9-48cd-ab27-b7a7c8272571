import React from "react";

interface ModalActionsProps {
  children: React.ReactNode;
  className?: string;
  alignment?: "left" | "center" | "right" | "between";
  spacing?: "sm" | "md" | "lg";
}

const ModalActions: React.FC<ModalActionsProps> = ({
  children,
  className = "",
  alignment = "right",
  spacing = "md",
}) => {
  const alignmentClasses = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end",
    between: "justify-between",
  };

  const spacingClasses = {
    sm: "gap-2",
    md: "gap-3",
    lg: "gap-4",
  };

  const baseClasses = "flex items-center";
  const combinedClasses =
    `${baseClasses} ${alignmentClasses[alignment]} ${spacingClasses[spacing]} ${className}`.trim();

  return <div className={combinedClasses}>{children}</div>;
};

export default ModalActions;
