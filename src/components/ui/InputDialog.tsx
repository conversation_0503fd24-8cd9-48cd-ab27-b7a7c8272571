/**
 * 通用输入对话框组件
 * 用于新建文件夹、文件、重命名等操作
 */

import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@heroui/react";
import { X, AlertCircle } from "lucide-react";

interface InputDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (value: string) => void;
  title: string;
  placeholder?: string;
  defaultValue?: string;
  confirmText?: string;
  cancelText?: string;
  validator?: (value: string) => { isValid: boolean; error?: string };
  maxLength?: number;
}

const InputDialog: React.FC<InputDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  placeholder = "Enter name...",
  defaultValue = "",
  confirmText = "Create",
  cancelText = "Cancel",
  validator,
  maxLength = 255,
}) => {
  const [value, setValue] = useState(defaultValue);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 重置状态当对话框打开/关闭时
  useEffect(() => {
    if (isOpen) {
      setValue(defaultValue);
      setError("");
      setIsSubmitting(false);
      // 延迟聚焦以确保动画完成
      setTimeout(() => {
        inputRef.current?.focus();
        inputRef.current?.select();
      }, 100);
    }
  }, [isOpen, defaultValue]);

  // 验证输入值
  const validateInput = (inputValue: string): boolean => {
    if (validator) {
      const result = validator(inputValue);
      if (!result.isValid) {
        setError(result.error || "Invalid input");
        return false;
      }
    }
    setError("");
    return true;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);

    // 实时验证
    if (newValue.trim()) {
      validateInput(newValue.trim());
    } else {
      setError("");
    }
  };

  // 处理确认
  const handleConfirm = async () => {
    const trimmedValue = value.trim();

    if (!trimmedValue) {
      setError("Name cannot be empty");
      return;
    }

    if (!validateInput(trimmedValue)) {
      return;
    }

    setIsSubmitting(true);

    try {
      onConfirm(trimmedValue);
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isSubmitting) {
      e.preventDefault();
      handleConfirm();
    } else if (e.key === "Escape") {
      e.preventDefault();
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* 背景遮罩 */}
        <motion.div
          className="absolute inset-0 bg-black/50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        />

        {/* 对话框 */}
        <motion.div
          className="relative bg-background border border-border rounded-xl shadow-xl w-full max-w-md mx-4 overflow-hidden"
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 30,
          }}
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-border">
            <h2 className="text-lg font-semibold text-foreground">{title}</h2>
            <button
              onClick={onClose}
              className="p-1 rounded-lg hover:bg-foreground/5 transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-5 h-5 text-foreground/60" />
            </button>
          </div>

          {/* 内容 */}
          <div className="p-6 space-y-4">
            <div className="space-y-2">
              <input
                ref={inputRef}
                type="text"
                value={value}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                maxLength={maxLength}
                disabled={isSubmitting}
                className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                  error ? "border-red-500" : "border-border"
                }`}
              />

              {/* 错误提示 */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    className="flex items-center gap-2 text-sm text-red-500"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                  >
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    <span>{error}</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* 字符计数 */}
            <div className="text-xs text-foreground/50 text-right">
              {value.length}/{maxLength}
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-border bg-foreground/2">
            <Button
              variant="ghost"
              onPress={onClose}
              isDisabled={isSubmitting}
              className="text-foreground/70 hover:text-foreground"
            >
              {cancelText}
            </Button>
            <Button
              color="primary"
              onPress={handleConfirm}
              isDisabled={isSubmitting || !value.trim() || !!error}
              isLoading={isSubmitting}
            >
              {confirmText}
            </Button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default InputDialog;
