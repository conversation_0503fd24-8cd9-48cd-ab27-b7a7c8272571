"use client";

import { useAuthActions } from "@/hooks/useAuthActions";
import { LogIn, LogOut } from "lucide-react";

interface LoginButtonProps {
  className?: string;
  variant?: "default" | "outline" | "text";
  size?: "sm" | "md" | "lg";
}

export default function LoginButton({
  className = "",
  variant = "default",
  size = "md",
}: LoginButtonProps) {
  const { handleLogin, handleLogout, isAuthenticated, isLoginExpired } =
    useAuthActions();

  const baseStyles =
    "inline-flex items-center justify-center font-medium rounded-lg transition-colors duration-200";

  const variantStyles = {
    default:
      "text-white bg-primary hover:bg-primary/90 border border-transparent",
    outline: "text-primary border border-primary hover:bg-primary/10",
    text: "text-foreground/70 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-400/10",
  };

  const logoutVariantStyles = {
    default: "text-white bg-red-500 hover:bg-red-600 border border-transparent",
    outline: "text-red-500 border border-red-500 hover:bg-red-50",
    text: "text-foreground/70 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-400/10",
  };

  const sizeStyles = {
    sm: "px-3 py-1.5 text-xs",
    md: "px-4 py-2 text-sm",
    lg: "px-5 py-2.5 text-base",
  };

  if (isAuthenticated) {
    return (
      <button
        onClick={handleLogout}
        className={`${baseStyles} ${logoutVariantStyles[variant]} ${sizeStyles[size]} ${className}`}
      >
        <LogOut className="mr-2 h-4 w-4" />
        <span>退出登录</span>
      </button>
    );
  }

  return (
    <button
      onClick={handleLogin}
      className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`}
    >
      <LogIn className="mr-2 h-4 w-4" />
      <span>{isLoginExpired() ? "重新登录" : "登录"}</span>
    </button>
  );
}
