import Link from "next/link";

interface NotFoundUIProps {
  /**
   * 错误页面的标题
   */
  title?: string;
  /**
   * 错误页面的描述
   */
  description?: string;
  /**
   * 返回链接的文本
   */
  linkText?: string;
  /**
   * 返回链接的目标路径
   */
  linkHref?: string;
}

/**
 * 通用的 404 页面 UI 组件
 * 可以在全局 404 页面和特定场景（如项目不存在）下使用
 */
export default function NotFoundUI({
  title = "Page Not Found",
  description = "The page you are looking for does not exist or may have been moved.",
  linkText = "Back to Home",
  linkHref = "/",
}: NotFoundUIProps) {
  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center bg-backgroundDeep">
      <div className="max-w-[256px] text-center">
        <h1 className="text-xl font-bold text-foreground mb-4">{title}</h1>
        <p className="text-secondaryBtn/60 mb-12 text-sm">{description}</p>
        <Link
          href={linkHref}
          className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium bg-secondaryBtn text-secondaryBtn-text rounded-lg hover:bg-secondaryBtn/90 transition-colors"
        >
          {linkText}
        </Link>
      </div>
    </div>
  );
}
