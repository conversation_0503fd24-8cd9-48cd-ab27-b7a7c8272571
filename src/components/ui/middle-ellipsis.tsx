import React from "react";

interface MiddleEllipsisProps {
  text: string;
  maxLength?: number;
  className?: string;
  startChars?: number;
  endChars?: number;
  title?: string;
  /**
   * 中文字符的宽度比例，相对于英文字符
   * 默认为1.8，表示一个中文字符的宽度约等于1.8个英文字符
   */
  chineseWidthRatio?: number;
}

/**
 * 判断字符是否为中文字符（包括中文标点符号）
 */
const isChineseChar = (char: string): boolean => {
  const code = char.charCodeAt(0);
  return (
    (code >= 0x4e00 && code <= 0x9fff) || // CJK统一汉字
    (code >= 0x3000 && code <= 0x303f) || // CJK标点符号
    (code >= 0xff00 && code <= 0xffef) // 全角ASCII、全角标点
  );
};

/**
 * 计算字符串的视觉宽度
 */
const getVisualWidth = (str: string, chineseWidthRatio: number): number => {
  let width = 0;
  for (let i = 0; i < str.length; i++) {
    width += isChineseChar(str[i]) ? chineseWidthRatio : 1;
  }
  return width;
};

/**
 * 截取指定视觉宽度的字符串
 * @param str 原始字符串
 * @param maxWidth 最大视觉宽度
 * @param fromStart 是否从开头截取（false则从结尾截取）
 * @param chineseWidthRatio 中文字符的宽度比例
 */
const truncateToVisualWidth = (
  str: string,
  maxWidth: number,
  fromStart: boolean = true,
  chineseWidthRatio: number
): string => {
  let width = 0;
  let result = "";

  if (fromStart) {
    // 从开头截取
    for (let i = 0; i < str.length; i++) {
      const charWidth = isChineseChar(str[i]) ? chineseWidthRatio : 1;
      if (width + charWidth <= maxWidth) {
        width += charWidth;
        result += str[i];
      } else {
        break;
      }
    }
    return result;
  } else {
    // 从结尾截取
    for (let i = str.length - 1; i >= 0; i--) {
      const charWidth = isChineseChar(str[i]) ? chineseWidthRatio : 1;
      if (width + charWidth <= maxWidth) {
        width += charWidth;
        result = str[i] + result;
      } else {
        break;
      }
    }
    return result;
  }
};

/**
 * MiddleEllipsis 组件用于在文本过长时在中间显示省略号，保留首尾部分
 * 考虑了中文字符的宽度
 *
 * @param text 需要显示的文本
 * @param maxLength 文本的最大视觉长度，超过则显示省略号
 * @param className 自定义样式类
 * @param startChars 开头保留的视觉宽度
 * @param endChars 结尾保留的视觉宽度
 * @param title 鼠标悬停时显示的完整文本
 * @param chineseWidthRatio 中文字符的宽度比例，默认为1.8
 */
export const MiddleEllipsis: React.FC<MiddleEllipsisProps> = ({
  text,
  maxLength = 24,
  className = "",
  startChars = 8,
  endChars = 8,
  title,
  chineseWidthRatio = 1.8,
}) => {
  // 如果文本为空，直接返回空组件
  if (!text) {
    return <span className={className}></span>;
  }

  // 计算文本的视觉宽度
  const textVisualWidth = getVisualWidth(text, chineseWidthRatio);

  // 如果文本视觉宽度小于最大长度，直接显示
  if (textVisualWidth <= maxLength) {
    return <span className={className}>{text}</span>;
  }

  // 计算实际可用的视觉宽度（考虑省略号占用的空间）
  const ellipsisLength = 3; // 省略号的长度
  const availableWidth = Math.max(maxLength - ellipsisLength, 2); // 确保至少有2个单位宽度可用

  // 如果 startChars + endChars 超过了可用宽度，按比例分配
  let adjustedStartWidth = startChars;
  let adjustedEndWidth = endChars;

  if (startChars + endChars > availableWidth) {
    // 按照原始比例分配可用宽度
    const ratio = startChars / (startChars + endChars);
    adjustedStartWidth = Math.max(Math.floor(availableWidth * ratio), 1);
    adjustedEndWidth = Math.max(availableWidth - adjustedStartWidth, 1);
  }

  // 计算中间省略部分
  const start = truncateToVisualWidth(
    text,
    adjustedStartWidth,
    true,
    chineseWidthRatio
  );
  const end = truncateToVisualWidth(
    text,
    adjustedEndWidth,
    false,
    chineseWidthRatio
  );

  return (
    <span className={className} title={title || text}>
      {start}
      <span className="opacity-60">...</span>
      {end}
    </span>
  );
};

export default MiddleEllipsis;
