import React, { useEffect, useState, createContext, useContext } from "react";

// 创建全局扫描位置上下文
interface ScanPositionContextType {
  scanPosition: number;
}

const ScanPositionContext = createContext<ScanPositionContextType | null>(null);

// 提供扫描位置的Provider组件
export const SkeletonProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [scanPosition, setScanPosition] = useState(-30);

  // 添加从左到右的扫描动画
  useEffect(() => {
    const scanAnimation = setInterval(() => {
      setScanPosition((prevPosition) => {
        // 当扫描完成后重置位置
        if (prevPosition > 120) {
          return -30;
        }
        return prevPosition + 2; // 控制扫描速度
      });
    }, 16); // 约60fps的更新频率

    return () => clearInterval(scanAnimation);
  }, []);

  return (
    <ScanPositionContext.Provider value={{ scanPosition }}>
      {children}
    </ScanPositionContext.Provider>
  );
};

// 使用扫描位置的Hook
const useScanPosition = (): ScanPositionContextType => {
  const context = useContext(ScanPositionContext);
  if (!context) {
    // 如果没有Provider，则使用本地状态
    const [scanPosition, setScanPosition] = useState(-30);

    useEffect(() => {
      const scanAnimation = setInterval(() => {
        setScanPosition((prevPosition) => {
          if (prevPosition > 120) {
            return -30;
          }
          return prevPosition + 2;
        });
      }, 16);

      return () => clearInterval(scanAnimation);
    }, []);

    return { scanPosition };
  }
  return context;
};

interface SkeletonProps {
  className?: string;
  children?: React.ReactNode;
}

const Skeleton: React.FC<SkeletonProps> = ({ className = "", children }) => {
  const { scanPosition } = useScanPosition();

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {children}
      <div
        className="absolute top-0 bottom-0 w-[30%] bg-gradient-to-r from-transparent via-background/40 to-transparent"
        style={{ left: `${scanPosition}%` }}
      />
    </div>
  );
};

export default Skeleton;
