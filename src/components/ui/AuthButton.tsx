"use client";

import React from "react";
import { useAuth } from "@quote/auth-client/react";
import { Button } from "@heroui/react";
import { showSuccessToast, showErrorToast } from "../../utils/toast";

export default function AuthButton() {
  const { isAuthenticated, user, loading, login, logout } = useAuth();

  const handleLogin = async () => {
    try {
      await login();
    } catch (error) {
      showErrorToast("登录失败");
      console.error("登录失败:", error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      showSuccessToast("已成功退出登录");
    } catch (error) {
      showErrorToast("退出登录失败");
      console.error("退出登录失败:", error);
    }
  };

  if (loading) {
    return (
      <Button isLoading variant="flat" size="sm">
        加载中...
      </Button>
    );
  }

  if (isAuthenticated) {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm">欢迎，{user?.email}</span>
        <Button variant="flat" color="danger" size="sm" onClick={handleLogout}>
          退出
        </Button>
      </div>
    );
  }

  return (
    <Button variant="flat" color="primary" size="sm" onClick={handleLogin}>
      登录
    </Button>
  );
}
