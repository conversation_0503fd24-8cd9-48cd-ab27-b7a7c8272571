"use client";

import React, { useRef, useEffect, useState } from "react";
import { motion } from "framer-motion";
import { ProjectFilterType } from "../types";

interface FilterTabsProps {
  activeFilter: ProjectFilterType;
  onFilterChange: (filter: ProjectFilterType) => void;
}

/**
 * 项目筛选标签页组件
 * 负责基本的项目类型筛选（全部、最近）
 */
const FilterTabs: React.FC<FilterTabsProps> = ({
  activeFilter = "all",
  onFilterChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<Record<ProjectFilterType, HTMLButtonElement | null>>({
    all: null,
    recent: null,
  });

  // 筛选选项配置
  const filterOptions: { key: ProjectFilterType; label: string }[] = [
    { key: "all", label: "All" },
    { key: "recent", label: "Recent" },
  ];

  // 计算初始背景位置和尺寸
  const getInitialDimensions = () => {
    return { left: 0, width: 40 }; // 默认值
  };

  const [tabDimensions, setTabDimensions] = useState(getInitialDimensions());

  // 在组件挂载和窗口大小变化时更新实际尺寸
  useEffect(() => {
    const updateTabDimensions = () => {
      const activeTab = tabRefs.current[activeFilter];
      if (activeTab) {
        const { offsetLeft, offsetWidth } = activeTab;
        setTabDimensions({
          left: offsetLeft,
          width: offsetWidth,
        });
      }
    };

    // 立即执行一次，然后添加窗口大小变化监听
    updateTabDimensions();
    window.addEventListener("resize", updateTabDimensions);

    return () => {
      window.removeEventListener("resize", updateTabDimensions);
    };
  }, [activeFilter]);

  return (
    <div className="h-full flex items-center relative" ref={containerRef}>
      <div className="flex h-full items-center gap-2 relative">
        {/* 动画背景 - 立即显示，不等待初始化 */}
        <motion.div
          className="absolute rounded-lg bg-secondaryBtn h-full"
          initial={false}
          animate={{
            x: tabDimensions.left,
            width: tabDimensions.width,
          }}
          transition={{
            type: "spring",
            stiffness: 500,
            damping: 40,
          }}
        />

        {filterOptions.map((option) => (
          <button
            key={option.key}
            ref={(el) => {
              tabRefs.current[option.key] = el;
            }}
            type="button"
            onClick={() => onFilterChange(option.key)}
            className={`h-full px-3 py-1 rounded-lg transition-colors duration-300 text-[13px] font-medium relative z-10 ${
              activeFilter === option.key
                ? "text-secondaryBtn-text"
                : "text-secondaryBtn/60 bg-secondaryBtn/5 hover:text-secondaryBtn hover:bg-secondaryBtn/10"
            }`}
            aria-selected={activeFilter === option.key}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default FilterTabs;
