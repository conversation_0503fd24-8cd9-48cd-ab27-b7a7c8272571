"use client";

import React, { useState, useRef, useEffect } from "react";
import { Search, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export interface SearchInputProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
  className?: string;
}

/**
 * 项目搜索输入组件
 * 负责项目关键词搜索
 * 未激活时只显示搜索图标，点击后动态展开输入框
 */
const SearchInput: React.FC<SearchInputProps> = ({
  searchQuery = "",
  onSearchChange,
  placeholder = "Search projects...",
  className = "",
}) => {
  const [isActive, setIsActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  };

  const handleActivate = () => {
    setIsActive(true);
    // 使用setTimeout确保在动画开始后再聚焦
    setTimeout(() => {
      inputRef.current?.focus();
    }, 10);
  };

  const handleBlur = () => {
    if (searchQuery === "") {
      setIsActive(false);
    }
  };

  const handleClear = () => {
    onSearchChange("");
    inputRef.current?.focus();
  };

  // 点击外部区域时关闭搜索框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        searchQuery === ""
      ) {
        setIsActive(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [searchQuery]);

  return (
    <div ref={containerRef} className={`relative h-full ${className}`}>
      <motion.div
        className={`h-full rounded-lg border border-transparent ${
          isActive
            ? "bg-secondaryBtn/5 hover:border-secondaryBtn/20"
            : "hover:bg-secondaryBtn/5"
        } transition-colors duration-300 flex items-center overflow-hidden relative`}
        animate={{
          width: isActive ? "12rem" : "2rem",
        }}
        initial={false}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 40,
        }}
      >
        {/* 搜索图标固定在左侧，避免位置跳变 */}
        <div
          className="absolute left-0 h-full w-8 flex items-center justify-center text-secondaryBtn opacity-40 hover:opacity-100 cursor-pointer transition-opacity duration-300 ease-in-out z-10"
          onClick={handleActivate}
        >
          <Search size={16} />
        </div>

        <AnimatePresence>
          {isActive && (
            <motion.div
              className="w-full h-full flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <input
                ref={inputRef}
                type="text"
                value={searchQuery}
                onChange={handleSearchChange}
                onBlur={handleBlur}
                placeholder={placeholder}
                className="w-full h-full pl-8 pr-8 bg-transparent text-sm text-secondaryBtn outline-none border-none placeholder:text-secondaryBtn/40"
              />

              {/* 清空按钮 */}
              <AnimatePresence>
                {searchQuery && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.15 }}
                    className="absolute right-2 h-full flex items-center justify-center"
                  >
                    <button
                      type="button"
                      onClick={handleClear}
                      className="w-3.5 h-3.5 flex items-center justify-center rounded-full bg-secondaryBtn text-background opacity-30 hover:opacity-60 transition-opacity"
                    >
                      <X size={8} strokeWidth={2.5} />
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default SearchInput;
