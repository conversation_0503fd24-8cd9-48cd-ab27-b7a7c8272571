"use client";

import React, { useRef, useEffect, useState } from "react";
import { ViewType } from "../types";
import { LayoutGrid, LayoutList } from "lucide-react";
import { motion } from "framer-motion";

interface ViewToggleProps {
  viewType: ViewType;
  onViewTypeChange: (viewType: ViewType) => void;
}

/**
 * 项目视图切换组件
 * 负责在网格视图和列表视图之间切换
 */
const ViewToggle: React.FC<ViewToggleProps> = ({
  viewType = "grid",
  onViewTypeChange,
}) => {
  const gridButtonRef = useRef<HTMLButtonElement>(null);
  const listButtonRef = useRef<HTMLButtonElement>(null);
  const [activeButtonPosition, setActiveButtonPosition] = useState({ left: 0 });

  // 更新激活按钮的位置
  useEffect(() => {
    const updatePosition = () => {
      const activeButton =
        viewType === "grid" ? gridButtonRef.current : listButtonRef.current;
      if (activeButton) {
        setActiveButtonPosition({
          left: activeButton.offsetLeft,
        });
      }
    };

    updatePosition();
    window.addEventListener("resize", updatePosition);
    return () => {
      window.removeEventListener("resize", updatePosition);
    };
  }, [viewType]);

  return (
    <div className="h-full flex items-center rounded-lg bg-secondaryBtn/5 p-1">
      <div className="h-full flex items-center rounded-md relative">
        {/* 移动的背景元素 - 使用绝对定位，宽度与按钮相同 */}
        <motion.div
          className="absolute h-full w-6 rounded-md bg-background/80 dark:bg-primaryBtn/10"
          initial={false}
          animate={{
            left: activeButtonPosition.left,
          }}
          transition={{
            type: "spring",
            stiffness: 500,
            damping: 40,
          }}
        />

        {/* 网格视图按钮 */}
        <button
          ref={gridButtonRef}
          type="button"
          onClick={() => onViewTypeChange("grid")}
          className={`w-6 h-full flex items-center justify-center relative z-10 transition-colors duration-300 ${
            viewType === "grid"
              ? "text-secondaryBtn"
              : "text-secondaryBtn/40 hover:text-secondaryBtn"
          }`}
        >
          <LayoutGrid size={12} />
        </button>

        {/* 列表视图按钮 */}
        <button
          ref={listButtonRef}
          type="button"
          onClick={() => onViewTypeChange("list")}
          className={`w-6 h-full flex items-center justify-center relative z-10 transition-colors duration-300 ${
            viewType === "list"
              ? "text-secondaryBtn"
              : "text-secondaryBtn/40 hover:text-secondaryBtn"
          }`}
        >
          <LayoutList size={12} />
        </button>
      </div>
    </div>
  );
};

export default ViewToggle;
