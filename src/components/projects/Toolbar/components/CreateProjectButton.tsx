"use client";

import React from "react";
import { Plus } from "lucide-react";

interface CreateProjectButtonProps {
  onClick?: () => void;
}

/**
 * 创建项目按钮组件
 * 负责触发创建新项目的操作
 * 在小屏幕上只显示加号图标，不显示文字
 */
const CreateProjectButton: React.FC<CreateProjectButtonProps> = ({
  onClick = () => {},
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className="h-full px-3 flex items-center justify-center gap-1.5 rounded-lg bg-primaryBtn text-primaryBtn-text text-[13px] font-medium transition-colors duration-300 hover:bg-primaryBtn/90"
    >
      <Plus className="w-3.5 h-3.5" />
      <span className="max-sm:hidden">New Project</span>
    </button>
  );
};

export default CreateProjectButton;
