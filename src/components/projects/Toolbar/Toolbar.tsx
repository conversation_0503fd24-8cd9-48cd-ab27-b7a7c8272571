"use client";

import React from "react";
import FilterTabs from "./components/FilterTabs";
import ViewToggle from "./components/ViewToggle";
import SearchInput from "./components/SearchInput";
import CreateProjectButton from "./components/CreateProjectButton";
import { ProjectFilterType, ViewType } from "./types";

interface ToolbarProps {
  activeFilter: ProjectFilterType;
  viewType: ViewType;
  searchQuery: string;
  onFilterChange: (filter: ProjectFilterType) => void;
  onViewTypeChange?: (viewType: ViewType) => void; // 修改为可选参数
  onSearchChange?: (query: string) => void; // 修改为可选参数
  onCreateProject?: () => void; // 创建项目回调
}

/**
 * 项目工具栏组件
 * 整合了标签页筛选、视图切换和搜索功能
 */
const Toolbar: React.FC<ToolbarProps> = ({
  activeFilter = "all",
  viewType = "grid",
  searchQuery = "",
  onFilterChange,
  onViewTypeChange,
  onSearchChange,
  onCreateProject,
}) => {
  // 渲染条件：只有当回调函数存在时才渲染对应组件
  const showViewToggle = typeof onViewTypeChange === "function";
  const showSearchInput = typeof onSearchChange === "function";

  return (
    <div className="flex items-center justify-between h-8">
      {/* 左侧标题和过滤选项卡 */}
      <div className="flex-1 h-full flex items-center gap-4">
        {/* 项目标题 */}
        <h1 className="text-4xl font-extrabold text-secondaryBtn">PROJECTS</h1>
        {/* 过滤选项卡 */}
        <FilterTabs
          activeFilter={activeFilter}
          onFilterChange={onFilterChange}
        />
      </div>

      {/* 右侧视图切换和搜索 */}
      <div className="flex items-center gap-4 h-full">
        {/* 搜索框 */}
        {showSearchInput && (
          <SearchInput
            searchQuery={searchQuery}
            onSearchChange={onSearchChange!}
          />
        )}
        {/* 视图切换按钮 */}
        {showViewToggle && (
          <ViewToggle
            viewType={viewType}
            onViewTypeChange={onViewTypeChange!}
          />
        )}
        {/* 创建项目按钮 */}
        <CreateProjectButton onClick={onCreateProject} />
      </div>
    </div>
  );
};

export default Toolbar;
