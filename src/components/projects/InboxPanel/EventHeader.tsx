import React from "react";
import { MoreVertical, RefreshCcw, Bell } from "lucide-react";

interface EventHeaderProps {
  title: string;
}

/**
 * 事件面板头部组件
 * 包含标题和操作按钮
 */
const EventHeader: React.FC<EventHeaderProps> = ({ title }) => {
  return (
    <div className="flex items-center justify-between h-10">
      <div className="flex items-center">
        <Bell size={14} className="text-secondaryBtn mr-1.5" />
        <h2 className="text-sm font-semibold text-secondaryBtn">{title}</h2>
      </div>
      <div className="flex items-center">
        <button className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 transition-colors duration-300">
          <RefreshCcw
            size={14}
            className="text-secondaryBtn/50 hover:text-secondaryBtn"
          />
        </button>
        <button className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 transition-colors duration-300 ml-1">
          <MoreVertical
            size={14}
            className="text-secondaryBtn/50 hover:text-secondaryBtn"
          />
        </button>
      </div>
    </div>
  );
};

export default EventHeader;
