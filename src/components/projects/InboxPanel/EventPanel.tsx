import React from "react";
import EventHeader from "./EventHeader";
import { Inbox } from "lucide-react";

interface EventPanelProps {
  className?: string;
}

// 定义事件数据类型
interface EventItem {
  id: number;
  title: string;
  message: string;
  time: string;
}

// Mock 数据
const mockEvents: EventItem[] = [
  {
    id: 1,
    title: "System Notification",
    message:
      'New document has been shared with you: "Contract Review Guidelines"',
    time: "2h ago",
  },
  {
    id: 2,
    title: "Project Update",
    message: 'The project "Marketing Campaign 2024" has been updated',
    time: "3h ago",
  },
  {
    id: 3,
    title: "Comment Notification",
    message: '<PERSON> commented on your document "Q1 Financial Report"',
    time: "5h ago",
  },
  {
    id: 4,
    title: "Task Assignment",
    message:
      'You have been assigned a new task: "Review product specifications"',
    time: "Yesterday",
  },
  {
    id: 5,
    title: "Meeting Reminder",
    message: "Team meeting scheduled for tomorrow at 10:00 AM",
    time: "Yesterday",
  },
  {
    id: 6,
    title: "System Notification",
    message: "Your account settings have been updated successfully",
    time: "2 days ago",
  },
  {
    id: 7,
    title: "Document Edited",
    message: '<PERSON> edited "Client Proposal Draft"',
    time: "2 days ago",
  },
  {
    id: 8,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 9,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 10,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 11,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 12,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 13,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 14,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
  {
    id: 15,
    title: "Deadline Reminder",
    message: "Project submission deadline is approaching (3 days left)",
    time: "3 days ago",
  },
];

const EventPanel: React.FC<EventPanelProps> = ({ className = "" }) => {
  return (
    <div
      className={`flex flex-col h-full mx-0 md2:ml-3 md2:mr-2 bg-background rounded-xl ${className}`}
    >
      <div className="flex-shrink-0 pl-4 pr-2.5 py-1">
        <EventHeader title="Events Center" />
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="pl-4 pr-2.5 space-y-0">
          {/* 事件列表 */}
          {mockEvents.length > 0 ? (
            mockEvents.map((event, index) => (
              <React.Fragment key={event.id}>
                <div className="py-3 px-3 bg-transparent rounded-xl hover:bg-secondaryBtn/5 transition-colors cursor-pointer">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm text-secondaryBtn dark:text-gray-100/70">
                      {event.title}
                    </span>
                    <span className="text-xs text-secondaryBtn/40 dark:text-gray-300/40">
                      {event.time}
                    </span>
                  </div>
                  <p className="text-sm text-secondaryBtn/40 dark:text-gray-300/40">
                    {event.message}
                  </p>
                </div>
                {index < mockEvents.length - 1 && (
                  <div className="border-b border-divider mx-3"></div>
                )}
              </React.Fragment>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Inbox className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-gray-700 font-medium mb-1 dark:text-gray-200">
                Your event list is empty
              </h3>
              <p className="text-gray-500 text-sm dark:text-gray-400">
                Notifications and events will appear here
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EventPanel;
