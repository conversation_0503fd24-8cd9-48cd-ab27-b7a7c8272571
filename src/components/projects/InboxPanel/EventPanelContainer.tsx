import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { EventPanel } from "./index";

interface EventPanelContainerProps {
  isCollapsed: boolean;
}

/**
 * EventPanel 容器组件，处理折叠/展开的动画过渡
 * 响应式布局:
 * - 在桌面视图(>=900px): 侧边栏
 * - 在平板视图(>=560px and <900px): 右上角浮窗
 * - 在手机视图(<560px): 底部全宽卡片
 */
const EventPanelContainer: React.FC<EventPanelContainerProps> = ({
  isCollapsed,
}) => {
  // 控制内容可见性，用于淡入淡出效果
  const [isContentVisible, setIsContentVisible] = useState(!isCollapsed);
  // 跟踪初始渲染状态
  const isFirstMount = useRef(true);
  // 面板宽度
  const PANEL_WIDTH = "410px";
  // 检测视图模式
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">(
    "desktop"
  );
  // 跟踪面板在切换视图模式前的显示状态
  const wasVisibleBeforeViewChange = useRef(!isCollapsed);

  // 检测窗口大小变化，判断当前视图模式
  useEffect(() => {
    const checkViewMode = () => {
      const prevViewMode = viewMode;
      let newViewMode: "desktop" | "tablet" | "mobile" = "desktop";

      const width = window.innerWidth;
      if (width >= 900) {
        // md2断点及以上
        newViewMode = "desktop";
      } else if (width >= 560) {
        // sm断点及以上，md2断点以下
        newViewMode = "tablet";
      } else {
        // sm断点以下
        newViewMode = "mobile";
      }

      // 如果视图模式发生变化
      if (prevViewMode !== newViewMode) {
        // 保存当前面板显示状态，以便在切换视图模式后使用
        wasVisibleBeforeViewChange.current = !isCollapsed;
        setViewMode(newViewMode);
      }
    };

    // 初始检查
    checkViewMode();

    // 监听窗口大小变化
    window.addEventListener("resize", checkViewMode);
    return () => window.removeEventListener("resize", checkViewMode);
  }, [viewMode, isCollapsed]);

  // 当折叠状态变化时，处理内容可见性
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    // 跳过首次渲染
    if (!isFirstMount.current) {
      if (isCollapsed) {
        // 折叠时，先淡出内容，再收起面板
        setIsContentVisible(false);
      } else {
        // 展开时，先展开面板，再淡入内容
        timeout = setTimeout(() => {
          setIsContentVisible(true);
        }, 150); // 等待面板展开一段时间后显示内容
      }
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [isCollapsed]);

  // 首次渲染后标记为非首次渲染
  useEffect(() => {
    // 使用 requestAnimationFrame 确保在下一帧执行
    requestAnimationFrame(() => {
      isFirstMount.current = false;
    });
  }, []);

  // 判断是否应显示面板
  const shouldShowPanel = !isCollapsed || wasVisibleBeforeViewChange.current;

  // 移动设备视图 - 底部向上滑出的全宽卡片
  if (viewMode === "mobile") {
    return (
      <AnimatePresence>
        {shouldShowPanel && !isCollapsed && (
          <motion.div
            className="fixed inset-x-0 bottom-0 z-50 w-full"
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <div
              className="w-full h-[90vh] mx-auto bg-background rounded-t-2xl shadow-lg overflow-hidden"
              style={{ maxHeight: "calc(100vh - 70px)" }}
            >
              <EventPanel className="max-h-[calc(100vh-70px)]" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // 平板设备视图 - 右上角浮窗
  if (viewMode === "tablet") {
    return (
      <AnimatePresence>
        {shouldShowPanel && !isCollapsed && (
          <motion.div
            className="fixed top-16 right-4 z-50 w-[90vw] max-w-[400px] shadow-lg rounded-xl"
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            <div className="max-h-[80vh] overflow-hidden">
              <EventPanel className="max-h-[80vh]" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // 桌面视图 - 侧边栏
  return (
    <AnimatePresence>
      {!isCollapsed && (
        <motion.div
          className="w-[410px] flex-shrink-0 flex flex-col overflow-hidden"
          // 首次渲染时不应用动画
          initial={
            isFirstMount.current
              ? { width: PANEL_WIDTH, opacity: 1 }
              : { width: 0, opacity: 0 }
          }
          animate={{ width: PANEL_WIDTH, opacity: 1 }}
          exit={{ width: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <div className="flex-grow mt-8 h-full overflow-hidden">
            <motion.div
              className="h-full flex flex-col overflow-hidden"
              initial={isFirstMount.current ? { opacity: 1 } : { opacity: 0 }}
              animate={{ opacity: isContentVisible ? 1 : 0 }}
              transition={{ duration: 0.15 }}
            >
              <EventPanel className="h-full" />
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EventPanelContainer;
