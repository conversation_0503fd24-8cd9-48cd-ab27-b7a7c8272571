"use client";

import React, { useMemo, useRef, useEffect, useState } from "react";
import ProjectCard from "./components/Card";
import ProjectListItem from "./components/List";
import PlaceholderCard from "./components/PlaceholderCard";
import { ProjectData } from "./types";
import { ViewType } from "../Toolbar";
import { Star, Archive } from "lucide-react"; // 导入需要的图标
import { ErrorState } from "@/components/ui/status";

interface ViewBoxProps {
  projects: ProjectData[];
  viewType: ViewType;
  onEditProject: (project: ProjectData) => void;
  onDeleteProject: (projectId: string) => void;
  onToggleFavorite: (projectId: string, isFavorite: boolean) => void;
  onProjectClick: (projectId: string) => void;
  onCopyProject?: (project: ProjectData) => void;
  isLoading?: boolean;
  error?: string | null;
}

// 分组标题组件
const SectionTitle: React.FC<{
  title: string;
  isSticky?: boolean;
  icon?: React.ReactNode;
}> = ({ title, isSticky = false, icon }) => (
  <div
    className={`flex items-center py-3 pr-2 bg-gradient-to-b from-backgroundDeep from-50% to-backgroundDeep/0 to-100% z-10 ${
      isSticky ? "sticky top-0" : ""
    }`}
  >
    {icon && <div className="mr-2">{icon}</div>}
    <h2 className="text-md font-medium text-secondaryBtn">{title}</h2>
  </div>
);

/**
 * 项目视图盒子组件
 * 提供标题吸顶效果的项目列表视图
 */
const ViewBox: React.FC<ViewBoxProps> = ({
  projects,
  viewType,
  onEditProject,
  onDeleteProject,
  onToggleFavorite,
  onProjectClick,
  onCopyProject,
  isLoading = false,
  error = null,
}) => {
  // 滚动状态
  const [isScrolled, setIsScrolled] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 按收藏状态分组并排序项目
  const { favoriteProjects, normalProjects } = useMemo(() => {
    // 收藏的项目
    const favorites = projects
      .filter((project) => project.is_favorite)
      .sort((a, b) => {
        // 按最后修改时间排序，最近修改的排在前面
        const timeA = new Date(a.last_edited_at).getTime();
        const timeB = new Date(b.last_edited_at).getTime();
        return timeB - timeA;
      });

    // 未收藏的项目
    const normals = projects
      .filter((project) => !project.is_favorite)
      .sort((a, b) => {
        // 按最后修改时间排序，最近修改的排在前面
        const timeA = new Date(a.last_edited_at).getTime();
        const timeB = new Date(b.last_edited_at).getTime();
        return timeB - timeA;
      });

    return { favoriteProjects: favorites, normalProjects: normals };
  }, [projects]);

  // 确定是否显示分组标题（只有同时存在收藏项目和普通项目时才显示标题）
  const shouldShowSectionTitles =
    favoriteProjects.length > 0 && normalProjects.length > 0;

  // 用于监测滚动的容器引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 监听滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      setIsScrolled(container.scrollTop > 10);
      setIsScrolling(true);

      // 清除之前的定时器
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }

      // 设置新的定时器，滚动停止 1 秒后隐藏滚动条
      scrollTimerRef.current = setTimeout(() => {
        setIsScrolling(false);
      }, 1000);
    };

    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
      // 组件卸载时清除定时器
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }
    };
  }, []);

  // 网格布局样式
  const gridLayoutStyle = {
    display: "grid" as const,
    gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))",
    gap: "1rem",
    maxWidth: "100%",
  };

  // 计算首屏能容纳的卡片数量
  const calculateViewportCards = () => {
    if (typeof window === "undefined") return 6; // SSR 默认值

    const viewportHeight = window.innerHeight;
    const cardHeight = 280; // 卡片高度约280px
    const gap = 16; // 1rem = 16px
    const headerHeight = 80; // 顶部导航高度
    const padding = 32; // 上下padding

    const availableHeight = viewportHeight - headerHeight - padding;
    const rowsPerScreen = Math.floor(availableHeight / (cardHeight + gap));
    const columnsPerRow = Math.floor((window.innerWidth - 64) / (300 + gap)); // 300px 最小宽度

    return Math.max(rowsPerScreen * columnsPerRow, 15); // 至少6个
  };

  // 渲染项目列表或网格
  const renderProjects = (
    projectsList: ProjectData[],
    isPadded = false,
    isLastSection = false
  ) => {
    if (viewType === "grid") {
      // 不再在有项目时显示占位卡片
      const placeholderCount = 0;

      return (
        <div className={`pr-4 sm:pr-12 ${isPadded ? "pb-24" : "pb-6"}`}>
          <div className="grid w-full gap-4 mx-auto" style={gridLayoutStyle}>
            {/* 渲染真实项目卡片 */}
            {projectsList.map((project) => (
              <div
                key={project.id}
                className="h-full"
                style={{
                  minWidth: "280px",
                  maxWidth: "100%",
                }}
              >
                <ProjectCard
                  project={project}
                  onEdit={onEditProject}
                  onDelete={onDeleteProject}
                  onToggleFavorite={onToggleFavorite}
                  onClick={onProjectClick}
                  onCopy={onCopyProject}
                />
              </div>
            ))}

            {/* 渲染占位卡片 */}
            {Array.from({ length: placeholderCount }, (_, index) => (
              <div
                key={`placeholder-${index}`}
                className="h-full"
                style={{
                  minWidth: "280px",
                  maxWidth: "100%",
                }}
              >
                <PlaceholderCard />
              </div>
            ))}
          </div>
        </div>
      );
    } else {
      return (
        <div
          className={`xxs:pl-2 xs:pl-2 sm:pl-4 pr-2 ${
            isPadded ? "pb-24" : "pb-6"
          }`}
        >
          {projectsList.map((project) => (
            <ProjectListItem
              key={project.id}
              project={project}
              onEdit={onEditProject}
              onDelete={onDeleteProject}
              onToggleFavorite={onToggleFavorite}
              onClick={onProjectClick}
              onCopy={onCopyProject}
            />
          ))}
        </div>
      );
    }
  };

  // 处理加载状态 - 显示占位卡片
  if (isLoading) {
    if (viewType === "grid") {
      const placeholderCount = calculateViewportCards();
      return (
        <div className="h-full flex flex-col">
          <div className="flex-grow h-full overflow-y-auto auto-hide-scrollbar">
            <div className="pt-4">
              <div className="pr-4 sm:pr-12 pb-6">
                <div
                  className="grid w-full gap-4 mx-auto"
                  style={gridLayoutStyle}
                >
                  {Array.from({ length: placeholderCount }, (_, index) => (
                    <div
                      key={`loading-placeholder-${index}`}
                      className="h-full"
                      style={{
                        minWidth: "280px",
                        maxWidth: "100%",
                      }}
                    >
                      <PlaceholderCard />
                    </div>
                  ))}
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-b from-backgroundDeep/10 to-backgroundDeep py-16 xxs:pr-2 xs:pr-2 sm:pr-12"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="h-full flex flex-col items-center justify-center py-16">
          <p className="text-gray-400">Loading projects...</p>
        </div>
      );
    }
  }

  // 处理错误状态
  if (error) {
    if (viewType === "grid") {
      const placeholderCount = calculateViewportCards();
      return (
        <div className="h-full flex flex-col relative">
          {/* 背景占位卡片层 */}
          <div className="flex-grow h-full overflow-y-auto auto-hide-scrollbar">
            <div className="pt-4">
              <div className="pr-4 sm:pr-12 pb-6">
                <div
                  className="grid w-full gap-4 mx-auto opacity-100"
                  style={gridLayoutStyle}
                >
                  {Array.from({ length: placeholderCount }, (_, index) => (
                    <div
                      key={`error-placeholder-${index}`}
                      className="h-full"
                      style={{
                        minWidth: "280px",
                        maxWidth: "100%",
                      }}
                    >
                      <PlaceholderCard />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 错误提示覆盖层 */}
          <ErrorState
            className="xxs:pr-2 xs:pr-2 sm:pr-12"
            title="Failed to Load Projects"
            message={error}
            overlay={true}
          />
        </div>
      );
    } else {
      // 列表视图的错误状态
      return (
        <div className="h-full flex flex-col relative">
          <ErrorState
            className="xxs:pr-2 xs:pr-2 sm:pr-12"
            title="Failed to Load Projects"
            message={error}
            overlay={true}
          />
        </div>
      );
    }
  }

  // 处理空状态 - 显示占位卡片而不是空提示
  if (projects.length === 0) {
    if (viewType === "grid") {
      // 在网格视图中显示占位卡片，铺满首屏
      const placeholderCount = calculateViewportCards();
      return (
        <div className="h-full flex flex-col">
          <div className="flex-grow h-full overflow-y-auto auto-hide-scrollbar">
            <div className="pt-4">
              <div className="pr-4 sm:pr-12 pb-6">
                <div
                  className="grid w-full gap-4 mx-auto"
                  style={gridLayoutStyle}
                >
                  {Array.from({ length: placeholderCount }, (_, index) => (
                    <div
                      key={`empty-placeholder-${index}`}
                      className="h-full"
                      style={{
                        minWidth: "280px",
                        maxWidth: "100%",
                      }}
                    >
                      <PlaceholderCard />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // 在列表视图中显示传统的空状态提示
      return (
        <div className="h-[400px] flex flex-col items-center justify-center py-16 text-gray-400 xxs:pr-2 xs:pr-2 sm:pr-12">
          <p className="text-lg">No projects found</p>
          <p className="text-sm mt-2">Create a new project to get started</p>
        </div>
      );
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* 滚动容器 */}
      <div
        ref={containerRef}
        className={`flex-grow h-full overflow-y-auto auto-hide-scrollbar ${
          isScrolling ? "scrolling" : ""
        }`}
        style={{ scrollbarGutter: "stable" as const }}
      >
        {/* 收藏项目区域 */}
        {favoriteProjects.length > 0 && (
          <div className={`${!normalProjects.length ? "pt-4" : ""}`}>
            {shouldShowSectionTitles && (
              <SectionTitle
                title="Favorites"
                isSticky={true}
                icon={<Star className="w-4 h-4 text-secondaryBtn" />}
              />
            )}
            {renderProjects(
              favoriteProjects,
              false,
              normalProjects.length === 0
            )}
          </div>
        )}

        {/* 普通项目区域 */}
        {normalProjects.length > 0 && (
          <div className={`${!favoriteProjects.length ? "pt-4" : ""}`}>
            {shouldShowSectionTitles && (
              <SectionTitle
                title="Others"
                isSticky={true}
                icon={<Archive className="w-4 h-4 text-secondaryBtn" />}
              />
            )}
            {renderProjects(normalProjects, true, true)}
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewBox;
