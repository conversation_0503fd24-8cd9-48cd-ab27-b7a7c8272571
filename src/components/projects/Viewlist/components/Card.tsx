"use client";

import React, { useState, useRef, useEffect } from "react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { ProjectData } from "../types";
import { EllipsisVertical, Star } from "lucide-react";
import Image from "next/image";
import ContextMenu from "../../../ui/DropdownMenu/ContextMenu";
import MenuItem from "../../../ui/DropdownMenu/MenuItem";

// 导入 SVG 作为 React 组件
import QuoteSymbol from "/public/imgs/quote_symbol.svg";

interface ProjectCardProps {
  project: ProjectData;
  onEdit: (project: ProjectData) => void;
  onDelete: (projectId: string) => void;
  onToggleFavorite: (projectId: string, isFavorite: boolean) => void;
  onClick: (projectId: string) => void;
  onCopy?: (project: ProjectData) => void;
}

// 禁用滚动的辅助函数
const disableScroll = () => {
  // 保存当前滚动位置
  const scrollY = window.scrollY;

  // 添加样式到body
  document.body.style.position = "fixed";
  document.body.style.width = "100%";
  document.body.style.top = `-${scrollY}px`;
  document.body.dataset.scrollPosition = scrollY.toString();
};

// 启用滚动的辅助函数
const enableScroll = () => {
  // 恢复滚动位置
  const scrollY = document.body.dataset.scrollPosition || "0";
  document.body.style.position = "";
  document.body.style.width = "";
  document.body.style.top = "";
  delete document.body.dataset.scrollPosition;

  window.scrollTo(0, parseInt(scrollY));
};

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit,
  onDelete,
  onToggleFavorite,
  onClick,
  onCopy,
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const menuButtonRef = useRef<HTMLButtonElement | null>(null);
  const menuClosedByClickRef = useRef<boolean>(false);
  const lastClickTimeRef = useRef<number>(0);

  // 处理菜单状态变化
  const handleMenuOpenChange = (open: boolean) => {
    if (!open) {
      // 菜单即将关闭，标记为菜单关闭操作
      menuClosedByClickRef.current = true;
      // 记录关闭时间
      lastClickTimeRef.current = Date.now();
      // 设置一个短暂的延迟，防止卡片点击事件立即触发
      setTimeout(() => {
        menuClosedByClickRef.current = false;
      }, 300); // 增加延迟时间
    }
    setIsMenuOpen(open);
  };

  // 处理滚动锁定
  useEffect(() => {
    if (isMenuOpen) {
      disableScroll();
    } else {
      enableScroll();
    }

    // 组件卸载时清理
    return () => {
      if (isMenuOpen) {
        enableScroll();
      }
    };
  }, [isMenuOpen]);

  // 使用 useEffect 确保组件仅在客户端渲染
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleCardClick = (e: React.MouseEvent) => {
    // 检查点击是否来自菜单按钮或其子元素
    if (
      menuButtonRef.current &&
      (menuButtonRef.current === e.target ||
        menuButtonRef.current.contains(e.target as Node))
    ) {
      return;
    }

    // 检查菜单是否打开或刚刚关闭
    if (isMenuOpen || menuClosedByClickRef.current) {
      return;
    }

    // 检查是否在短时间内有菜单关闭操作
    const timeSinceLastClick = Date.now() - lastClickTimeRef.current;
    if (timeSinceLastClick < 500) {
      // 500毫秒内的点击都忽略
      return;
    }

    onClick(project.id);
  };

  const handleEditClick = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    onEdit(project);
    setIsMenuOpen(false);
  };

  const handleDeleteClick = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    onDelete(project.id);
    setIsMenuOpen(false);
  };

  const handleCopyClick = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    if (onCopy) {
      onCopy(project);
    }
    setIsMenuOpen(false);
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log("卡片收藏前状态:", project.is_favorite);
    // onToggleFavorite(project.id, !project.is_favorite);
    // 临时使用固定的 project_id 进行测试
    onToggleFavorite('00000002-1234-5678-9abc-def012345678', !project.is_favorite);
    console.log("卡片收藏后期望状态:", !project.is_favorite);
  };

  const handleMenuAction = (action: string) => {
    if (action === "edit") handleEditClick();
    if (action === "duplicate") handleCopyClick();
    if (action === "delete") handleDeleteClick();
    setIsMenuOpen(false);
  };

  // 卡片外层容器样式 - 确保一致性
  const cardContainerClasses = "cursor-pointer w-full h-[180px]";

  // 卡片主体样式 - 确保一致性
  const cardClasses =
    "w-full h-full transition-all duration-300 flex flex-col border border-background bg-background dark:hover:bg-gray-400/10 relative overflow-hidden rounded-lg hover:shadow-lg hover:shadow-secondaryBtn/10 dark:hover:shadow-black/20";

  // 服务端渲染时返回样式一致的占位符
  if (!isMounted) {
    return (
      <div className={cardContainerClasses}>
        <div className={cardClasses}>
          {/* 占位符也添加引号符号背景装饰 */}
          <div className="absolute top-0 left-0 w-32 h-32 pointer-events-none text-current opacity-[0.03]">
            <QuoteSymbol className="w-full h-full scale-75 transform origin-top-left" />
          </div>

          {/* 占位符内容 - 与实际卡片结构保持一致 */}
          <div className="p-4 flex flex-col flex-grow overflow-hidden">
            <div className="flex justify-between items-start">
              <div className="h-6 bg-secondaryBtn/10 rounded mb-3 w-3/4"></div>
            </div>
            <div className="h-4 bg-secondaryBtn/5 rounded mb-3 w-full"></div>
            <div className="flex-grow"></div>
          </div>
          <div className="pt-0 pb-3 px-4 flex justify-between items-center">
            <div className="h-4 bg-secondaryBtn/5 rounded w-1/3"></div>
            <div className="flex items-center space-x-1 flex-shrink-0">
              <div className="h-8 w-8 bg-secondaryBtn/5 rounded-md"></div>
              <div className="h-8 w-8 bg-secondaryBtn/5 rounded-md"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cardContainerClasses}
      onClick={handleCardClick}
      ref={cardRef}
    >
      {/* 自定义Card组件 */}
      <div className={cardClasses}>
        {/* 引号符号背景装饰 */}
        <div className="absolute top-0 left-0 w-32 h-40 pointer-events-none text-current opacity-5">
          <QuoteSymbol className="w-full h-full scale-85 transform origin-top-left" />
        </div>

        {/* 自定义CardBody */}
        <div className="p-4 flex flex-col flex-grow overflow-hidden">
          <div className="flex justify-between items-start">
            <h3 className="text-[15px] text-secondaryBtn dark:text-gray-100/70 font-semibold line-clamp-2 mr-2 mb-3">
              {project.name}
            </h3>
          </div>
          <p className="text-gray-700/50 dark:text-gray-300/40 text-xs line-clamp-2 mb-3">
            {project.description}
          </p>
          <div className="flex-grow"></div>
        </div>

        {/* 自定义CardFooter */}
        <div className="pt-0 pb-3 px-4 flex justify-between items-center text-gray-700/50 dark:text-gray-300/40 text-[11px] font-medium">
          <div className="truncate">
            Last Modified{" "}
            {format(new Date(project.last_edited_at), "MM/dd/yyyy H:mm", { locale: zhCN })}
          </div>
          <div className="flex items-center space-x-1 flex-shrink-0">
            {/* 自定义Button - 收藏 */}
            <button
              type="button"
              onClick={handleFavoriteClick}
              className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-secondaryBtn/5 transition-all duration-300 group"
            >
              <Star
                className={`w-4 h-4 transition-all duration-300 ${
                  project.is_favorite
                    ? "text-yellow-400 fill-yellow-400"
                    : "text-secondaryBtn/50 group-hover:text-secondaryBtn"
                }`}
              />
            </button>

            {/* 自定义Popover */}
            <div className="relative" onClick={(e) => e.stopPropagation()}>
              {/* 自定义Button - 菜单 */}
              <button
                type="button"
                ref={menuButtonRef}
                className="flex items-center justify-center w-8 h-8 rounded-lg text-secondaryBtn/50 hover:text-secondaryBtn hover:bg-secondaryBtn/5 transition-all duration-300 group"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsMenuOpen(!isMenuOpen);
                }}
              >
                <EllipsisVertical className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 使用独立的ContextMenu组件 */}
      <ContextMenu
        isOpen={isMenuOpen}
        setIsOpen={handleMenuOpenChange}
        referenceElement={menuButtonRef.current}
      >
        <MenuItem onClick={() => handleMenuAction("edit")}>Edit Info</MenuItem>
        <MenuItem onClick={() => handleMenuAction("duplicate")}>
          Duplicate
        </MenuItem>
        <MenuItem
          onClick={() => handleMenuAction("delete")}
          textColor="text-red-500"
        >
          Delete
        </MenuItem>
      </ContextMenu>
    </div>
  );
};

export default ProjectCard;
