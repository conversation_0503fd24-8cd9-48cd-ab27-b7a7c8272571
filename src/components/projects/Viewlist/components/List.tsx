"use client";

import React, { useState, useRef } from "react";
import {
  Button,
  Listbox,
  ListboxItem,
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@heroui/react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { ProjectData } from "../types";
import { EllipsisVertical, Star } from "lucide-react";


interface ProjectListItemProps {
  project: ProjectData;
  onEdit: (project: ProjectData) => void;
  onDelete: (projectId: string) => void;
  onToggleFavorite: (projectId: string, isFavorite: boolean) => void;
  onClick: (projectId: string) => void;
  onCopy?: (project: ProjectData) => void;
}

const ProjectListItem: React.FC<ProjectListItemProps> = ({
  project,
  onEdit,
  onDelete,
  onToggleFavorite,
  onClick,
  onCopy,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const listItemRef = useRef<HTMLDivElement>(null);

  // 添加全局鼠标移动事件监听器
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMenuOpen &&
        listItemRef.current &&
        !listItemRef.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMenuOpen]);

  const handleClick = () => {
    onClick(project.id);
  };

  const handleEditClick = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    onEdit(project);
    setIsMenuOpen(false);
  };

  const handleDeleteClick = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    onDelete(project.id);
    setIsMenuOpen(false);
  };

  const handleCopyClick = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    if (onCopy) {
      onCopy(project);
    }
    setIsMenuOpen(false);
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log("收藏前状态:", project.is_favorite);
    // onToggleFavorite(project.id, !project.is_favorite);
    // 临时使用固定的 project_id 进行测试
    onToggleFavorite('00000002-1234-5678-9abc-def012345678', !project.is_favorite);
    console.log("收藏后期望状态:", !project.is_favorite);
  };

  const handleMenuAction = (key: React.Key) => {
    if (key === "edit") handleEditClick();
    if (key === "copy") handleCopyClick();
    if (key === "delete") handleDeleteClick();
    setIsMenuOpen(false);
  };

  return (
    <div
      className="flex items-center p-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer"
      onClick={handleClick}
      ref={listItemRef}
      onMouseLeave={() => {
        if (isMenuOpen) {
          setTimeout(() => {
            setIsMenuOpen(false);
          }, 100);
        }
      }}
    >
      <div className="flex-1">
        <div className="flex items-center">
          <h3 className="text-base font-medium">{project.name}</h3>
        </div>
        <p className="text-gray-600 text-xs mt-1">{project.description}</p>
      </div>

      <div className="text-gray-500 text-sm mr-8">
        最近编辑{" "}
        {format(new Date(project.last_edited_at), "MM/dd/yyyy H:mm", { locale: zhCN })}
      </div>

      <div className="flex items-center space-x-1">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          onClick={handleFavoriteClick}
          className="text-gray-500 hover:text-yellow-400"
        >
          <Star
            className={`w-4 h-4 ${
              project.is_favorite
                ? "text-yellow-400 fill-yellow-400"
                : "text-gray-400 hover:text-gray-800"
            }`}
          />
        </Button>

        <Popover
          placement="bottom-end"
          isOpen={isMenuOpen}
          onOpenChange={setIsMenuOpen}
          classNames={{
            content:
              "p-0 border border-gray-200 shadow-md min-w-[160px] rounded-md overflow-hidden",
          }}
        >
          <PopoverTrigger>
            <Button
              isIconOnly
              variant="light"
              size="sm"
              className="text-gray-500 hover:text-gray-800"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setIsMenuOpen(!isMenuOpen);
              }}
            >
              <EllipsisVertical className="w-4 h-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent>
            <Listbox
              aria-label="项目操作"
              onAction={handleMenuAction}
              variant="flat"
              color="default"
              className="p-1 rounded-none"
              disallowEmptySelection={false}
              selectionMode="none"
              itemClasses={{
                base: "rounded-sm",
              }}
            >
              <ListboxItem
                key="edit"
                className="text-sm"
                onClick={(e) => e.stopPropagation()}
              >
                编辑信息
              </ListboxItem>
              <ListboxItem
                key="copy"
                className="text-sm"
                onClick={(e) => e.stopPropagation()}
              >
                复制
              </ListboxItem>
              <ListboxItem
                key="delete"
                className="text-sm text-danger"
                color="danger"
                onClick={(e) => e.stopPropagation()}
              >
                删除
              </ListboxItem>
            </Listbox>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default ProjectListItem;
