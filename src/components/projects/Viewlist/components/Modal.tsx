"use client";

import React, { useState, useEffect } from "react";
import { Input, Textarea, Modal } from "@/components/ui";
import { ProjectData } from "../types";

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (projectData: Pick<ProjectData, "name" | "description">) => void;
  project?: ProjectData;
  title: string;
}

interface CopyProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCopy: (name: string) => void;
  project: ProjectData;
}

// 项目编辑模态框
const ProjectModal: React.FC<ProjectModalProps> = ({
  isOpen,
  onClose,
  onSave,
  project,
  title,
}) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [nameError, setNameError] = useState("");
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件仅在客户端渲染
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 当项目数据变化或模态框打开时重置表单
  useEffect(() => {
    if (isOpen) {
      setName(project?.name || "");
      setDescription(project?.description || "");
      setNameError("");
    }
  }, [isOpen, project]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (e.target.value.trim()) {
      setNameError("");
    }
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setDescription(e.target.value);
  };

  const handleSubmit = () => {
    // 验证
    if (!name.trim()) {
      setNameError("项目名称不能为空");
      return;
    }

    onSave({
      name: name.trim(),
      description: description.trim(),
    });

    // 重置表单
    setName("");
    setDescription("");
    onClose();
  };

  // 检查表单是否有效
  const isFormValid = name.trim() !== "";

  // 避免服务端渲染不匹配
  if (!isMounted) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="md"
      variant="form"
      onSave={handleSubmit}
      saveText="Save"
      isSaveDisabled={!isFormValid}
    >
      <div className="space-y-6">
        <div>
          <Input
            id="project-name"
            label="Project Name"
            value={name}
            onChange={handleNameChange}
            placeholder="Enter project name"
            isInvalid={!!nameError}
            errorMessage={nameError}
            variant="filled"
            size="lg"
          />
        </div>

        <div>
          <Textarea
            id="project-description"
            label="Project Description"
            value={description}
            onChange={handleDescriptionChange}
            placeholder="Enter project description (optional)"
            rows={3}
            variant="filled"
            size="lg"
            fixedHeight={true}
          />
        </div>
      </div>
    </Modal>
  );
};

// 项目复制模态框
const CopyProjectModal: React.FC<CopyProjectModalProps> = ({
  isOpen,
  onClose,
  onCopy,
  project,
}) => {
  const [name, setName] = useState("");
  const [nameError, setNameError] = useState("");
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件仅在客户端渲染
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 当项目数据变化或模态框打开时重置表单
  useEffect(() => {
    if (isOpen && project) {
      setName(`${project.name} Copy`);
      setNameError("");
    }
  }, [isOpen, project]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (e.target.value.trim()) {
      setNameError("");
    }
  };

  const handleSubmit = () => {
    // 验证
    if (!name.trim()) {
      setNameError("Name is required");
      return;
    }

    onCopy(name.trim());

    // 重置表单
    setName("");
    onClose();
  };

  // 检查表单是否有效
  const isFormValid = name.trim() !== "";

  // 避免服务端渲染不匹配
  if (!isMounted) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Duplicate Project"
      size="md"
      variant="form"
      onSave={handleSubmit}
      saveText="Duplicate"
      isSaveDisabled={!isFormValid}
    >
      <div className="space-y-6">
        <div>
          <Input
            id="copy-project-name"
            label="Project Name"
            value={name}
            onChange={handleNameChange}
            placeholder="Enter project name"
            isInvalid={!!nameError}
            errorMessage={nameError}
            variant="filled"
            size="lg"
          />
        </div>
      </div>
    </Modal>
  );
};

// 删除确认模态框接口
interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  projectName: string;
}

// 删除确认模态框
const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  projectName,
}) => {
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件仅在客户端渲染
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 避免服务端渲染不匹配
  if (!isMounted) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Confirm Delete Project?"
      size="md"
      variant="confirm"
      onConfirm={() => {
        onConfirm();
        onClose();
      }}
      confirmText="Delete"
      confirmColor="danger"
    >
      <div className="text-gray-700">
        Are you sure you want to delete <strong>"{projectName}"</strong> ? This
        action cannot be undone.
      </div>
    </Modal>
  );
};

export default ProjectModal;
export { CopyProjectModal, DeleteConfirmModal };
