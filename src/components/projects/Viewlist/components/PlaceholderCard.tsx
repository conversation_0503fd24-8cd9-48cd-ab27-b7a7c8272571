"use client";

import React from "react";

interface PlaceholderCardProps {
  className?: string;
}

/**
 * 占位卡片组件
 * 用于在项目网格视图中填充空白区域，使页面看起来更加饱满
 */
const PlaceholderCard: React.FC<PlaceholderCardProps> = ({ className = "" }) => {
  // 卡片外层容器样式 - 与真实卡片保持一致
  const cardContainerClasses = `w-full h-[180px] ${className}`;

  // 卡片主体样式 - 与真实卡片保持一致但添加占位符特有的样式
  const cardClasses =
    "w-full h-full flex flex-col bg-background/40 relative overflow-hidden rounded-lg opacity-40 pointer-events-none";

  return (
    <div className={cardContainerClasses}>
      <div className={cardClasses}>

        {/* 占位符内容 - 模拟真实卡片的结构 */}
        <div className="p-4 flex flex-col flex-grow overflow-hidden">
        </div>
        
        {/* 底部区域 */}
        <div className="pt-0 pb-3 px-4 flex justify-between items-center">
        </div>
      </div>
    </div>
  );
};

export default PlaceholderCard;