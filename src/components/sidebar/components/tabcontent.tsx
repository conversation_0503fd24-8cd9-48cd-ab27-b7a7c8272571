import React from "react";
import FileList from "./filelist";
import { Region } from "@/lib/api/types/common";
import { FileItem } from "./filelist";

interface TabContentProps {
  activeTabId: string;
  files: any[];
  onFileSelect: (file: any) => void;
  selectedFilePath?: string;
  onFileContextMenu?: (e: React.MouseEvent, file: FileItem) => void;
  projectId?: string;
}

const TabContent: React.FC<TabContentProps> = ({
  activeTabId,
  files,
  onFileSelect,
  selectedFilePath,
  onFileContextMenu,
  projectId,
}) => {
  // console.log("🏷️ TabContent received:", {
  //   projectId,
  //   activeTabId,
  //   filesCount: files.length,
  // });
  // console.log("📄 Files data sample:", files.slice(0, 2));
  // 根据不同的tab id显示不同的内容
  const getContentForTab = (id: string) => {
    switch (id) {
      case "personal":
        return (
          <FileList
            files={files.filter((file) => file.region === Region.PERSONAL)}
            onFileSelect={onFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={onFileContextMenu}
            projectId={projectId}
            region={Region.PERSONAL}
          />
        );
      case "team":
        return (
          <FileList
            files={files.filter((file) => file.region === Region.SHARED)}
            onFileSelect={onFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={onFileContextMenu}
            projectId={projectId}
            region={Region.SHARED}
          />
        );
      case "laws":
        return (
          <FileList
            files={files.filter((file) => file.region === Region.REFERENCE)}
            onFileSelect={onFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={onFileContextMenu}
            projectId={projectId}
            region={Region.REFERENCE}
          />
        );
      case "inbox":
        return (
          <div className="p-4 text-center text-gray-500">
            Inbox content will be displayed here
          </div>
        );
      default:
        return (
          <div className="p-4 text-center text-gray-500">
            Please select a tab to view content
          </div>
        );
    }
  };

  return <div className="h-full w-full">{getContentForTab(activeTabId)}</div>;
};

export default TabContent;
