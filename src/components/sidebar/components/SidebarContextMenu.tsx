import React from "react";
import {
  Edit3,
  <PERSON><PERSON>,
  Trash2,
  FolderPlus,
  FilePlus,
  Info,
  Upload,
  MessageSquarePlus,
} from "lucide-react";
import ContextMenu from "../../ui/DropdownMenu/ContextMenu";
import MenuItem from "../../ui/DropdownMenu/MenuItem";
import {
  ContextMenuData,
  ContextMenuAction,
  ContextMenuType,
} from "../hooks/useSidebarContextMenu";
import { FileItem } from "./filelist";

interface SidebarContextMenuProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  menuData: ContextMenuData | null;
  onAction: (actionId: string, data?: any) => void;
}

/**
 * 统一的 Sidebar 右键菜单组件
 * 根据菜单类型显示不同的菜单项
 */
const SidebarContextMenu: React.FC<SidebarContextMenuProps> = ({
  isOpen,
  setIsOpen,
  menuData,
  onAction,
}) => {
  // 获取菜单项配置
  const getMenuActions = (
    type: ContextMenuType,
    target?: FileItem | null
  ): ContextMenuAction[] => {
    switch (type) {
      case "sidebar-empty":
        return [
          {
            id: "new-folder",
            label: "New Folder",
            icon: FolderPlus,
            onClick: () => onAction("new-folder"),
          },
          {
            id: "new-file",
            label: "New File",
            icon: FilePlus,
            onClick: () => onAction("new-file"),
          },
          {
            id: "separator-1",
            label: "",
            icon: () => null,
            onClick: () => {},
            separator: true,
          },
          {
            id: "import-files",
            label: "Import Files",
            icon: Upload,
            onClick: () => onAction("import-files"),
          },
          {
            id: "separator-2",
            label: "",
            icon: () => null,
            onClick: () => {},
            separator: true,
          },
          {
            id: "project-info",
            label: "Project Info",
            icon: Info,
            onClick: () => onAction("project-info"),
          },
        ];

      case "file":
        return [
          {
            id: "rename",
            label: "Rename",
            icon: Edit3,
            onClick: () => onAction("rename", target),
          },
          {
            id: "duplicate",
            label: "Duplicate",
            icon: Copy,
            onClick: () => onAction("duplicate", target),
          },
          {
            id: "separator-1",
            label: "",
            icon: () => null,
            onClick: () => {},
            separator: true,
          },
          {
            id: "add-to-chat",
            label: "Add to Chat",
            icon: MessageSquarePlus,
            onClick: () => onAction("add-to-chat", target),
          },
          {
            id: "separator-2",
            label: "",
            icon: () => null,
            onClick: () => {},
            separator: true,
          },
          {
            id: "delete",
            label: "Delete",
            icon: Trash2,
            onClick: () => onAction("delete", target),
            textColor: "text-red-500",
          },
        ];

      case "folder":
        return [
          {
            id: "rename",
            label: "Rename",
            icon: Edit3,
            onClick: () => onAction("rename", target),
          },
          {
            id: "duplicate",
            label: "Duplicate",
            icon: Copy,
            onClick: () => onAction("duplicate", target),
          },
          {
            id: "separator-1",
            label: "",
            icon: () => null,
            onClick: () => {},
            separator: true,
          },
          {
            id: "new-file-in-folder",
            label: "New File",
            icon: FilePlus,
            onClick: () => onAction("new-file-in-folder", target),
          },
          {
            id: "new-subfolder",
            label: "New Subfolder",
            icon: FolderPlus,
            onClick: () => onAction("new-subfolder", target),
          },
          {
            id: "separator-2",
            label: "",
            icon: () => null,
            onClick: () => {},
            separator: true,
          },
          {
            id: "delete",
            label: "Delete",
            icon: Trash2,
            onClick: () => onAction("delete", target),
            textColor: "text-red-500",
          },
        ];

      default:
        return [];
    }
  };

  if (!menuData) {
    return null;
  }

  const actions = getMenuActions(menuData.type, menuData.target);

  return (
    <ContextMenu
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      referenceElement={null}
      clickPosition={menuData.position}
      placement="right-start"
      width="w-[160px]"
    >
      {actions.map((action) => {
        if (action.separator) {
          return (
            <div key={action.id} className="h-px bg-foreground/5 my-1 mx-2" />
          );
        }

        const IconComponent = action.icon;

        return (
          <MenuItem
            key={action.id}
            onClick={(e) => {
              e.stopPropagation();
              action.onClick();
              setIsOpen(false);
            }}
            className="flex items-center gap-2"
            textColor={action.textColor}
            disabled={action.disabled}
          >
            <IconComponent className="w-4 h-4" />
            <span>{action.label}</span>
          </MenuItem>
        );
      })}
    </ContextMenu>
  );
};

export default SidebarContextMenu;
