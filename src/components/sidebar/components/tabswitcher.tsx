import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { PersonalIcon } from "@/components/ui/icons/PersonalIcon";
import { TeamSharedIcon } from "@/components/ui/icons/TeamSharedIcon";
import { LawsAndPrecIcon } from "@/components/ui/icons/LawsAndPrecIcon";

export interface Tab {
  id: string;
  icon: "Personal" | "TeamShared" | "LawsAndPrec";
  label: string;
}

interface TabSwitcherProps {
  tabs: Tab[];
  onTabChange: (tabId: string) => void;
}

// 更平缓的动画参数，减少弹跳感
const tabTransition = {
  type: "spring",
  stiffness: 220, // 降低刚度
  damping: 26, // 增加阻尼
  mass: 0.9, // 减轻质量
};

// 文本展开动画更平滑
const labelTransition = {
  type: "spring",
  stiffness: 300, // 降低刚度使动画更平缓
  damping: 30, // 增加阻尼减少震荡
  mass: 0.7, // 减轻质量使动画更轻盈
  restDelta: 0.001, // 更精确的静止检测
};

const TabSwitcher: React.FC<TabSwitcherProps> = ({ tabs, onTabChange }) => {
  const [activeTab, setActiveTab] = useState(tabs[0]?.id || "");
  const [isClient, setIsClient] = useState(false);
  const [tabWidths, setTabWidths] = useState<Record<string, number>>({});
  const labelRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const initialRenderRef = useRef(true);

  // 添加额外的宽度缓冲，防止文字被裁切
  const widthBuffer = 6; // 进一步增加缓冲区

  // 确保组件只在客户端渲染后执行动画
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 使用 useLayoutEffect 确保在浏览器绘制前计算宽度
  useLayoutEffect(() => {
    if (isClient && initialRenderRef.current) {
      initialRenderRef.current = false;

      // 立即计算初始宽度
      const initialWidths: Record<string, number> = {};
      tabs.forEach((tab) => {
        const labelElement = labelRefs.current[tab.id];
        if (labelElement) {
          initialWidths[tab.id] = labelElement.scrollWidth + widthBuffer;
        }
      });

      setTabWidths(initialWidths);
    }
  }, [tabs, widthBuffer, isClient]);

  // 当标签内容变化时重新计算宽度
  useEffect(() => {
    if (isClient && !initialRenderRef.current) {
      const widths: Record<string, number> = {};
      tabs.forEach((tab) => {
        const labelElement = labelRefs.current[tab.id];
        if (labelElement) {
          widths[tab.id] = labelElement.scrollWidth + widthBuffer;
        }
      });
      setTabWidths(widths);
    }
  }, [tabs, widthBuffer, isClient]);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange(tabId);
  };

  // 创建ref回调函数
  const setLabelRef = (id: string) => (el: HTMLDivElement | null) => {
    labelRefs.current[id] = el;
  };

  return (
    <div className="flex items-center gap-1 py-2 mb-2">
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        return (
          <motion.div
            key={tab.id}
            className={`flex items-center rounded-lg text-[13px] transition-colors duration-300 ease-in-out cursor-pointer ${
              isActive
                ? "bg-secondaryBtn text-secondaryBtn-text font-medium"
                : "text-secondaryBtn-inactive hover:bg-secondaryBtn/5 hover:text-secondaryBtn"
            }`}
            style={{ minWidth: "28px" }}
            initial={false}
            animate={{
              paddingLeft: "9px",
              paddingRight: "8px",
              paddingTop: "6px",
              paddingBottom: "6px",
            }}
            transition={tabTransition}
            onClick={() => handleTabClick(tab.id)}
            whileTap={{ scale: 0.96 }} // 减小点击缩放幅度
          >
            <div className="flex items-center">
              <motion.div
                initial={false}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.25 }}
              >
                {tab.icon === "Personal" && <PersonalIcon size={16} />}
                {tab.icon === "TeamShared" && <TeamSharedIcon size={16} />}
                {tab.icon === "LawsAndPrec" && <LawsAndPrecIcon size={16} />}
              </motion.div>

              {isClient && (
                <AnimatePresence initial={false} mode="wait">
                  {isActive && (
                    <motion.div
                      style={{
                        overflow: "hidden",
                        whiteSpace: "nowrap",
                        position: "relative",
                      }}
                      initial={{ width: 0, opacity: 0, marginLeft: 0 }}
                      animate={{
                        width: tabWidths[tab.id] || 0,
                        marginLeft: 8,
                        opacity: 1,
                      }}
                      exit={{
                        width: 0,
                        marginLeft: 0,
                        opacity: 0,
                        transition: {
                          ...labelTransition,
                          opacity: { duration: 0.15, ease: "easeOut" },
                        },
                      }}
                      transition={labelTransition}
                    >
                      {/* 隐藏的元素用于测量宽度 */}
                      <div
                        className="absolute opacity-0 pointer-events-none"
                        ref={setLabelRef(tab.id)}
                      >
                        {tab.label}
                      </div>
                      {/* 实际显示的元素 */}
                      <div className="opacity-100 transition-opacity duration-200">
                        {tab.label}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              )}
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};

export default TabSwitcher;
