import React, { useState, useEffect, useRef } from "react";
import { ArrowUpFromLine, X } from "lucide-react";
import FileIcon from "../../ui/file-icon";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "../../ui/tooltip/tooltip";
import { motion, AnimatePresence } from "framer-motion";
import { InboxIcon } from "@/components/ui/icons/InboxIcon";
import { Trash, RefreshCw, ChevronUp, Check } from "lucide-react";

interface InboxItem {
  id: string;
  path: string;
  type: string;
}

interface InboxProps {
  onFileSelect: (file: InboxItem) => void;
  selectedFilePath?: string;
  activeTab?: string;
}

// 从 path 中提取文件名并去除后缀
const getNameFromPath = (path: string): string => {
  const fileName = path.endsWith("/")
    ? path.slice(0, -1).split("/").pop()
    : path.split("/").pop();
  return (fileName || "").split(".")[0];
};

const Inbox: React.FC<InboxProps> = ({
  onFileSelect,
  selectedFilePath,
  activeTab = "Personal",
}) => {
  const [isInboxOpen, setIsInboxOpen] = useState(false);
  // 多选状态管理
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  // 添加删除确认状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  // 删除按钮引用
  const trashButtonRef = useRef<HTMLDivElement>(null);

  // 监听selectedItems变化，当为空时重置确认状态
  useEffect(() => {
    if (selectedItems.length === 0 && showDeleteConfirm) {
      setShowDeleteConfirm(false);
    }
  }, [selectedItems]);

  // 收件箱示例数据
  const [inboxItems] = useState<InboxItem[]>([
    { id: "inbox-1", path: "/inbox/new-document.docx", type: "file" },
    {
      id: "inbox-2",
      path: "/inbox/绿能科技重大资产重组法律意见书意见书意见书意见书.pdf",
      type: "file",
    },
    { id: "inbox-3", path: "/inbox/team-updates.png", type: "file" },
    { id: "inbox-4", path: "/inbox/team-updates-2.md", type: "file" },
    { id: "inbox-5", path: "/inbox/team-updates-3.md", type: "file" },
    { id: "inbox-6", path: "/inbox/team-updates-4.md", type: "file" },
    { id: "inbox-7", path: "/inbox/team-updates-5.md", type: "file" },
    { id: "inbox-8", path: "/inbox/team-updates-6.md", type: "file" },
    { id: "inbox-9", path: "/inbox/team-updates-7.md", type: "file" },
    { id: "inbox-10", path: "/inbox/team-updates-8.md", type: "file" },
    { id: "inbox-11", path: "/inbox/team-updates-9.md", type: "file" },
    { id: "inbox-12", path: "/inbox/team-updates-10.md", type: "file" },
  ]);

  // 公共样式定义
  const activeClass = "text-secondaryBtn";
  const inactiveClass = "text-secondaryBtn-inactive group-hover:text-secondaryBtn";

  // 处理文件选择
  const handleItemSelect = (item: InboxItem, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡

    // 切换选中状态
    setSelectedItems((prev) => {
      // 检查是否是取消选中最后一个项目
      const isRemovingLast = prev.length === 1 && prev.includes(item.id);

      // 如果是取消选中最后一个项目，重置删除确认状态
      if (isRemovingLast) {
        setShowDeleteConfirm(false);
      }

      if (prev.includes(item.id)) {
        return prev.filter((id) => id !== item.id);
      } else {
        return [...prev, item.id];
      }
    });

    // 如果需要同时触发文件选择功能，可以取消下面的注释
    // onFileSelect(item);
  };

  // 处理复选框点击
  const handleCheckboxClick = (itemId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡

    setSelectedItems((prev) => {
      // 检查是否是取消选中最后一个项目
      const isRemovingLast = prev.length === 1 && prev.includes(itemId);

      // 如果是取消选中最后一个项目，重置删除确认状态
      if (isRemovingLast) {
        setShowDeleteConfirm(false);
      }

      if (prev.includes(itemId)) {
        return prev.filter((id) => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };

  // 处理删除选中项
  const handleDeleteSelected = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡

    if (showDeleteConfirm) {
      // 如果已经在确认状态，执行删除
      console.log("删除选中项:", selectedItems);
      setSelectedItems([]);
      setShowDeleteConfirm(false);
    } else {
      // 否则，显示确认按钮
      setShowDeleteConfirm(true);
    }
  };

  // 处理取消删除
  const handleCancelDelete = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    setShowDeleteConfirm(false);
  };

  // 处理添加新项
  const handleAddNew = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    // 这里可以添加实际的添加逻辑
    console.log("添加新项");
  };

  // 处理刷新
  const handleRefresh = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    // 这里可以添加实际的刷新逻辑
    console.log("刷新收件箱");
  };

  // 处理删除按钮点击
  const handleTrashClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);

      // 通过模拟鼠标离开事件来隐藏tooltip
      if (trashButtonRef.current) {
        const mouseLeaveEvent = new MouseEvent("mouseleave", {
          bubbles: true,
          cancelable: true,
        });
        trashButtonRef.current.dispatchEvent(mouseLeaveEvent);
      }
    }
  };

  return (
    <>
      {selectedItems.length > 0 && (
        <div className="mb-2 px-2 overflow-hidden">
          <AnimatePresence mode="wait">
            {showDeleteConfirm ? (
              <motion.div
                className="flex items-center justify-between gap-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.15 }}
                key="delete-confirm"
              >
                <Tooltip delay={1000}>
                  <TooltipTrigger asChild>
                    <motion.div
                      className="flex items-center justify-center px-2 py-1 h-8 rounded-lg bg-secondaryBtn/5 dark:bg-background/80 hover:bg-secondaryBtn/10 dark:hover:bg-gray-50/5 cursor-pointer text-secondaryBtn/85 dark:text-foreground/75 transition-all duration-150 flex-1"
                      onClick={handleCancelDelete}
                      whileTap={{ scale: 0.95 }}
                    >
                      <X size={13} className="mr-1.5" />
                      <span className="text-xs font-medium">Cancel</span>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>Cancel Delete</TooltipContent>
                </Tooltip>

                <Tooltip delay={1000}>
                  <TooltipTrigger asChild>
                    <motion.div
                      className="flex items-center justify-center px-2 py-1 h-8 rounded-lg bg-red-500/10 hover:bg-red-500/20 cursor-pointer text-red-500 transition-all duration-150 flex-1"
                      onClick={handleDeleteSelected}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Trash size={13} className="mr-1.5" />
                      <span className="text-xs font-medium">
                        Delete {selectedItems.length}
                      </span>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>Confirm Delete</TooltipContent>
                </Tooltip>
              </motion.div>
            ) : (
              <motion.div
                key="import-button"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.15 }}
              >
                <Tooltip delay={500}>
                  <TooltipTrigger asChild>
                    <motion.div
                      className={`flex items-center justify-center px-2 py-1 h-8 rounded-lg bg-secondaryBtn/5 dark:bg-background/80 hover:bg-secondaryBtn/10 dark:hover:bg-background cursor-pointer text-secondaryBtn w-full`}
                      onClick={handleAddNew}
                      whileTap={{ scale: 0.95 }}
                    >
                      <ArrowUpFromLine size={13} className="mr-1.5" />
                      <span className="text-xs font-medium">
                        Import to {activeTab}
                      </span>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>
                    Import Selected into {activeTab}
                  </TooltipContent>
                </Tooltip>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
      <div
        className={`mt-auto overflow-hidden transition-all duration-300 ${
          isInboxOpen
            ? "bg-secondaryBtn/5 dark:bg-background/80 rounded-xl"
            : "bg-transparent rounded-lg"
        }`}
      >
        <div
          className="flex items-center px-3 py-2 cursor-pointer hover:bg-secondaryBtn/5 dark:hover:bg-background transition-all duration-300 group"
          onClick={() => {
            const newIsOpen = !isInboxOpen;
            setIsInboxOpen(newIsOpen);
            if (!newIsOpen) {
              // 如果折叠，则清空选中项
              setSelectedItems([]);
              setShowDeleteConfirm(false);
            }
          }}
        >
          <div className="flex items-center justify-between w-full">
            <div
              className={`flex items-center transition-all duration-300 ${
                isInboxOpen || inboxItems.length > 0
                  ? activeClass
                  : inactiveClass
              }`}
            >
              <InboxIcon size={16} className="mr-2" />
              <span className="font-semibold text-sm">Inbox</span>
              <div className="ml-1.5 flex items-center justify-center min-w-[18px] h-[18px] rounded-full bg-secondaryBtn/[0.08] px-1">
                <span
                  className={`text-xs font-medium transition-all duration-300 ${
                    isInboxOpen || inboxItems.length > 0
                      ? activeClass
                      : inactiveClass
                  }`}
                >
                  {inboxItems.length}
                </span>
              </div>
            </div>
            <div className="flex items-center">
              <div className="flex items-center">
                <Tooltip delay={500}>
                  <TooltipTrigger asChild>
                    <div
                      ref={trashButtonRef}
                      className={`mr-1 w-6 h-6 flex items-center justify-center rounded-lg ${
                        showDeleteConfirm
                          ? "bg-secondaryBtn/10 text-secondaryBtn opacity-50 cursor-not-allowed"
                          : "hover:bg-red-500/10 cursor-pointer text-secondaryBtn hover:text-red-500"
                      } transform transition-all duration-150 origin-center ${
                        selectedItems.length > 0 && isInboxOpen
                          ? "scale-100 opacity-100"
                          : "scale-75 opacity-0 pointer-events-none"
                      }`}
                      onClick={handleTrashClick}
                    >
                      <Trash size={14} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    {showDeleteConfirm ? "Please Confirm" : "Delete Selected"}
                  </TooltipContent>
                </Tooltip>

                <Tooltip delay={500}>
                  <TooltipTrigger asChild>
                    <div
                      className={`mr-2 w-6 h-6 flex items-center justify-center rounded-lg hover:bg-secondaryBtn/10 cursor-pointer ${activeClass} transform transition-all duration-150 origin-center ${
                        isInboxOpen
                          ? "scale-100 opacity-100"
                          : "scale-75 opacity-0 pointer-events-none"
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRefresh(e);
                        if (showDeleteConfirm) {
                          setShowDeleteConfirm(false);
                        }
                      }}
                    >
                      <RefreshCw size={14} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>Refresh</TooltipContent>
                </Tooltip>
              </div>
              <div
                className={`transition-all duration-300 ${
                  isInboxOpen ? activeClass : inactiveClass
                }`}
              >
                <ChevronUp
                  size={16}
                  className={`transform transition-transform -translate-y-0.5 ${
                    isInboxOpen ? "rotate-180" : ""
                  }`}
                />
              </div>
            </div>
          </div>
        </div>

        {/* 收件箱内容 */}
        <div
          className={`overflow-hidden transition-all duration-300 ${
            isInboxOpen
              ? "max-h-64 opacity-100"
              : "max-h-0 opacity-0 border-t-0"
          }`}
        >
          <div className="pt-1 pb-2 space-y-0.5 max-h-64 overflow-y-auto slim-scrollbar">
            {inboxItems.map((item) => (
              <div
                key={item.id}
                className={`flex items-center justify-between mx-2 px-2 py-2 rounded-md cursor-pointer text-sm ${
                  selectedItems.includes(item.id)
                    ? "bg-secondaryBtn/5"
                    : "hover:bg-secondaryBtn/5"
                } group`}
                onClick={(e) => handleItemSelect(item, e)}
              >
                <div className="flex items-center flex-1 min-w-0">
                  <FileIcon
                    fileName={getNameFromPath(item.path)}
                    filePath={item.path}
                    fileType={item.type as "file" | "folder"}
                    className="h-4 w-4 mr-1.5 text-foreground opacity-50 flex-shrink-0"
                  />
                  <span className="truncate text-foreground/85">
                    {getNameFromPath(item.path)}
                  </span>
                </div>
                <div>
                  {selectedItems.includes(item.id) && (
                    <Check size={14} className="text-secondaryBtn" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default Inbox;
