/**
 * 文件/文件夹操作模态框组件
 * 支持创建文件夹、创建文件、删除确认等操作
 */

import React, { useState, useEffect } from "react";
import { Modal, Input } from "@/components/ui";
import { validateFolderName } from "../../../utils/virtualFolders";

export type OperationType =
  | "create-folder"
  | "create-file"
  | "delete"
  | "rename";

interface FileOperationModalProps {
  isOpen: boolean;
  onClose: () => void;
  operation: OperationType;
  onConfirm: (value?: string) => void;
  targetName?: string; // 用于删除确认或重命名时显示目标名称
  defaultValue?: string; // 用于重命名时的默认值
  parentPath?: string; // 用于显示创建位置
  deleteInfo?: {
    isFolder: boolean;
    fileCount?: number;
    folderCount?: number;
  }; // 用于删除时显示详细信息
}

const FileOperationModal: React.FC<FileOperationModalProps> = ({
  isOpen,
  onClose,
  operation,
  onConfirm,
  targetName,
  defaultValue = "",
  parentPath,
  deleteInfo,
}) => {
  const [inputValue, setInputValue] = useState(defaultValue);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // 重置状态当模态框打开/关闭时
  useEffect(() => {
    if (isOpen) {
      setInputValue(defaultValue);
      setError("");
      setIsLoading(false);
    }
  }, [isOpen, defaultValue]);

  // 获取模态框配置
  const getModalConfig = () => {
    switch (operation) {
      case "create-folder":
        return {
          title: "Create New Folder",
          confirmText: "Create",
          variant: "form" as const,
          needsInput: true,
          placeholder: "Folder name...",
          validator: validateFolderName,
        };
      case "create-file":
        return {
          title: "Create New File",
          confirmText: "Create",
          variant: "form" as const,
          needsInput: true,
          placeholder: "File name...",
        };
      case "delete":
        return {
          title: "Delete Item",
          confirmText: "Delete",
          variant: "confirm" as const,
          confirmColor: "danger" as const,
          needsInput: false,
        };
      case "rename":
        return {
          title: "Rename Item",
          confirmText: "Rename",
          variant: "form" as const,
          needsInput: true,
          placeholder: "New name...",
        };
      default:
        return {
          title: "Confirm",
          confirmText: "OK",
          variant: "confirm" as const,
          needsInput: false,
        };
    }
  };

  const config = getModalConfig();

  // 验证输入
  const validateInput = (value: string): boolean => {
    if (!config.needsInput) return true;

    if (!value.trim()) {
      setError("Name cannot be empty");
      return false;
    }

    if (config.validator) {
      const result = config.validator(value.trim());
      if (!result.isValid) {
        setError(result.error || "Invalid input");
        return false;
      }
    }

    setError("");
    return true;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // 实时验证
    if (value.trim()) {
      validateInput(value.trim());
    } else {
      setError("");
    }
  };

  // 处理确认
  const handleConfirm = async () => {
    if (config.needsInput && !validateInput(inputValue.trim())) {
      return;
    }

    setIsLoading(true);

    try {
      await onConfirm(config.needsInput ? inputValue.trim() : undefined);
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // 渲染内容
  const renderContent = () => {
    if (operation === "delete") {
      const isFolder = deleteInfo?.isFolder || false;
      const fileCount = deleteInfo?.fileCount || 0;
      const folderCount = deleteInfo?.folderCount || 0;

      console.log("📋 Modal delete info:", {
        isFolder,
        fileCount,
        folderCount,
        deleteInfo,
      });

      return (
        <div className="space-y-4">
          <p className="text-foreground/80">
            Are you sure you want to delete <strong>"{targetName}"</strong>?
          </p>

          {isFolder && (fileCount > 0 || folderCount > 0) && (
            <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-3">
              <p className="text-sm text-orange-800 dark:text-orange-200 font-medium mb-2">
                This folder contains:{" "}
                <strong>
                  {[
                    folderCount > 0 &&
                      `${folderCount} subfolder${folderCount > 1 ? "s" : ""}`,
                    fileCount > 0 &&
                      `${fileCount} file${fileCount > 1 ? "s" : ""}`,
                  ]
                    .filter(Boolean)
                    .join(" and ")}
                </strong>
              </p>
              <p className="text-sm text-orange-800 dark:text-orange-200">
                All contents will be permanently deleted.
              </p>
            </div>
          )}

          <p className="text-sm text-foreground/60">
            This action cannot be undone.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {parentPath && (
          <p className="text-sm text-foreground/60">
            Location: <span className="font-mono">{parentPath}</span>
          </p>
        )}

        <div className="space-y-2">
          <Input
            value={inputValue}
            onChange={handleInputChange}
            placeholder={config.placeholder}
            maxLength={255}
            autoFocus
            error={error}
          />

          {/* 字符计数 */}
          <div className="text-xs text-foreground/50 text-right">
            {inputValue.length}/255
          </div>
        </div>
      </div>
    );
  };

  // 检查表单是否有效
  const isFormValid = config.needsInput
    ? inputValue.trim() !== "" && !error
    : true;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={config.title}
      size="md"
      variant={config.variant}
      onSave={config.variant === "form" ? handleConfirm : undefined}
      onConfirm={config.variant === "confirm" ? handleConfirm : undefined}
      saveText={config.confirmText}
      confirmText={config.confirmText}
      confirmColor={config.confirmColor}
      isSaveDisabled={!isFormValid}
      isConfirmDisabled={!isFormValid}
      isSaveLoading={isLoading}
      isConfirmLoading={isLoading}
    >
      {renderContent()}
    </Modal>
  );
};

export default FileOperationModal;
