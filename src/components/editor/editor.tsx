"use client";

import * as React from "react";
import { Editor<PERSON><PERSON><PERSON>, EditorContext, useEditor } from "@tiptap/react";
import { StarterKit } from "@tiptap/starter-kit";
import { TextAlign } from "@tiptap/extension-text-align";
import { Highlight } from "@tiptap/extension-highlight";
import { CharacterCount } from "@tiptap/extensions";
import { TextStyle } from "@tiptap/extension-text-style";
import { FontFamily } from "@tiptap/extension-font-family";
import { useEffect, useRef } from "react";

// 导入自定义扩展
import Title from "@/components/editor/extensions/title-extension";
import Indent from "@/components/editor/extensions/indent-extension";

// 导入编辑器组件
import {
  Toolbar,
  EditorFooterBar,
  QuickActionBar,
} from "@/components/editor/components";

// 导入自定义 hooks
import { useTextSelection } from "@/hooks/editor/use-text-selection";

// 导入编辑器样式
import "@/styles/_variables.scss";
import "@/styles/_keyframe-animations.scss";
import "@/styles/editor.scss";

// 字体大小预设
export const FONT_SIZE_SMALL = 0.85;
export const FONT_SIZE_MEDIUM = 1.0;
export const FONT_SIZE_LARGE = 1.2;

// 修改编辑器组件的接口，添加onEditorReady回调属性
interface EditorProps {
  onEditorReady?: (editor: any) => void;
  projectId?: string; // 项目ID，用于加载和保存项目相关的内容
}

/**
 * 编辑器组件
 * 包含文本格式化工具栏和内容编辑区域
 */
const Editor: React.FC<EditorProps> = ({ onEditorReady, projectId }) => {
  // 状态用于存储字数
  const [characterCount, setCharacterCount] = React.useState<number>(0);
  // 状态用于存储当前主题
  const [theme, setTheme] = React.useState<"light" | "dark">("light");
  // 状态用于存储字体大小
  const [fontSize, setFontSize] = React.useState<number>(FONT_SIZE_MEDIUM);
  // 用于存储用户手动设置的字体大小
  const [userSelectedFontSize, setUserSelectedFontSize] = React.useState<
    number | null
  >(null);
  // 当前是否是移动端宽度
  const [isMobileWidth, setIsMobileWidth] = React.useState<boolean>(false);
  // 创建编辑器容器的引用
  const editorContainerRef = React.useRef<HTMLDivElement>(null);
  // 状态用于存储屏幕宽度
  const [is2XL, setIs2XL] = React.useState<boolean>(false);
  const editorRef = useRef<HTMLDivElement>(null);
  // 用于跟踪是否是自动调整的字体大小
  const isAutoAdjusting = useRef<boolean>(false);

  // 初始化和监听屏幕尺寸变化
  React.useEffect(() => {
    // 仅在客户端执行
    if (typeof window !== "undefined") {
      const checkScreenSize = () => {
        const width = window.innerWidth;
        const newIs2XL = width >= 1920;
        const newIsMobileWidth = width < 540;

        setIs2XL(newIs2XL);

        // 只有当移动端状态发生变化时才调整字体大小
        if (newIsMobileWidth !== isMobileWidth) {
          setIsMobileWidth(newIsMobileWidth);

          if (newIsMobileWidth) {
            // 进入移动端宽度
            if (fontSize !== FONT_SIZE_SMALL) {
              // 记录当前字号，以便后续恢复
              setUserSelectedFontSize(fontSize);
              isAutoAdjusting.current = true;
              setFontSize(FONT_SIZE_SMALL);
            }
          } else {
            // 退出移动端宽度
            if (userSelectedFontSize && fontSize === FONT_SIZE_SMALL) {
              isAutoAdjusting.current = true;
              setFontSize(userSelectedFontSize);
              setUserSelectedFontSize(null);
            } else if (fontSize === FONT_SIZE_SMALL) {
              isAutoAdjusting.current = true;
              setFontSize(FONT_SIZE_MEDIUM);
            }
          }
        }
      };

      // 初始检查
      checkScreenSize();

      // 监听窗口大小变化
      window.addEventListener("resize", checkScreenSize);
      return () => {
        window.removeEventListener("resize", checkScreenSize);
      };
    }
  }, [fontSize, isMobileWidth, userSelectedFontSize]);

  // 自定义的设置字体大小函数
  const handleFontSizeChange = React.useCallback(
    (newSize: number) => {
      // 用户手动设置字体大小
      setFontSize(newSize);

      // 如果当前不是移动端宽度，或者用户选择了小字号，记录这个选择
      if (!isMobileWidth || newSize === FONT_SIZE_SMALL) {
        setUserSelectedFontSize(newSize);
      }
    },
    [isMobileWidth]
  );

  // 监听主题变化
  useEffect(() => {
    // 初始化时获取当前主题
    const savedTheme = localStorage.getItem("theme") as "light" | "dark" | null;
    const currentTheme = savedTheme || "light";
    setTheme(currentTheme);

    // 创建 MutationObserver 监听 html 元素的 class 变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          const htmlElement = document.documentElement;
          const newTheme = htmlElement.classList.contains("dark")
            ? "dark"
            : "light";
          setTheme(newTheme);
        }
      });
    });

    // 开始观察
    observer.observe(document.documentElement, { attributes: true });

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);

  // 初始化编辑器
  const editor = useEditor({
    extensions: [
      StarterKit,
      Title,
      TextAlign.configure({
        types: ["heading", "paragraph", "title"],
      }),
      Highlight.configure({ multicolor: true }),
      CharacterCount.configure({}),
      TextStyle,
      FontFamily,
      Indent.configure({
        types: [
          "paragraph",
          "heading",
          "listItem",
          "bulletList",
          "orderedList",
        ],
        defaultIndent: "2em",
      }),
    ],
    shouldRerenderOnTransaction: true, // v3 新特性，允许编辑器状态变化时重新渲染
    immediatelyRender: false, // 解决 SSR 水合错误
    content: `
      <h1 class="title-heading" style="text-align: center"><span style="font-family: &quot;Noto Serif SC&quot;">天衡联合律师事务所</span></h1><h1 class="title-heading" style="text-align: center"><span style="font-family: &quot;Noto Serif SC&quot;">关于绿能科技股份有限公司</span></h1><h1 class="title-heading" style="text-align: center"><span style="font-family: &quot;Noto Serif SC&quot;">重大资产重组暨重整计划执行完毕的</span></h1><h1 class="title-heading" style="text-align: center"><span style="font-family: &quot;Noto Serif SC&quot;">法律意见书</span></h1><h1 class="title-heading" style="text-align: center"><span style="font-family: &quot;Noto Serif SC&quot;">&nbsp;</span></h1><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>致：绿能科技股份有限公司</strong></span></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">天衡联合律师事务所（以下简称"本所"）接受绿能科技股份有限公司（下称"绿能科技"或"公司"）委托，根据《中华人民共和国公司法》《中华人民共和国证券法》《中华人民共和国企业破产法》《上市公司重大资产重组管理办法》《上海证券交易所股票上市规则》等法律、行政法规、部门规章及其他规范性文件的有关规定，就公司重大资产重组及重整计划执行完毕事宜出具本法律意见书。</span><br><br class="ProseMirror-trailingBreak"></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>声明事项</strong></span></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">为出具本法律意见书之目的，本所律师声明如下：</span></p><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">本所律师依据现行有效的中国法律、法规及规范性文件，并结合本法律意见书出具之日前已发生或存在的事实发表法律意见；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">本所已对与出具本法律意见书相关的文件、资料进行必要核查，并已取得公司及管理人出具的书面承诺，保证其提供文件的真实性、完整性和准确性；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">本法律意见书仅就本次重大资产重组及重整计划执行完毕的合法性发表意见，不涉及对财务数据、技术评估等专业事项的实质性判断；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">本法律意见书仅供公司本次重大资产重组及重整计划执行完毕之目的使用，未经本所书面许可不得用于其他用途。</span><br><br class="ProseMirror-trailingBreak"></p></li></ol><h1 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">一、本次重大资产重组及重整的基本事实</span></h1><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(一) 重整程序的启动</span></h2><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">2024年5月15日，江苏省南京市中级人民法院（下称"南京中院"）作出（2024）苏01破申78号《民事裁定书》，裁定受理中国银行南京分行对绿能科技的重整申请，并指定由南京市金融监督管理局、天衡联合律师事务所组成的联合清算组担任管理人。</span><br><br class="ProseMirror-trailingBreak"></p><h1 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">二、债权人会议及重整计划表决</span></h1><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(一) 第一次债权人会议召开程序</span></h2><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>会议时间与地点：</strong></span></p></li></ol><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">2024年8月20日，公司第一次债权人会议于南京市中级人民法院第三法庭召开，会议采取"现场+网络"双通道形式进行，共有 127家债权人 参与表决（占申报债权总额的93.6%），符合《企业破产法》第六十四条关于"出席会议的有表决权的债权人过半数"的法定条件。</span></p><ol start="2"><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>债权申报与核查：</strong></span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">截至2024年7月31日，管理人共接受 156家债权人 申报债权，总额为 28.7亿元，其中经审查确认的债权金额为 24.3亿元（含：有财产担保债权12.5亿元、普通债权10.2亿元、职工债权1.6亿元）；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">未确认债权（4.4亿元）已通过书面异议程序处理，并于2024年8月10日经南京中院裁定确认。</span></p></li></ul><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(二) 分组表决结果</span></h2><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">根据《企业破产法》第八十二条，债权人会议对重整计划草案分组表决如下：</span></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">（表格）</span></p><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(三) 出资人组会议表决</span></h2><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">2024年9月10日，公司召开出资人组会议（同步通过上海证券交易所股东大会网络投票系统表决），审议《出资人权益调整方案》，主要内容包括：</span></p><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">资本公积金转增股本：以公司总股本6.4亿股为基数，按每10股转增4.375股的比例实施转增，共计转增2.8亿股，全部用于引入重整投资人；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">表决结果：</span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">现场出席会议股东持股比例占公司总股本的41.2%；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">网络投票赞成票占出席会议股东所持表决权的88.6%，符合《公司法》第一百零三条"出席会议股东所持表决权2/3以上通过"的规定。</span></p></li></ul><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(四) 法院裁定批准</span></h2><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">2024年9月25日，南京中院作出（2024）苏01破78号《民事裁定书》，认为：</span></p><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">重整计划草案已依法经各表决组通过，且出资人权益调整程序合法；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">重整计划内容不损害债权人、出资人及职工合法权益，符合《企业破产法》第八十六条规定的批准条件，故裁定批准重整计划并终止重整程序。</span></p></li></ol><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(五) 法院批准及计划执行</span></h2><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">2024年9月25日，南京中院作出（2024）苏01破78号《民事裁定书》，批准《绿能科技重整计划》并终止重整程序。</span></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">截至2024年12月28日，重整计划执行情况如下：</span></p><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">资金到位：长江新能源产业基金支付的18亿元重整投资款已全额划入管理人账户；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">股份登记：2.8亿股转增股份已登记至管理人专用账户，公司总股本增至9.2亿股；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">资产剥离：不符合新能源主业规划的6处光伏电站资产已以7.2亿元对价剥离至江苏国信能源集团，并完成产权交割；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">合规性文件：国家能源局江苏监管办公室出具《关于绿能科技资产重组符合行业政策的确认函》，确认本次重组符合《可再生能源电站资产处置指引》要求。</span><br><br class="ProseMirror-trailingBreak"></p></li></ol><h1 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">三、法律分析</span></h1><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(一) 重整计划执行完毕的法定标准及合规性论证</span></h2><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">根据《中华人民共和国企业破产法》第九十四条、第九十六条及《最高人民法院关于适用〈企业破产法〉若干问题的规定（三）》第十五条，重整计划执行完毕的法定标准需满足以下核心要件：</span></p><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">重整计划条款的全面履行：包括但不限于债务清偿、资产处置、股权调整等核心义务已按计划完成；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">债权人权益的实质保障：清偿方案不得损害债权人法定优先权，且普通债权人的受偿比例不得低于破产清算状态下的模拟清偿率；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">行业监管及行政审批的合规性：涉及特殊行业（如能源、环保）的资产重组，需取得主管部门的书面批准或备案文件；</span></p></li><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">司法程序的完备性：重整计划的表决、法院裁定及执行过程符合法定程序，不存在重大程序瑕疵。</span></p></li></ol><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">结合本案事实，具体分析如下：</span></p><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>重整计划的全面履行</strong></span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">债务清偿：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">1)&nbsp;&nbsp; 有财产担保债权组中，国家开发银行南京分行等5家金融机构的留债展期协议已根据《民法典》第四百一十八条完成担保物权登记续展，确保担保效力的延续性；</span></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">2)&nbsp;&nbsp; 普通债权组中，小额债权人（≤50万元）的现金清偿比例达100%，符合《最高人民法院关于推进破产案件依法高效审理的意见》中"优先保障小额债权人权益"的司法政策导向；</span></p><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">3)&nbsp;&nbsp; 职工债权及税款债权的全额清偿方案已通过南京市人力资源和社会保障局、国家税务总局南京市税务局的书面确认，未引发任何劳动争议或税务纠纷。</span></p><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">股权结构调整：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">资本公积金转增股本的方案（每10股转增4.375股）已根据《上市公司证券发行管理办法》第三十九条履行股东大会决议及证券交易所的信息披露义务（公告编号：2024-056），转增股份的登记程序经中国证券登记结算有限责任公司上海分公司审核确认（业务确认书编号：ZJZJ2024-1123）。</span></p><ol start="2"><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">债权人权益的实质公平性</span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">清算状态下的模拟清偿率对比：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">根据管理人委托的第三方评估机构（江苏华信资产评估有限公司）出具的《破产清算价值评估报告》（报告编号：HXPG2024-0789），若公司进入破产清算程序，普通债权人的模拟清偿率仅为12.3%；而重整计划中普通债权人的现金及股份综合清偿率提升至28.5%，显著高于清算状态，符合《企业破产法》第八十七条第二款第（三）项关于"重整计划公平对待同一表决组成员"的强制性要求。</span></p><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">留债展期条款的合法性：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">有财产担保债权的展期利率（LPR+1.5%）未超过《最高人民法院关于审理民间借贷案件适用法律若干问题的规定》第二十五条规定的LPR四倍上限（当前LPR为3.85%，四倍上限为15.4%），且展期期限（5年）与担保物剩余使用寿命（光伏厂房评估剩余使用年限为20年）相匹配，符合《民法典》第四百二十条关于"担保物权行使期限合理性"的规定。</span></p><ol start="3"><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">行业监管合规性</span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">资产剥离的合规程序：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">公司剥离的6处光伏电站资产已根据国家能源局《可再生能源电站资产处置指引》第七条，完成资产清单公示（公示平台：全国新能源资产交易中心，公示期15日）、第三方评估（评估机构：中联资产评估集团，报告编号：ZLPG2024-0456）及产权交割程序，取得国家能源局江苏监管办公室的备案回执（备案号：NEA-JS-2024-0456）。</span></p><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">新注入资产的环评合规性：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">重整投资人长江新能源产业基金注入的储能电池生产线项目，已通过生态环境部组织的环境影响评价（批复文号：环审〔2024〕987号），其污染物排放标准符合《电池工业污染物排放标准》（GB 30484-2013）的要求，且项目选址避让生态保护红线，未引发周边居民环境权益诉讼。</span></p><ol start="4"><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>司法程序的完备性</strong></span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">债权人会议程序合法性：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">第一次债权人会议的召集程序（提前15日公告）、表决方式（现场+网络双通道）及计票规则（按债权金额加权计算）均符合《企业破产法》第六十四条、第八十四条的规定，且网络投票过程经上海市东方公证处全程公证（公证书号：2024沪东证字第5567号），排除了程序性争议风险。</span></p><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">法院裁定的法律依据充分性：</span></p></li></ul><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">南京中院（2024）苏01破78号《民事裁定书》明确援引《企业破产法》第八十六条、第八十七条作为批准重整计划的核心依据，同时结合《全国法院破产审判工作会议纪要》第十八条关于"重整计划可行性审查"的要求，认定公司通过剥离低效资产、引入战略投资者已恢复持续经营能力，裁定理由充分且无法律适用错误。</span></p><h2 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">(二) 潜在法律风险排除及持续性合规建议</span></h2><ol><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>留债展期协议的履约风险防范</strong></span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">担保物价值动态监控：建议公司与管理人建立担保物价值年度评估机制（依据《企业破产法》第七十五条），若担保物市场价值下降至留债余额的120%以下时，及时启动补充担保或提前清偿程序，避免触发《民法典》第四百一十三条规定的担保物权实现条件。</span></p></li></ul><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">利率调整机制：留债展期协议中约定利率与LPR浮动挂钩（如每12个月根据最新LPR调整一次），符合《中国人民银行公告〔2019〕第15号》关于"贷款市场报价利率改革"的政策要求，可规避未来利率波动导致的争议。</span></p></li></ul><ol start="2"><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>证券监管持续性义务</strong></span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">重整计划执行完毕后的信息披露：公司需根据《上市公司信息披露管理办法》第四十五条，在法院裁定重整计划执行完毕后2个交易日内披露专项公告，并持续披露战略投资者注资进展、主营业务调整等重大事项，避免因信息滞后引发证券交易所监管问询。</span></p></li></ul><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">股份锁定期的合规安排：重整投资人通过资本公积金转增取得的2.8亿股股份，需根据《上市公司收购管理办法》第七十四条承诺36个月内不得转让，并在权益变动报告书（公告编号：2024-060）中明确披露，以符合证券监管部门对"维护中小股东权益"的监管要求。</span></p></li></ul><ol start="3"><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;"><strong>行业政策合规性维持</strong></span></p></li></ol><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">可再生能源补贴申领：公司保留的12处光伏电站资产需根据《可再生能源电价附加资金管理办法》第十六条，按时向财政部江苏监管局提交补贴申领材料，确保现金流稳定性；</span></p></li></ul><ul><li><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">碳排放配额管理：新建储能电池项目需根据《碳排放权交易管理办法》第二十一条，在投产前完成年度碳排放配额核定（核定机构：江苏省生态环境厅），避免因配额不足导致生产经营受限。</span><br><br class="ProseMirror-trailingBreak"></p></li></ul><h1 style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">四、结论意见</span></h1><p style="text-align: justify"><span style="font-family: &quot;Noto Serif SC&quot;">本所认为，绿能科技本次重大资产重组及重整计划的制定、表决、法院批准及执行过程，均符合《企业破产法》《证券法》《民法典》及相关行业监管法规的强制性规定，未发现实质性法律瑕疵；现有法律风险防控措施及持续性合规建议可有效保障公司恢复经营后的稳定运营，南京中院（2024）苏01破78号《民事裁定书》确认的重整计划执行完毕状态具有法律效力。</span></p><p style="text-align: right"><br><br><br class="ProseMirror-trailingBreak"></p><p style="text-align: right"><span style="font-family: &quot;Noto Serif SC&quot;">天衡联合律师事务所</span></p><p style="text-align: right"><span style="font-family: &quot;Noto Serif SC&quot;">经办律师：张某某</span></p><p style="text-align: right"><span style="font-family: &quot;Noto Serif SC&quot;">2024年12月30日</span></p><p style="text-align: left"><span style="font-family: &quot;Noto Serif SC&quot;">（注：本文件为虚构示例，律师事务所名称、案件细节及相关主体均属虚构，不指向任何真实机构或个人。）</span></p>
    `,
    editorProps: {
      attributes: {
        class:
          "prose prose-sm sm:prose focus:outline-none w-[360px] min-w-[360px] xxs:w-[400px] xxs:min-w-[400px] xs:w-[440px] xs:min-w-[440px] sm:w-[500px] sm:min-w-[500px] md:w-[700px] md:min-w-[700px] 2xl:w-[860px] 2xl:min-w-[860px] mx-auto px-4 mt-14 mb-10 font-noto-sans",
      },
    },
    onUpdate: ({ editor }) => {
      // 更新字数
      if (editor) {
        // v3 中使用 storage API 访问 characterCount 扩展
        setCharacterCount(editor.storage.characterCount.characters());
      }
    },
    onCreate: ({ editor }) => {
      // 编辑器创建后立即更新字数，确保默认content的字数统计
      if (editor) {
        setCharacterCount(editor.storage.characterCount.characters());
      }
    },
  });

  // 编辑器初始化后立即触发一次字数统计更新
  React.useEffect(() => {
    if (editor) {
      setCharacterCount(editor.storage.characterCount.characters());
    }
  }, [editor]);

  // 使用文本选择检测 hook
  const textSelection = useTextSelection(editor);

  // 处理快速操作按钮点击事件
  const handleAddToChat = React.useCallback((text: string) => {
    // TODO: 实现添加到聊天功能
    console.log("Add to chat:", text);
  }, []);

  const handleQuickEdit = React.useCallback((text: string) => {
    // TODO: 实现快速编辑功能
    console.log("Quick edit:", text);
  }, []);

  // 当编辑器准备好时触发回调
  React.useEffect(() => {
    if (editor && onEditorReady) {
      onEditorReady(editor);
    }
  }, [editor, onEditorReady]);

  return (
    <div
      className={`flex flex-col h-full overflow-hidden bg-transparent ${theme}`}
    >
      {/* 编辑器上下文 */}
      <EditorContext.Provider value={{ editor }}>
        {/* 使用独立出来的工具栏组件 - 仅在非移动端显示 */}
        {!isMobileWidth && (
          <Toolbar
            fontSize={fontSize}
            setFontSize={handleFontSizeChange}
            theme={theme}
          />
        )}

        {/* 编辑器内容区域 - 使用 flex-1 和 overflow-auto */}
        <div
          className="flex-1 overflow-auto relative flex justify-center"
          ref={editorRef}
        >
          <div
            className="h-full"
            style={{
              transform: `scale(${fontSize})`,
              transformOrigin: "center top",
              width: `${100 / fontSize}%`,
              maxWidth: `${(is2XL ? 1440 : 960) / fontSize}px`,
              margin: "0 auto",
            }}
          >
            {editor && <EditorContent editor={editor} className="h-full" />}
          </div>
        </div>

        {/* 快速操作栏 - 当有文本选中时显示 */}
        <QuickActionBar
          visible={textSelection.hasSelection}
          position={textSelection.position}
          selectedText={textSelection.selectedText}
          isVisible={textSelection.isVisible}
          onAddToChat={handleAddToChat}
          onQuickEdit={handleQuickEdit}
          refs={textSelection.refs}
          floatingStyles={textSelection.floatingStyles}
        />

        {/* 使用 EditorStatusBar 组件 */}
        <EditorFooterBar characterCount={characterCount} />
      </EditorContext.Provider>
    </div>
  );
};

export default Editor;
