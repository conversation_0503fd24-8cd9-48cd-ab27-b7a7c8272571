import { Editor } from "@tiptap/react";

/**
 * 缩进功能的快捷键
 */
export const indentShortcutKey = "Ctrl-]";

/**
 * 检查节点是否为顶级列表（不是其他列表的子列表）
 */
export function isTopLevelList(node: any, pos: number, doc: any): boolean {
  // 获取父节点
  const $pos = doc.resolve(pos);

  // 如果深度小于等于1，则是顶级节点
  if ($pos.depth <= 1) {
    return true;
  }

  // 获取父节点
  const parentNode = $pos.node($pos.depth - 1);

  // 如果父节点不是列表，则这是顶级列表
  return !(
    parentNode.type.name === "bulletList" ||
    parentNode.type.name === "orderedList"
  );
}

/**
 * 检查编辑器中是否有节点应用了缩进
 */
export function isIndentActive(editor: Editor | null): boolean {
  if (!editor) return false;

  // 检查文档中是否有段落或标题被缩进
  const doc = editor.state.doc;
  let hasIndentedNodes = false;

  doc.descendants((node, pos) => {
    // 检查段落、标题或顶级列表是否有缩进
    if (
      (node.type.name === "paragraph" ||
        node.type.name.startsWith("heading")) &&
      node.attrs.indent
    ) {
      hasIndentedNodes = true;
      return false; // 停止遍历
    }

    // 检查顶级列表是否有缩进
    if (
      (node.type.name === "bulletList" || node.type.name === "orderedList") &&
      isTopLevelList(node, pos, doc) &&
      node.attrs.indent
    ) {
      hasIndentedNodes = true;
      return false; // 停止遍历
    }

    return true;
  });

  return hasIndentedNodes;
}

/**
 * 切换段落首行缩进
 */
export function toggleIndent(
  editor: Editor | null,
  defaultIndent: string = "2em"
): boolean {
  if (!editor) return false;

  // 检查文档是否已经有缩进
  const isActive = isIndentActive(editor);

  // 创建一个事务
  const tr = editor.state.tr;
  let hasChanges = false;

  // 遍历文档中的所有节点
  editor.state.doc.descendants((node, pos) => {
    // 处理段落和标题
    if (
      node.type.name === "paragraph" ||
      node.type.name.startsWith("heading")
    ) {
      if (isActive) {
        // 如果文档已有缩进，则移除所有缩进
        if (node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, { ...node.attrs, indent: null });
          hasChanges = true;
        }
      } else {
        // 如果文档没有缩进，则添加缩进
        if (!node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            indent: defaultIndent,
          });
          hasChanges = true;
        }
      }
    }

    // 只处理顶级列表
    if (
      (node.type.name === "bulletList" || node.type.name === "orderedList") &&
      isTopLevelList(node, pos, editor.state.doc)
    ) {
      if (isActive) {
        // 如果文档已有缩进，则移除所有列表的缩进
        if (node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, { ...node.attrs, indent: null });
          hasChanges = true;
        }
      } else {
        // 如果文档没有缩进，则添加列表缩进
        if (!node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            indent: defaultIndent,
          });
          hasChanges = true;
        }
      }
    }

    return true;
  });

  // 如果有更改，应用事务
  if (hasChanges) {
    editor.view.dispatch(tr);
    return true;
  }

  return false;
}
