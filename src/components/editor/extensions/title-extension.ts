import { mergeAttributes, Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";

export interface TitleOptions {
  HTMLAttributes: Record<string, any>;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    title: {
      /**
       * 设置标题为 Title
       */
      setTitle: () => ReturnType;
      /**
       * 切换 Title
       */
      toggleTitle: () => ReturnType;
    };
  }
}

export const Title = Node.create<TitleOptions>({
  name: "title",

  priority: 1000,

  addOptions() {
    return {
      HTMLAttributes: {
        class: "title-heading",
      },
    };
  },

  group: "block",

  content: "inline*",

  defining: true,

  parseHTML() {
    return [{ tag: "h1.title-heading" }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "h1",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ];
  },

  addCommands() {
    return {
      setTitle:
        () =>
        ({ commands }) => {
          return commands.setNode(this.name);
        },
      toggleTitle:
        () =>
        ({ commands, chain }) => {
          // 如果切换为title，同时应用文字居中对齐
          if (!this.editor.isActive(this.name)) {
            return chain()
              .toggleNode(this.name, "paragraph")
              .setTextAlign("center")
              .run();
          }
          // 如果从title切换回普通段落，同时设置为左对齐
          return chain()
            .toggleNode(this.name, "paragraph")
            .setTextAlign("left")
            .run();
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Alt-0": () => this.editor.commands.toggleTitle(),
    };
  },
});

export default Title;
