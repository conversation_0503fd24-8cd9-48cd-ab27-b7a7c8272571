import { Extension } from "@tiptap/core";
import { Node as ProsemirrorNode } from "@tiptap/pm/model";
import {
  toggleIndent,
  indentShortcutKey,
} from "@/components/editor/utils/indent-utils";

export interface IndentOptions {
  types: string[];
  defaultIndent: string;
  indentClass: string;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    indent: {
      /**
       * 设置段落首行缩进
       */
      setIndent: (indent: string) => ReturnType;
      /**
       * 取消段落首行缩进
       */
      unsetIndent: () => ReturnType;
      /**
       * 切换段落首行缩进
       */
      toggleIndent: () => ReturnType;
    };
  }
}

/**
 * 检查节点是否为顶级列表（不是其他列表的子列表）
 */
function isTopLevelList(node: any, pos: number, doc: any): boolean {
  // 获取父节点
  const $pos = doc.resolve(pos);

  // 如果深度小于等于1，则是顶级节点
  if ($pos.depth <= 1) {
    return true;
  }

  // 获取父节点
  const parentNode = $pos.node($pos.depth - 1);

  // 如果父节点不是列表，则这是顶级列表
  return !(
    parentNode.type.name === "bulletList" ||
    parentNode.type.name === "orderedList"
  );
}

/**
 * 检查编辑器中是否有节点应用了缩进
 */
function isIndentActive(editor: any): boolean {
  if (!editor) return false;

  // 检查文档中是否有段落或标题被缩进
  const doc = editor.state.doc;
  let hasIndentedNodes = false;

  doc.descendants((node: any, pos: number) => {
    // 检查段落、标题或顶级列表是否有缩进
    if (
      (node.type.name === "paragraph" ||
        node.type.name.startsWith("heading")) &&
      node.attrs.indent
    ) {
      hasIndentedNodes = true;
      return false; // 停止遍历
    }

    // 检查顶级列表是否有缩进
    if (
      (node.type.name === "bulletList" || node.type.name === "orderedList") &&
      isTopLevelList(node, pos, doc) &&
      node.attrs.indent
    ) {
      hasIndentedNodes = true;
      return false; // 停止遍历
    }

    return true;
  });

  return hasIndentedNodes;
}

/**
 * 在快捷键处理中使用的切换缩进函数
 */
function handleToggleIndent(editor: any, defaultIndent: string): boolean {
  if (!editor) return false;

  // 检查文档是否已经有缩进
  const isActive = isIndentActive(editor);

  // 创建一个事务
  const tr = editor.state.tr;
  let hasChanges = false;

  // 遍历文档中的所有节点
  editor.state.doc.descendants((node: any, pos: number) => {
    // 处理段落和标题
    if (
      node.type.name === "paragraph" ||
      node.type.name.startsWith("heading")
    ) {
      if (isActive) {
        // 如果文档已有缩进，则移除所有缩进
        if (node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, { ...node.attrs, indent: null });
          hasChanges = true;
        }
      } else {
        // 如果文档没有缩进，则添加缩进
        if (!node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            indent: defaultIndent,
          });
          hasChanges = true;
        }
      }
    }

    // 只处理顶级列表
    if (
      (node.type.name === "bulletList" || node.type.name === "orderedList") &&
      isTopLevelList(node, pos, editor.state.doc)
    ) {
      if (isActive) {
        // 如果文档已有缩进，则移除所有列表的缩进
        if (node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, { ...node.attrs, indent: null });
          hasChanges = true;
        }
      } else {
        // 如果文档没有缩进，则添加列表缩进
        if (!node.attrs.indent) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            indent: defaultIndent,
          });
          hasChanges = true;
        }
      }
    }

    return true;
  });

  // 如果有更改，应用事务
  if (hasChanges) {
    editor.view.dispatch(tr);
    return true;
  }

  return false;
}

export const Indent = Extension.create<IndentOptions>({
  name: "indent",

  addOptions() {
    return {
      types: ["paragraph", "heading", "bulletList", "orderedList"],
      defaultIndent: "2em",
      indentClass: "indented",
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          indent: {
            default: null,
            parseHTML: (element) => {
              if (element.classList.contains(this.options.indentClass)) {
                return this.options.defaultIndent;
              }

              return null;
            },
            renderHTML: (attributes) => {
              if (!attributes.indent) {
                return {};
              }

              return {
                class: this.options.indentClass,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setIndent:
        (indent) =>
        ({ commands }) => {
          return this.options.types.every((type) =>
            commands.updateAttributes(type, { indent })
          );
        },
      unsetIndent:
        () =>
        ({ commands }) => {
          return this.options.types.every((type) =>
            commands.resetAttributes(type, "indent")
          );
        },
      toggleIndent:
        () =>
        ({ editor }) => {
          const { state } = editor;
          const { selection } = state;
          const { $from } = selection;

          if ($from.parent.type.name === "listItem") {
            let depth = $from.depth;
            let listNodeType = "";
            let listNodePos = -1;

            while (depth > 0) {
              const node = $from.node(depth);
              if (
                node.type.name === "bulletList" ||
                node.type.name === "orderedList"
              ) {
                listNodeType = node.type.name;
                listNodePos = depth;
                break;
              }
              depth -= 1;
            }

            if (listNodeType) {
              const listNode = $from.node(listNodePos);
              const listNodeIsIndented =
                listNode.attrs.indent === this.options.defaultIndent;

              if (listNodeIsIndented) {
                return editor.commands.updateAttributes(listNodeType, {
                  indent: null,
                });
              } else {
                return editor.commands.updateAttributes(listNodeType, {
                  indent: this.options.defaultIndent,
                });
              }
            }
          } else {
            const nodeType = $from.parent.type.name;

            const isIndented =
              $from.parent.attrs.indent === this.options.defaultIndent;

            if (isIndented) {
              return editor.commands.updateAttributes(nodeType, {
                indent: null,
              });
            } else {
              return editor.commands.updateAttributes(nodeType, {
                indent: this.options.defaultIndent,
              });
            }
          }

          return false;
        },
    };
  },

  // 添加键盘快捷键
  addKeyboardShortcuts() {
    // 将 Mod 前缀转换为 Ctrl 或 Cmd
    const shortcutKey = indentShortcutKey.replace("Ctrl", "Mod");

    return {
      [shortcutKey]: ({ editor }) => {
        // 使用共享的工具函数
        return toggleIndent(editor, this.options.defaultIndent);
      },
    };
  },
});

export default Indent;
