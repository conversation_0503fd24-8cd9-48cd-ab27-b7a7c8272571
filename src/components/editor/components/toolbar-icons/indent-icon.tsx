"use client";

import * as React from "react";
import clsx from "clsx";

export interface HeadingTitleIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

export const IndentIcon: React.FC<HeadingTitleIconProps> = ({
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      className={clsx("h-5 w-5", className)}
      {...props}
    >
      <g>
        <path
          d="M5,11L19,11Q19.0985,11,19.1951,11.019214999999999Q19.2917,11.03843,19.3827,11.076121Q19.4737,11.113812,19.5556,11.16853Q19.6375,11.223249,19.7071,11.292893Q19.776699999999998,11.362537,19.8315,11.44443Q19.886200000000002,11.526322,19.9239,11.617317Q19.9616,11.708311,19.980800000000002,11.80491Q20,11.9015086,20,12Q20,12.0984914,19.980800000000002,12.19509Q19.9616,12.291689,19.9239,12.382683Q19.886200000000002,12.473678,19.8315,12.55557Q19.776699999999998,12.637463,19.7071,12.707107Q19.6375,12.776751,19.5556,12.83147Q19.4737,12.886188,19.3827,12.923879Q19.2917,12.96157,19.1951,12.980785000000001Q19.0985,13,19,13L5,13Q4.9015086,13,4.80491,12.980785000000001Q4.708311,12.96157,4.617317,12.923879Q4.526322,12.886188,4.44443,12.83147Q4.362537,12.776751,4.292893,12.707107Q4.223249,12.637463,4.16853,12.55557Q4.113812,12.473678,4.076121,12.382683Q4.03843,12.291689,4.019215,12.19509Q4,12.0984914,4,12Q4,11.9015086,4.019215,11.80491Q4.03843,11.708311,4.076121,11.617317Q4.113812,11.526322,4.16853,11.44443Q4.223249,11.362537,4.292893,11.292893Q4.362537,11.223249,4.44443,11.16853Q4.526322,11.113812,4.617317,11.076121Q4.708311,11.03843,4.80491,11.019214999999999Q4.9015086,11,5,11Z"
          fill="currentColor"
        />
      </g>
      <g>
        <path
          d="M5,16.5L19,16.5Q19.0985,16.5,19.1951,16.519215Q19.2917,16.538429999999998,19.3827,16.576121Q19.4737,16.613812,19.5556,16.66853Q19.6375,16.723249,19.7071,16.792893Q19.776699999999998,16.862537,19.8315,16.94443Q19.886200000000002,17.026322,19.9239,17.117317Q19.9616,17.208311,19.980800000000002,17.30491Q20,17.4015086,20,17.5Q20,17.5984914,19.980800000000002,17.69509Q19.9616,17.791689,19.9239,17.882683Q19.886200000000002,17.973678,19.8315,18.05557Q19.776699999999998,18.137463,19.7071,18.207107Q19.6375,18.276751,19.5556,18.33147Q19.4737,18.386188,19.3827,18.423879Q19.2917,18.461570000000002,19.1951,18.480785Q19.0985,18.5,19,18.5L5,18.5Q4.9015086,18.5,4.80491,18.480785Q4.708311,18.461570000000002,4.617317,18.423879Q4.526322,18.386188,4.44443,18.33147Q4.362537,18.276751,4.292893,18.207107Q4.223249,18.137463,4.16853,18.05557Q4.113812,17.973678,4.076121,17.882683Q4.03843,17.791689,4.019215,17.69509Q4,17.5984914,4,17.5Q4,17.4015086,4.019215,17.30491Q4.03843,17.208311,4.076121,17.117317Q4.113812,17.026322,4.16853,16.94443Q4.223249,16.862537,4.292893,16.792893Q4.362537,16.723249,4.44443,16.66853Q4.526322,16.613812,4.617317,16.576121Q4.708311,16.538429999999998,4.80491,16.519215Q4.9015086,16.5,5,16.5Z"
         fill="currentColor"
        />
      </g>
      <g>
        <path
          d="M10.5,5.5L19,5.5Q19.098489999999998,5.5,19.19509,5.519215Q19.29169,5.53843,19.38268,5.576121Q19.47368,5.613812,19.55557,5.66853Q19.63746,5.723249,19.70711,5.792893Q19.77675,5.862537,19.83147,5.94443Q19.88619,6.026322,19.92388,6.117317Q19.961570000000002,6.208311,19.98078,6.30491Q20,6.4015086,20,6.5Q20,6.5984914,19.98078,6.69509Q19.961570000000002,6.791689,19.92388,6.882683Q19.88619,6.973678,19.83147,7.05557Q19.77675,7.137463,19.70711,7.207107Q19.63746,7.276751,19.55557,7.33147Q19.47368,7.386188,19.38268,7.423879Q19.29169,7.46157,19.19509,7.480785Q19.098489999999998,7.5,19,7.5L10.5,7.5Q10.4015086,7.5,10.30491,7.480785Q10.208311,7.46157,10.117317,7.423879Q10.026322,7.386188,9.94443,7.33147Q9.862537,7.276751,9.792893,7.207107Q9.723249,7.137463,9.66853,7.05557Q9.613812,6.973678,9.576121,6.882683Q9.53843,6.791689,9.519214999999999,6.69509Q9.5,6.5984914,9.5,6.5Q9.5,6.4015086,9.519214999999999,6.30491Q9.53843,6.208311,9.576121,6.117317Q9.613812,6.026322,9.66853,5.94443Q9.723249,5.862537,9.792893,5.792893Q9.862537,5.723249,9.94443,5.66853Q10.026322,5.613812,10.117317,5.576121Q10.208311,5.53843,10.30491,5.519215Q10.4015086,5.5,10.5,5.5Z"
         fill="currentColor"
        />
      </g>
    </svg>
  );
};

export default IndentIcon;
