.tiptap-button {
  /************************************************** 
      Default button background color 
  **************************************************/

  /* Light mode */
  --tt-button-default-bg-color: var(--tt-gray-light-a-100);
  --tt-button-hover-bg-color: var(--tt-gray-light-200);
  --tt-button-active-bg-color: var(--tt-gray-light-a-200);
  --tt-button-active-bg-color-emphasized: var(
    --tt-brand-color-100
  ); //more important active state
  --tt-button-active-bg-color-subdued: var(
    --tt-gray-light-a-200
  ); //less important active state
  --tt-button-active-hover-bg-color: var(--tt-gray-light-300);
  --tt-button-active-hover-bg-color-emphasized: var(
    --tt-brand-color-200
  ); //more important active state hover
  --tt-button-active-hover-bg-color-subdued: var(
    --tt-gray-light-a-300
  ); //less important active state hover
  --tt-button-disabled-bg-color: var(--tt-gray-light-a-50);

  /* Dark mode */
  .dark & {
    --tt-button-default-bg-color: var(--tt-gray-dark-a-100);
    --tt-button-hover-bg-color: var(--tt-gray-dark-200);
    --tt-button-active-bg-color: var(--tt-gray-dark-a-200);
    --tt-button-active-bg-color-emphasized: var(
      --tt-brand-color-900
    ); //more important active state
    --tt-button-active-bg-color-subdued: var(
      --tt-gray-dark-a-200
    ); //less important active state
    --tt-button-active-hover-bg-color: var(--tt-gray-dark-300);
    --tt-button-active-hover-bg-color-emphasized: var(
      --tt-brand-color-800
    ); //more important active state hover
    --tt-button-active-hover-bg-color-subdued: var(
      --tt-gray-dark-a-300
    ); //less important active state hover
    --tt-button-disabled-bg-color: var(--tt-gray-dark-a-50);
  }

  /************************************************** 
      Default button text color 
  **************************************************/

  /* Light mode */
  --tt-button-default-text-color: var(--tt-gray-light-a-600);
  --tt-button-hover-text-color: var(--tt-gray-light-a-900);
  --tt-button-active-text-color: var(--tt-gray-light-a-900);
  --tt-button-active-text-color-emphasized: var(--tt-gray-light-a-900);
  --tt-button-active-text-color-subdued: var(--tt-gray-light-a-900);
  --tt-button-disabled-text-color: var(--tt-gray-light-a-400);

  /* Dark mode */
  .dark & {
    --tt-button-default-text-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-dark-a-300);
  }

  /************************************************** 
      Default button icon color 
  **************************************************/

  /* Light mode */
  --tt-button-default-icon-color: var(--tt-gray-light-a-600);
  --tt-button-hover-icon-color: var(--tt-gray-light-a-900);
  --tt-button-active-icon-color: var(--tt-brand-color-500);
  --tt-button-active-icon-color-emphasized: var(--tt-brand-color-600);
  --tt-button-active-icon-color-subdued: var(--tt-gray-light-a-900);
  --tt-button-disabled-icon-color: var(--tt-gray-light-a-400);

  /* Dark mode */
  .dark & {
    --tt-button-default-icon-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-icon-color: var(--tt-gray-dark-a-900);
    --tt-button-active-icon-color: var(--tt-brand-color-400);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-400);
    --tt-button-active-icon-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-icon-color: var(--tt-gray-dark-a-400);
  }

  /************************************************** 
      Default button subicon color 
  **************************************************/

  /* Light mode */
  --tt-button-default-icon-sub-color: var(--tt-gray-light-a-400);
  --tt-button-hover-icon-sub-color: var(--tt-gray-light-a-500);
  --tt-button-active-icon-sub-color: var(--tt-gray-light-a-400);
  --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-light-a-500);
  --tt-button-active-icon-sub-color-subdued: var(--tt-gray-light-a-400);
  --tt-button-disabled-icon-sub-color: var(--tt-gray-light-a-100);

  /* Dark mode */
  .dark & {
    --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-dark-a-300);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-dark-a-100);
  }

  /************************************************** 
      Default button dropdown / arrows color 
  **************************************************/

  /* Light mode */
  --tt-button-default-dropdown-arrows-color: var(--tt-gray-light-a-600);
  --tt-button-hover-dropdown-arrows-color: var(--tt-gray-light-a-700);
  --tt-button-active-dropdown-arrows-color: var(--tt-gray-light-a-600);
  --tt-button-active-dropdown-arrows-color-emphasized: var(
    --tt-gray-light-a-700
  );
  --tt-button-active-dropdown-arrows-color-subdued: var(--tt-gray-light-a-600);
  --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-light-a-400);

  /* Dark mode */
  .dark & {
    --tt-button-default-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-dropdown-arrows-color: var(--tt-gray-dark-a-700);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var(
      --tt-gray-dark-a-700
    );
    --tt-button-active-dropdown-arrows-color-subdued: var(--tt-gray-dark-a-600);
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-dark-a-400);
  }

  /* ----------------------------------------------------------------
      --------------------------- GHOST BUTTON --------------------------
      ---------------------------------------------------------------- */

  &[data-style="ghost"] {
    /************************************************** 
        Ghost button background color 
    **************************************************/

    /* Light mode */
    --tt-button-default-bg-color: var(--transparent);
    --tt-button-hover-bg-color: var(--tt-gray-light-200);
    --tt-button-active-bg-color: var(--tt-gray-light-a-100);
    --tt-button-active-bg-color-emphasized: var(
      --tt-brand-color-100
    ); //more important active state
    --tt-button-active-bg-color-subdued: var(
      --tt-gray-light-a-100
    ); //less important active state
    --tt-button-active-hover-bg-color: var(--tt-gray-light-200);
    --tt-button-active-hover-bg-color-emphasized: var(
      --tt-brand-color-200
    ); //more important active state hover
    --tt-button-active-hover-bg-color-subdued: var(
      --tt-gray-light-a-200
    ); //less important active state hover
    --tt-button-disabled-bg-color: var(--transparent);

    /* Dark mode */
    .dark & {
      --tt-button-default-bg-color: var(--transparent);
      --tt-button-hover-bg-color: var(--tt-gray-dark-200);
      --tt-button-active-bg-color: var(--tt-gray-dark-a-100);
      --tt-button-active-bg-color-emphasized: var(
        --tt-brand-color-900
      ); //more important active state
      --tt-button-active-bg-color-subdued: var(
        --tt-gray-dark-a-100
      ); //less important active state
      --tt-button-active-hover-bg-color: var(--tt-gray-dark-200);
      --tt-button-active-hover-bg-color-emphasized: var(
        --tt-brand-color-800
      ); //more important active state hover
      --tt-button-active-hover-bg-color-subdued: var(
        --tt-gray-dark-a-200
      ); //less important active state hover
      --tt-button-disabled-bg-color: var(--transparent);
    }

    /************************************************** 
        Ghost button text color 
    **************************************************/

    /* Light mode */
    --tt-button-default-text-color: var(--tt-gray-light-a-600);
    --tt-button-hover-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-light-a-400);

    /* Dark mode */
    .dark & {
      --tt-button-default-text-color: var(--tt-gray-dark-a-600);
      --tt-button-hover-text-color: var(--tt-gray-dark-a-900);
      --tt-button-active-text-color: var(--tt-gray-dark-a-900);
      --tt-button-active-text-color-emphasized: var(--tt-gray-dark-a-900);
      --tt-button-active-text-color-subdued: var(--tt-gray-dark-a-900);
      --tt-button-disabled-text-color: var(--tt-gray-dark-a-300);
    }

    /************************************************** 
        Ghost button icon color 
    **************************************************/

    /* Light mode */
    --tt-button-default-icon-color: var(--tt-gray-light-a-600);
    --tt-button-hover-icon-color: var(--tt-gray-light-a-900);
    --tt-button-active-icon-color: var(--tt-brand-color-500);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-600);
    --tt-button-active-icon-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-icon-color: var(--tt-gray-light-a-400);

    /* Dark mode */
    .dark & {
      --tt-button-default-icon-color: var(--tt-gray-dark-a-600);
      --tt-button-hover-icon-color: var(--tt-gray-dark-a-900);
      --tt-button-active-icon-color: var(--tt-brand-color-400);
      --tt-button-active-icon-color-emphasized: var(--tt-brand-color-300);
      --tt-button-active-icon-color-subdued: var(--tt-gray-dark-a-900);
      --tt-button-disabled-icon-color: var(--tt-gray-dark-a-400);
    }

    /************************************************** 
        Ghost button subicon color 
    **************************************************/

    /* Light mode */
    --tt-button-default-icon-sub-color: var(--tt-gray-light-a-400);
    --tt-button-hover-icon-sub-color: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color: var(--tt-gray-light-a-400);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-light-a-400);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-light-a-100);

    /* Dark mode */
    .dark & {
      --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-300);
      --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-400);
      --tt-button-active-icon-sub-color: var(--tt-gray-dark-a-300);
      --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-dark-a-400);
      --tt-button-active-icon-sub-color-subdued: var(--tt-gray-dark-a-300);
      --tt-button-disabled-icon-sub-color: var(--tt-gray-dark-a-100);
    }

    /************************************************** 
        Ghost button dropdown / arrows color 
    **************************************************/

    /* Light mode */
    --tt-button-default-dropdown-arrows-color: var(--tt-gray-light-a-600);
    --tt-button-hover-dropdown-arrows-color: var(--tt-gray-light-a-700);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-light-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var(
      --tt-gray-light-a-700
    );
    --tt-button-active-dropdown-arrows-color-subdued: var(
      --tt-gray-light-a-600
    );
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-light-a-400);

    /* Dark mode */
    .dark & {
      --tt-button-default-dropdown-arrows-color: var(--tt-gray-dark-a-600);
      --tt-button-hover-dropdown-arrows-color: var(--tt-gray-dark-a-700);
      --tt-button-active-dropdown-arrows-color: var(--tt-gray-dark-a-600);
      --tt-button-active-dropdown-arrows-color-emphasized: var(
        --tt-gray-dark-a-700
      );
      --tt-button-active-dropdown-arrows-color-subdued: var(
        --tt-gray-dark-a-600
      );
      --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-dark-a-400);
    }
  }

  /* ----------------------------------------------------------------
      -------------------------- PRIMARY BUTTON -------------------------
      ---------------------------------------------------------------- */

  &[data-style="primary"] {
    /************************************************** 
        Primary button background color 
    **************************************************/

    /* Light mode */
    --tt-button-default-bg-color: var(--tt-brand-color-500);
    --tt-button-hover-bg-color: var(--tt-brand-color-600);
    --tt-button-active-bg-color: var(--tt-brand-color-100);
    --tt-button-active-bg-color-emphasized: var(
      --tt-brand-color-100
    ); //more important active state
    --tt-button-active-bg-color-subdued: var(
      --tt-brand-color-100
    ); //less important active state
    --tt-button-active-hover-bg-color: var(--tt-brand-color-200);
    --tt-button-active-hover-bg-color-emphasized: var(
      --tt-brand-color-200
    ); //more important active state hover
    --tt-button-active-hover-bg-color-subdued: var(
      --tt-brand-color-200
    ); //less important active state hover
    --tt-button-disabled-bg-color: var(--tt-gray-light-a-100);

    /* Dark mode */
    .dark & {
      --tt-button-default-bg-color: var(--tt-brand-color-500);
      --tt-button-hover-bg-color: var(--tt-brand-color-600);
      --tt-button-active-bg-color: var(--tt-brand-color-900);
      --tt-button-active-bg-color-emphasized: var(
        --tt-brand-color-900
      ); //more important active state
      --tt-button-active-bg-color-subdued: var(
        --tt-brand-color-900
      ); //less important active state
      --tt-button-active-hover-bg-color: var(--tt-brand-color-800);
      --tt-button-active-hover-bg-color-emphasized: var(
        --tt-brand-color-800
      ); //more important active state hover
      --tt-button-active-hover-bg-color-subdued: var(
        --tt-brand-color-800
      ); //less important active state hover
      --tt-button-disabled-bg-color: var(--tt-gray-dark-a-100);
    }

    /************************************************** 
        Primary button text color 
    **************************************************/

    /* Light mode */
    --tt-button-default-text-color: var(--white);
    --tt-button-hover-text-color: var(--white);
    --tt-button-active-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-light-a-400);

    /* Dark mode */
    .dark & {
      --tt-button-default-text-color: var(--white);
      --tt-button-hover-text-color: var(--white);
      --tt-button-active-text-color: var(--tt-gray-dark-a-900);
      --tt-button-active-text-color-emphasized: var(--tt-gray-dark-a-900);
      --tt-button-active-text-color-subdued: var(--tt-gray-dark-a-900);
      --tt-button-disabled-text-color: var(--tt-gray-dark-a-300);
    }

    /************************************************** 
        Primary button icon color 
    **************************************************/

    /* Light mode */
    --tt-button-default-icon-color: var(--white);
    --tt-button-hover-icon-color: var(--white);
    --tt-button-active-icon-color: var(--tt-brand-color-600);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-600);
    --tt-button-active-icon-color-subdued: var(--tt-brand-color-600);
    --tt-button-disabled-icon-color: var(--tt-gray-light-a-400);

    /* Dark mode */
    .dark & {
      --tt-button-default-icon-color: var(--white);
      --tt-button-hover-icon-color: var(--white);
      --tt-button-active-icon-color: var(--tt-brand-color-400);
      --tt-button-active-icon-color-emphasized: var(--tt-brand-color-400);
      --tt-button-active-icon-color-subdued: var(--tt-brand-color-400);
      --tt-button-disabled-icon-color: var(--tt-gray-dark-a-300);
    }

    /************************************************** 
        Primary button subicon color 
    **************************************************/

    /* Light mode */
    --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-500);
    --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-500);
    --tt-button-active-icon-sub-color: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-light-a-500);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-light-a-100);

    /* Dark mode */
    .dark & {
      --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-400);
      --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-500);
      --tt-button-active-icon-sub-color: var(--tt-gray-dark-a-300);
      --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-dark-a-400);
      --tt-button-active-icon-sub-color-subdued: var(--tt-gray-dark-a-300);
      --tt-button-disabled-icon-sub-color: var(--tt-gray-dark-a-100);
    }

    /************************************************** 
        Primary button dropdown / arrows color 
    **************************************************/

    /* Light mode */
    --tt-button-default-dropdown-arrows-color: var(--white);
    --tt-button-hover-dropdown-arrows-color: var(--white);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-light-a-700);
    --tt-button-active-dropdown-arrows-color-emphasized: var(
      --tt-gray-light-a-700
    );
    --tt-button-active-dropdown-arrows-color-subdued: var(
      --tt-gray-light-a-700
    );
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-light-a-400);

    /* Dark mode */
    .dark & {
      --tt-button-default-dropdown-arrows-color: var(--white);
      --tt-button-hover-dropdown-arrows-color: var(--white);
      --tt-button-active-dropdown-arrows-color: var(--tt-gray-dark-a-600);
      --tt-button-active-dropdown-arrows-color-emphasized: var(
        --tt-gray-dark-a-600
      );
      --tt-button-active-dropdown-arrows-color-subdued: var(
        --tt-gray-dark-a-600
      );
      --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-dark-a-400);
    }
  }
}
