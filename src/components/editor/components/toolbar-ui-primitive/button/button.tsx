"use client";

import * as React from "react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import "@/components/editor/components/toolbar-ui-primitive/button/button-colors.scss";
import "@/components/editor/components/toolbar-ui-primitive/button/button-group.scss";
import "@/components/editor/components/toolbar-ui-primitive/button/button.scss";

type PlatformShortcuts = Record<string, string>;

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string;
  showTooltip?: boolean;
  tooltip?: React.ReactNode;
  shortcutKeys?: string;
  preserveHoverOnClick?: boolean;
}

export const MAC_SYMBOLS: PlatformShortcuts = {
  ctrl: "⌘",
  alt: "⌥",
  shift: "⇧",
} as const;

export const formatShortcutKey = (key: string, isMac: boolean) => {
  if (isMac) {
    const lowerKey = key.toLowerCase();
    return MAC_SYMBOLS[lowerKey] || key.toUpperCase();
  }
  return key.charAt(0).toUpperCase() + key.slice(1);
};

export const parseShortcutKeys = (
  shortcutKeys: string | undefined,
  isMac: boolean
) => {
  if (!shortcutKeys) return [];

  return shortcutKeys
    .split("-")
    .map((key) => key.trim())
    .map((key) => formatShortcutKey(key, isMac));
};

export const ShortcutDisplay: React.FC<{ shortcuts: string[] }> = ({
  shortcuts,
}) => {
  if (shortcuts.length === 0) return null;

  return (
    <div>
      {shortcuts.map((key, index) => (
        <React.Fragment key={index}>
          {index > 0 && <kbd>+</kbd>}
          <kbd>{key}</kbd>
        </React.Fragment>
      ))}
    </div>
  );
};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className = "",
      children,
      tooltip,
      showTooltip = true,
      shortcutKeys,
      preserveHoverOnClick = false,
      "aria-label": ariaLabel,
      onClick,
      ...props
    },
    ref
  ) => {
    const isMac = React.useMemo(
      () =>
        typeof navigator !== "undefined" &&
        navigator.platform.toLowerCase().includes("mac"),
      []
    );

    const shortcuts = React.useMemo(
      () => parseShortcutKeys(shortcutKeys, isMac),
      [shortcutKeys, isMac]
    );

    const [isHovered, setIsHovered] = React.useState(false);

    // 为保持hover状态创建处理函数
    const handleMouseEnter = React.useCallback(() => {
      setIsHovered(true);
    }, []);

    const handleMouseLeave = React.useCallback(() => {
      setIsHovered(false);
    }, []);

    // 处理点击事件，保持hover样式
    const handleClick = React.useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        if (onClick) {
          onClick(e);
        }
      },
      [onClick]
    );

    if (!tooltip || !showTooltip) {
      return (
        <button
          className={`tiptap-button ${className}`.trim()}
          ref={ref}
          aria-label={ariaLabel}
          onClick={handleClick}
          onMouseEnter={preserveHoverOnClick ? handleMouseEnter : undefined}
          onMouseLeave={preserveHoverOnClick ? handleMouseLeave : undefined}
          data-hovered={preserveHoverOnClick && isHovered ? true : undefined}
          {...props}
        >
          {children}
        </button>
      );
    }

    return (
      <Tooltip delay={600}>
        <TooltipTrigger
          className={`tiptap-button ${className}`.trim()}
          ref={ref}
          aria-label={ariaLabel}
          onClick={handleClick}
          onMouseEnter={preserveHoverOnClick ? handleMouseEnter : undefined}
          onMouseLeave={preserveHoverOnClick ? handleMouseLeave : undefined}
          data-hovered={preserveHoverOnClick && isHovered ? true : undefined}
          {...props}
        >
          {children}
        </TooltipTrigger>
        <TooltipContent>
          {tooltip}
          <ShortcutDisplay shortcuts={shortcuts} />
        </TooltipContent>
      </Tooltip>
    );
  }
);

Button.displayName = "Button";

export default Button;
