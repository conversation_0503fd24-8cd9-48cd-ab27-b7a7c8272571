"use client";

import * as React from "react";
import { Button } from "@heroui/react";

interface QuickActionBarProps {
  /**
   * 是否显示快速操作栏
   */
  visible: boolean;
  /**
   * 操作栏的位置坐标
   */
  position: {
    x: number;
    y: number;
  };
  /**
   * 选中的文本内容
   */
  selectedText: string;
  /**
   * 是否在可见区域内（用于边界剪裁）
   */
  isVisible: boolean;
  /**
   * Add to Chat 按钮点击事件
   */
  onAddToChat?: (text: string) => void;
  /**
   * Quick Edit 按钮点击事件
   */
  onQuickEdit?: (text: string) => void;
  /**
   * Floating UI 提供的引用对象
   */
  refs?: {
    setReference?: (node: Element | null) => void;
    setFloating?: (node: HTMLElement | null) => void;
  };
  /**
   * Floating UI 提供的样式
   */
  floatingStyles?: React.CSSProperties;
}

/**
 * 快速操作栏组件
 * 当用户选中文本时，在选中文本上方显示操作按钮
 * 使用 Floating UI 实现精确定位和跟随
 */
export const QuickActionBar: React.FC<QuickActionBarProps> = ({
  visible,
  position,
  selectedText,
  isVisible,
  onAddToChat,
  onQuickEdit,
  refs,
  floatingStyles,
}) => {
  // 如果不可见或没有选中文本，不渲染组件
  if (!visible || !selectedText.trim() || !isVisible) {
    return null;
  }

  // 合并样式，确保 z-index 正确
  const finalStyles = {
    ...floatingStyles,
    // 确保 z-index 低于工具栏和底部栏，但高于编辑器内容
    zIndex: 9,
  };

  // 设置浮动元素引用的回调函数
  const setFloatingRef = React.useCallback(
    (node: HTMLDivElement | null) => {
      if (refs?.setFloating) {
        refs.setFloating(node);
      }
    },
    [refs]
  );

  return (
    <div
      ref={setFloatingRef}
      className="flex items-center gap-1.5 px-1.5 py-1.5 bg-floatingBar border border-floatingBar-border rounded-xl shadow-lg"
      style={finalStyles}
    >
      <Button
        variant="light"
        size="sm"
        onClick={() => onAddToChat?.(selectedText)}
        className="text-secondaryBtn-text bg-secondaryBtn data-[hover=true]:bg-secondaryBtn/85 text-xs px-2 py-1 h-7 min-w-0 rounded-md"
      >
        Add to Chat
      </Button>

      <Button
        variant="light"
        size="sm"
        onClick={() => onQuickEdit?.(selectedText)}
        className="text-secondaryBtn bg-transparent data-[hover=true]:bg-secondaryBtn/10 text-xs px-2 py-1 h-7 min-w-0 rounded-md"
      >
        Quick Edit
      </Button>
    </div>
  );
};

export default QuickActionBar;
