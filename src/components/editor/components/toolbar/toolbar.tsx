import * as React from "react";
import { useContext, useEffect, useRef, useState } from "react";
import { EditorContext } from "@tiptap/react";

// Tiptap UI 组件
import { MarkButton } from "@/components/editor/components/toolbar-ui/mark-button";
import { HeadingDropdownMenu } from "@/components/editor/components/toolbar-ui/heading-dropdown-menu";
import { UndoRedoButton } from "@/components/editor/components/toolbar-ui/undo-redo-button";
import { TextAlignButton } from "@/components/editor/components/toolbar-ui/text-align-button";
import { HighlightPopover } from "@/components/editor/components/toolbar-ui/highlight-popover";
import { ListButton } from "@/components/editor/components/toolbar-ui/list-button";
import { SettingsButton } from "@/components/editor/components/toolbar-ui/setting";
import { LineIndentButton } from "@/components/editor/components/toolbar-ui/line-indent-button";

interface ToolbarProps {
  fontSize: number;
  setFontSize: (fontSize: number) => void;
  theme: "light" | "dark";
}

/**
 * 编辑器工具栏组件
 * 包含所有文本格式化工具按钮
 */
export const Toolbar: React.FC<ToolbarProps> = ({
  fontSize,
  setFontSize,
  theme,
}) => {
  // 获取编辑器上下文
  const { editor } = useContext(EditorContext);

  // 用于检测是否需要显示右侧滚动指示器
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  // 用于检测是否有足够空间显示所有工具按钮
  const [hasEnoughSpace, setHasEnoughSpace] = useState(true);

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const toolbarButtonsRef = useRef<HTMLDivElement>(null);
  const toolbarContainerRef = useRef<HTMLDivElement>(null);

  // 检测容器是否有足够空间显示所有按钮
  useEffect(() => {
    const checkForSpace = () => {
      if (
        scrollContainerRef.current &&
        toolbarButtonsRef.current &&
        toolbarContainerRef.current
      ) {
        // 获取按钮组的实际宽度
        const buttonsWidth = toolbarButtonsRef.current.scrollWidth;
        // 获取可用容器宽度（需要减去右侧按钮和左侧间距的宽度）
        // 假设右侧设置按钮和滚动指示器总宽度大约为60px
        const rightSectionWidth = 60;
        const availableWidth =
          toolbarContainerRef.current.clientWidth - rightSectionWidth;

        // 更新状态
        const hasSpace = buttonsWidth <= availableWidth;
        setHasEnoughSpace(hasSpace);
        setShowScrollIndicator(!hasSpace);

        // 如果有空间且已经滚动，重置滚动位置
        if (hasSpace && scrollContainerRef.current.scrollLeft > 0) {
          scrollContainerRef.current.scrollLeft = 0;
        }
      }
    };

    // 初始检查
    checkForSpace();

    // 监听窗口大小变化
    window.addEventListener("resize", checkForSpace);

    return () => {
      window.removeEventListener("resize", checkForSpace);
    };
  }, []);

  // 监听滚动事件，检查是否已滚动到最右侧
  const handleScroll = () => {
    if (scrollContainerRef.current && !hasEnoughSpace) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      const isAtEnd = Math.ceil(scrollLeft + clientWidth) >= scrollWidth;
      setShowScrollIndicator(!isAtEnd);
    }
  };

  // 如果编辑器未初始化，不渲染工具栏
  if (!editor) {
    return null;
  }

  return (
    <div
      className={`sticky top-0 z-20 flex items-center m-0 p-1.5 bg-background ${theme}`}
      ref={toolbarContainerRef}
    >
      {/* 中间按钮组 - 可滚动区域 */}
      <div
        className={`flex-1 overflow-x-auto scrollbar-hide ${
          hasEnoughSpace ? "flex justify-center" : ""
        }`}
        ref={scrollContainerRef}
        onScroll={handleScroll}
      >
        <div
          ref={toolbarButtonsRef}
          className={`flex items-center ${
            hasEnoughSpace ? "justify-center" : "justify-start min-w-max"
          } px-2`}
        >
          <div className="flex items-center space-x-1">
            <UndoRedoButton action="undo" />
            <UndoRedoButton action="redo" />
          </div>

          <div className="w-px h-6 mx-2 bg-divider" />

          <div className="flex items-center space-x-1">
            {/* 标题层级下拉菜单 */}
            <HeadingDropdownMenu levels={["title", 1, 2, 3, 4]} />
          </div>

          <div className="w-px h-6 mx-2 bg-divider" />

          <div className="flex items-center space-x-1">
            {/* 文本格式化按钮 */}
            <MarkButton type="bold" />
            <MarkButton type="italic" />
            <MarkButton type="underline" />
            <MarkButton type="strike" />
            <HighlightPopover />
          </div>

          <div className="w-px h-6 mx-2 bg-divider" />

          {/* 文本对齐按钮组 */}
          <div className="flex items-center space-x-1">
            <TextAlignButton align="left" />
            <TextAlignButton align="center" />
            <TextAlignButton align="right" />
            <TextAlignButton align="justify" />
          </div>

          <div className="w-px h-6 mx-2 bg-divider" />

          {/* 列表按钮组 */}
          <div className="flex items-center space-x-1">
            <ListButton type="bulletList" />
            <ListButton type="orderedList" />
          </div>

          <div className="w-px h-6 mx-2 bg-divider" />

          {/* 首行缩进按钮 */}
          <div className="flex items-center space-x-1">
            <LineIndentButton />
          </div>
        </div>
      </div>

      {/* 右侧滚动指示器 */}
      {showScrollIndicator && (
        <div className="flex-none flex items-center justify-center mr-0">
          <div className="flex items-center justify-center w-5 h-5">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-foreground opacity-20"
            >
              <path d="m9 18 6-6-6-6" />
            </svg>
          </div>
        </div>
      )}

      {/* 右侧设置按钮 - 固定位置 */}
      <div className="ml-2 flex-none">
        <div className="flex items-center space-x-1">
          <SettingsButton fontSize={fontSize} setFontSize={setFontSize} />
        </div>
      </div>
    </div>
  );
};
