"use client";

import * as React from "react";
import { isNodeSelection, type Editor } from "@tiptap/react";

// --- Hooks ---
import { useTiptapEditor } from "@/hooks/editor/use-tiptap-editor";

// --- Icons ---
import { ChevronDownIcon } from "@/components/editor/components/toolbar-icons/chevron-down-icon";
import { HeadingIcon } from "@/components/editor/components/toolbar-icons/heading-icon";

// --- Lib ---
import { isNodeInSchema } from "@/lib/tiptap-utils";

// --- Tiptap UI ---
import {
  HeadingButton,
  headingIcons,
  type Level,
  getFormattedHeadingName,
  headingShortcutKeys,
} from "@/components/editor/components/toolbar-ui/heading-button/heading-button";

// --- UI Primitives ---
import type { ButtonProps } from "@/components/editor/components/toolbar-ui-primitive/button";
import {
  Button,
  parseShortcutKeys,
} from "@/components/editor/components/toolbar-ui-primitive/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuGroup,
} from "@/components/editor/components/toolbar-ui-primitive/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export interface HeadingDropdownMenuProps extends Omit<ButtonProps, "type"> {
  editor?: Editor | null;
  levels?: Level[];
  hideWhenUnavailable?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
}

export function HeadingDropdownMenu({
  editor: providedEditor,
  levels = ["title", 1, 2, 3, 4, 5, 6],
  hideWhenUnavailable = false,
  onOpenChange,
  ...props
}: HeadingDropdownMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const editor = useTiptapEditor(providedEditor);

  const headingInSchema = isNodeInSchema("heading", editor);

  const handleOnOpenChange = React.useCallback(
    (open: boolean) => {
      setIsOpen(open);
      onOpenChange?.(open);
    },
    [onOpenChange]
  );

  const getActiveIcon = React.useCallback(() => {
    if (!editor) return <HeadingIcon className="tiptap-button-icon" />;

    const activeLevel = levels.find((level) =>
      level === "title"
        ? editor.isActive("title")
        : editor.isActive("heading", { level })
    ) as Level | undefined;

    if (!activeLevel) return <HeadingIcon className="tiptap-button-icon" />;

    const ActiveIcon = headingIcons[activeLevel];
    return <ActiveIcon className="tiptap-button-icon" />;
  }, [editor, levels]);

  const canToggleAnyHeading = React.useCallback((): boolean => {
    if (!editor) return false;
    return levels.some((level) => {
      if (level === "title") {
        return editor.can().toggleTitle();
      }
      return editor.can().toggleNode("heading", "paragraph", { level });
    });
  }, [editor, levels]);

  const isDisabled = !canToggleAnyHeading();
  const isAnyHeadingActive = editor
    ? editor.isActive("heading") || editor.isActive("title")
    : false;

  const show = React.useMemo(() => {
    // 检查 heading 和 title 节点是否在 schema 中
    const titleInSchema = editor ? isNodeInSchema("title", editor) : false;

    if ((!headingInSchema && !titleInSchema) || !editor) {
      return false;
    }

    if (hideWhenUnavailable) {
      if (isNodeSelection(editor.state.selection) || !canToggleAnyHeading()) {
        return false;
      }
    }

    return true;
  }, [headingInSchema, editor, hideWhenUnavailable, canToggleAnyHeading]);

  if (!show || !editor || !editor.isEditable) {
    return null;
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={handleOnOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button
          type="button"
          disabled={isDisabled}
          data-style="ghost"
          data-active-state={isAnyHeadingActive ? "on" : "off"}
          data-disabled={isDisabled}
          role="button"
          tabIndex={-1}
          aria-label="Format text as heading"
          aria-pressed={isAnyHeadingActive}
          tooltip="Heading"
          {...props}
        >
          {getActiveIcon()}
          <ChevronDownIcon className="tiptap-button-dropdown-small" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuGroup>
          {levels.map((level) => (
            <DropdownMenuItem key={`heading-${level}`} asChild>
              <Tooltip placement="right" delay={600}>
                <TooltipTrigger asChild>
                  <HeadingButton
                    editor={editor}
                    level={level}
                    text={getFormattedHeadingName(level)}
                    showTooltip={false}
                  />
                </TooltipTrigger>
                <TooltipContent>
                  {getFormattedHeadingName(level)}
                  {headingShortcutKeys[level] && (
                    <div>
                      {parseShortcutKeys(
                        headingShortcutKeys[level],
                        typeof navigator !== "undefined" &&
                          navigator.platform.toLowerCase().includes("mac")
                      ).map((key: string, index: number) => (
                        <React.Fragment key={index}>
                          {index > 0 && <kbd>+</kbd>}
                          <kbd>{key}</kbd>
                        </React.Fragment>
                      ))}
                    </div>
                  )}
                </TooltipContent>
              </Tooltip>
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default HeadingDropdownMenu;
