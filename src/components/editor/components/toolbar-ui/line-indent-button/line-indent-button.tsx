import * as React from "react";
import type { Editor } from "@tiptap/react";

// --- Hooks ---
import { useTiptapEditor } from "@/hooks/editor/use-tiptap-editor";

// --- Icons ---
import { IndentIcon } from "@/components/editor/components/toolbar-icons/indent-icon";

// --- UI Primitives ---
import type { ButtonProps } from "@/components/editor/components/toolbar-ui-primitive/button";
import { Button } from "@/components/editor/components/toolbar-ui-primitive/button";

// --- Utils ---
import {
  isIndentActive,
  toggleIndent,
  indentShortcutKey,
} from "@/components/editor/utils/indent-utils";

export interface LineIndentButtonProps extends Omit<ButtonProps, "type"> {
  /**
   * The TipTap editor instance.
   */
  editor?: Editor | null;
  /**
   * Optional text to display alongside the icon.
   */
  text?: string;
}

export const LineIndentButton = React.forwardRef<
  HTMLButtonElement,
  LineIndentButtonProps
>(
  (
    {
      editor: providedEditor,
      className = "",
      onClick,
      text,
      children,
      ...buttonProps
    },
    ref
  ) => {
    const editor = useTiptapEditor(providedEditor);
    const isActive = editor ? isIndentActive(editor) : false;

    const handleClick = React.useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        onClick?.(e);

        if (!e.defaultPrevented && editor) {
          toggleIndent(editor);
        }
      },
      [onClick, editor]
    );

    if (!editor || !editor.isEditable) {
      return null;
    }

    return (
      <Button
        type="button"
        className={className.trim()}
        data-style="ghost"
        data-active-state={isActive ? "on" : "off"}
        role="button"
        tabIndex={-1}
        aria-label="First-line indent"
        aria-pressed={isActive}
        tooltip="First-line indent"
        shortcutKeys={indentShortcutKey}
        onClick={handleClick}
        {...buttonProps}
        ref={ref}
      >
        {children || (
          <>
            <IndentIcon className="tiptap-button-icon" />
            {text && <span className="tiptap-button-text">{text}</span>}
          </>
        )}
      </Button>
    );
  }
);

LineIndentButton.displayName = "LineIndentButton";

export default LineIndentButton;
