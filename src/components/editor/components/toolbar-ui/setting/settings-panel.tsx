"use client";

import * as React from "react";
import { useTiptapEditor } from "@/hooks/editor/use-tiptap-editor";
import { FontSerifIcon } from "@/components/editor/components/toolbar-icons/font-serif-icon";
import { useFontFamily } from "@/hooks/editor/use-font-family";
import { ALargeSmall } from "lucide-react";
import {
  FONT_SIZE_SMALL,
  FONT_SIZE_MEDIUM,
  FONT_SIZE_LARGE,
} from "@/components/editor/editor";

interface SettingsPanelProps {
  editor?: any;
  onClose?: () => void;
  fontSize?: number;
  setFontSize?: (fontSize: number) => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  editor: providedEditor,
  onClose,
  fontSize = FONT_SIZE_MEDIUM,
  setFontSize,
}) => {
  const editor = useTiptapEditor(providedEditor);
  const { isSerif, toggleFontFamily } = useFontFamily(editor);

  // 处理字体大小选择
  const handleFontSizeChange = (newSize: number) => {
    if (setFontSize) {
      setFontSize(newSize);
    }
  };

  return (
    <div className="mt-1 p-3 z-20 w-68 bg-floatingBar border border-floatingBar-border rounded-2xl shadow-lg backdrop-blur-lg">
      <h3 className="text-sm font-medium text-secondaryBtn dark:text-white/90 mb-3">
        Editor Settings
      </h3>
      <div className="space-y-3">
        {/* 字体样式设置 - 卡片样式 */}
        <div className="p-3 bg-backgroundDeep/40 dark:bg-backgroundDeep/30 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FontSerifIcon className="w-5 h-5 p-0.5 text-secondaryBtn dark:text-white/95 opacity-80" />
              <span className="text-sm text-secondaryBtn dark:text-white/80">
                Serif Font
              </span>
            </div>
            <button
              type="button"
              role="switch"
              aria-checked={isSerif}
              className={`relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none ${
                isSerif ? "bg-secondaryBtn" : "bg-secondaryBtn/10"
              }`}
              onClick={toggleFontFamily}
            >
              <span
                className={`pointer-events-none inline-block h-4 w-4 transform rounded-full shadow ring-0 transition duration-200 ease-in-out ${
                  isSerif
                    ? "translate-x-4 bg-white dark:bg-backgroundDeep"
                    : "translate-x-0 bg-white dark:bg-secondaryBtn"
                }`}
              />
            </button>
          </div>
        </div>

        {/* 字体大小设置 - 卡片样式 */}
        <div className="p-3 bg-backgroundDeep/40 dark:bg-backgroundDeep/30 rounded-lg">
          <div className="flex items-center justify-between mb-2.5">
            <div className="flex items-center space-x-2">
              <ALargeSmall className="w-5 h-5 text-secondaryBtn dark:text-white/95 opacity-80" />
              <span className="text-sm text-secondaryBtn dark:text-white/80">
                Font Size
              </span>
            </div>
          </div>
          <div className="flex items-center justify-end gap-2">
            <button
              type="button"
              onClick={() => handleFontSizeChange(FONT_SIZE_SMALL)}
              className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                fontSize === FONT_SIZE_SMALL
                  ? "bg-secondaryBtn text-secondaryBtn-text"
                  : "bg-secondaryBtn/5 text-secondaryBtn dark:text-white/60 hover:bg-secondaryBtn/10"
              }`}
            >
              Small
            </button>
            <button
              type="button"
              onClick={() => handleFontSizeChange(FONT_SIZE_MEDIUM)}
              className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                fontSize === FONT_SIZE_MEDIUM
                  ? "bg-secondaryBtn text-secondaryBtn-text"
                  : "bg-secondaryBtn/5 text-secondaryBtn dark:text-white/60 hover:bg-secondaryBtn/10"
              }`}
            >
              Medium
            </button>
            <button
              type="button"
              onClick={() => handleFontSizeChange(FONT_SIZE_LARGE)}
              className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                fontSize === FONT_SIZE_LARGE
                  ? "bg-secondaryBtn text-secondaryBtn-text"
                  : "bg-secondaryBtn/5 text-secondaryBtn dark:text-white/60 hover:bg-secondaryBtn/10"
              }`}
            >
              Large
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
