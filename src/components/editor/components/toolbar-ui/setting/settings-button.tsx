"use client";

import React, { useContext, useState, useRef, useEffect } from "react";
import { EditorContext } from "@tiptap/react";
import { Button } from "@/components/editor/components/toolbar-ui-primitive/button/button";
import { Ellipsis } from "lucide-react";
import { SettingsPanel } from "./settings-panel";
import { motion, AnimatePresence } from "framer-motion";

interface SettingsButtonProps {
  className?: string;
  fontSize?: number;
  setFontSize?: (fontSize: number) => void;
}

export const SettingsButton: React.FC<SettingsButtonProps> = ({
  className,
  fontSize,
  setFontSize,
}) => {
  const { editor } = useContext(EditorContext);
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭设置面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  if (!editor) {
    return null;
  }

  const toggleSettings = () => {
    setIsOpen(!isOpen);
  };

  // 面板动画变体
  const panelVariants = {
    hidden: {
      opacity: 0,
      y: -5,
      scale: 0.98,
      transition: {
        duration: 0.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.2,
        ease: [0, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      y: -5,
      scale: 0.98,
      transition: {
        duration: 0.1,
        ease: [0.4, 0, 1, 1],
      },
    },
  };

  return (
    <div className="relative" ref={containerRef}>
      <Button
        type="button"
        className={`${className || ""} ${isOpen ? "tiptap-button-active" : ""}`}
        data-style="ghost"
        data-active-state={isOpen ? "on" : "off"}
        role="button"
        tabIndex={-1}
        aria-label="Editor Settings"
        aria-pressed={isOpen}
        tooltip="Editor Settings"
        showTooltip={!isOpen}
        onClick={toggleSettings}
        preserveHoverOnClick={true}
      >
        <Ellipsis className="tiptap-button-icon" size={18} />
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={panelVariants}
            style={{ originY: 0, originX: 1 }}
            className="absolute right-0 top-full"
          >
            <SettingsPanel
              editor={editor}
              onClose={() => setIsOpen(false)}
              fontSize={fontSize}
              setFontSize={setFontSize}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SettingsButton;
