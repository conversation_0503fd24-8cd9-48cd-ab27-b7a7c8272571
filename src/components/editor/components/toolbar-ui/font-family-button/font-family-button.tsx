"use client";

import React, { useContext, useState, useEffect, useRef } from "react";
import { EditorContext } from "@tiptap/react";
import { Button } from "@/components/editor/components/toolbar-ui-primitive/button/button";
import { FontSansIcon } from "../../toolbar-icons/font-sans-icon";
import { FontSerifIcon } from "../../toolbar-icons/font-serif-icon";
import { useFontFamily } from "@/hooks/editor/use-font-family";

interface FontFamilyButtonProps {
  className?: string;
}

export const FontFamilyButton: React.FC<FontFamilyButtonProps> = ({
  className,
}) => {
  const { editor } = useContext(EditorContext);
  const { isSerif, toggleFontFamily } = useFontFamily(editor);

  if (!editor) {
    return null;
  }

  // 根据当前字体状态决定显示的内容
  const tooltipText = isSerif
    ? "Switch to sans-serif font"
    : "Switch to serif font";
  const Icon = isSerif ? FontSansIcon : FontSerifIcon;

  return (
    <Button
      type="button"
      className={`${className || ""}`}
      data-style="ghost"
      data-active-state="off"
      role="button"
      tabIndex={-1}
      aria-label={tooltipText}
      aria-pressed={false}
      tooltip={tooltipText}
      onClick={toggleFontFamily}
    >
      <Icon className="tiptap-button-icon" />
    </Button>
  );
};
