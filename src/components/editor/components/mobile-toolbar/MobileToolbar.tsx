import * as React from "react";
import { useEffect, useState, useCallback } from "react";
import { Editor } from "@tiptap/react";

// 导入现有UI组件
import { MarkButton } from "@/components/editor/components/toolbar-ui/mark-button";
import { ListButton } from "@/components/editor/components/toolbar-ui/list-button";
import { UndoRedoButton } from "@/components/editor/components/toolbar-ui/undo-redo-button";
import { HeadingButton } from "@/components/editor/components/toolbar-ui/heading-button";
import { LineIndentButton } from "@/components/editor/components/toolbar-ui/line-indent-button";

interface MobileToolbarProps {
  editor: Editor | null;
  isActive: boolean;
}

// 移动端简化版工具栏
const MobileToolbar: React.FC<MobileToolbarProps> = ({ editor, isActive }) => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  // 用于强制刷新组件状态的计数器
  const [updateCounter, setUpdateCounter] = useState(0);

  // 处理工具栏容器的点击事件，防止失去编辑器焦点
  const handleToolbarClick = useCallback(
    (e: React.MouseEvent) => {
      // 阻止事件冒泡和默认行为
      e.preventDefault();
      e.stopPropagation();

      // 确保编辑器保持焦点
      if (editor) {
        setTimeout(() => {
          editor.commands.focus();
        }, 0);
      }
    },
    [editor]
  );

  // 监听编辑器状态变化，用于更新工具栏状态
  useEffect(() => {
    if (!editor) return;

    const updateToolbarState = () => {
      // 强制工具栏重新渲染以反映最新状态
      setUpdateCounter((prev) => prev + 1);
    };

    // 监听可能导致格式变化的事件
    editor.on("selectionUpdate", updateToolbarState);
    editor.on("transaction", updateToolbarState);

    return () => {
      editor.off("selectionUpdate", updateToolbarState);
      editor.off("transaction", updateToolbarState);
    };
  }, [editor]);

  useEffect(() => {
    // 使用视口高度变化检测键盘是否弹出
    const detectKeyboard = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;

      // 当视口高度小于窗口高度时，说明键盘可能已弹出
      if (viewportHeight < windowHeight) {
        const estimatedKeyboardHeight = windowHeight - viewportHeight;
        setKeyboardHeight(estimatedKeyboardHeight);
      } else {
        setKeyboardHeight(0);
      }
    };

    // 添加视口变化监听器
    window.visualViewport?.addEventListener("resize", detectKeyboard);

    return () => {
      window.visualViewport?.removeEventListener("resize", detectKeyboard);
    };
  }, []);

  if (!editor) {
    return null;
  }

  return (
    <div
      className={`fixed left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-md dark:bg-gray-800 dark:border-gray-700 transition-all duration-300 ease-in-out ${
        !isActive ? "h-0 opacity-0 overflow-hidden border-t-0" : "opacity-100"
      }`}
      style={{
        bottom: `${keyboardHeight}px`,
        transition:
          "bottom 0.3s ease-out, height 0.3s ease-out, opacity 0.3s ease-out",
      }}
      onClick={handleToolbarClick}
      onTouchStart={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.preventDefault()}
    >
      <div
        className="flex items-center justify-between px-2 py-1 overflow-x-auto"
        key={updateCounter}
      >
        {/* 基本文本格式化工具 */}
        <div className="flex space-x-2">
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <MarkButton editor={editor} type="bold" />
          </div>
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <MarkButton editor={editor} type="italic" />
          </div>
          {/* <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <MarkButton editor={editor} type="underline" />
          </div> */}
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <MarkButton editor={editor} type="strike" />
          </div>
        </div>

        {/* 段落格式工具 */}
        <div className="flex space-x-2">
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <HeadingButton editor={editor} level={2} />
          </div>
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <ListButton editor={editor} type="bulletList" />
          </div>
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <ListButton editor={editor} type="orderedList" />
          </div>
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <LineIndentButton editor={editor} />
          </div>
        </div>

        {/* 其他操作 */}
        <div className="flex space-x-2">
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <UndoRedoButton editor={editor} action="undo" />
          </div>
          <div
            className="touch-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            <UndoRedoButton editor={editor} action="redo" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileToolbar;
