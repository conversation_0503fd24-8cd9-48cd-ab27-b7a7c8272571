import * as React from "react";
// import { WholeWord } from "lucide-react";

interface EditorFooterBarProps {
  characterCount: number;
}

/**
 * 编辑器底部状态栏组件
 * 包含字数统计
 */
export const EditorFooterBar: React.FC<EditorFooterBarProps> = ({
  characterCount,
}) => {
  return (
    <div className="flex items-center justify-between px-4 py-1.5 bg-background text-xs text-gray-600 dark:text-gray-400 z-10">
      {/* 字数显示 */}
      <div className="flex items-center space-x-1">
        {/* <WholeWord size={14} className="opacity-70" /> */}
        <span> {characterCount.toLocaleString()} characters</span>
      </div>

      {/* 保留空白区域以保持布局平衡 */}
      <div></div>
    </div>
  );
};

export default EditorFooterBar;
