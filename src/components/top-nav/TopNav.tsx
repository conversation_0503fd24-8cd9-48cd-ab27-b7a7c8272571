"use client";

import React, { useState, useEffect, useRef } from "react";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import dynamic from "next/dynamic";
import CustomAvatar from "./components/Avatar";
import Logo from "./components/Logo";
import { PanelLeftIcon } from "../ui/icons/PanelLeftIcon";
import { PanelRightIcon } from "../ui/icons/PanelRightIcon";
import { Tooltip, TooltipTrigger, TooltipContent } from "../ui/tooltip/tooltip";
import { Menu, Bell } from "lucide-react";
import { GlobalMenu } from "./components/GlobalMenu";
import { AnimatePresence, motion } from "framer-motion";
import { useMenuControl } from "@/hooks/top-nav/useMenuControl";
import { usePathname } from "next/navigation";

// 动态导入Icon组件
const Icon = dynamic(() => import("framesound-ui").then((mod) => mod.Icon), {
  ssr: false,
});

interface TopNavProps {
  currentFilePath?: string[];
  isSidebarCollapsed?: boolean;
  isAiPanelCollapsed?: boolean;
  isEventPanelCollapsed?: boolean;
  isEventPanelMobile?: boolean; // 是否为移动设备视图
  eventPanelViewMode?: "desktop" | "tablet" | "mobile"; // 事件面板视图模式
  onToggleSidebar?: () => void;
  onToggleAiPanel?: () => void;
  onToggleEventPanel?: () => void;
  projectId?: string; // 项目ID，用于在项目编辑页面显示项目信息
}

const TopNav: React.FC<TopNavProps> = ({
  currentFilePath = [],
  isSidebarCollapsed = false,
  isAiPanelCollapsed = false,
  isEventPanelCollapsed = false,
  isEventPanelMobile = false, // 是否为移动设备视图
  eventPanelViewMode = "desktop", // 事件面板视图模式
  onToggleSidebar = () => {},
  onToggleAiPanel = () => {},
  onToggleEventPanel = () => {},
  projectId,
}) => {
  // 使用useState但不立即初始化，避免服务端和客户端不一致
  const [localSidebarState, setLocalSidebarState] = useState(false);
  const [localAiPanelState, setLocalAiPanelState] = useState(false);
  const [localEventPanelState, setLocalEventPanelState] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 获取当前路径
  const pathname = usePathname();

  // 在组件挂载后，标记为客户端环境，并设置初始状态
  useEffect(() => {
    setMounted(true);
    setLocalSidebarState(!isSidebarCollapsed);
    setLocalAiPanelState(!isAiPanelCollapsed);
    setLocalEventPanelState(!isEventPanelCollapsed);
  }, [isSidebarCollapsed, isAiPanelCollapsed, isEventPanelCollapsed]);

  // 使用自定义 Hook 处理汉堡菜单
  const {
    isMenuOpen,
    menuRef,
    triggerRef,
    handleTriggerMouseEnter,
    handleTriggerMouseLeave,
    handleMenuMouseEnter,
    handleMenuMouseLeave,
    handleTriggerClick,
    handleMenuItemClick,
  } = useMenuControl();

  // 处理侧边栏切换
  const handleSidebarToggle = () => {
    setLocalSidebarState(!localSidebarState);
    onToggleSidebar();
  };

  // 处理AI面板切换
  const handleAiPanelToggle = () => {
    setLocalAiPanelState(!localAiPanelState);
    onToggleAiPanel();
  };

  // 处理事件面板切换
  const handleEventPanelToggle = () => {
    setLocalEventPanelState(!localEventPanelState);
    onToggleEventPanel();
  };

  // 检查是否在projects页面
  const isProjectsPage = pathname?.startsWith("/projects");
  // 检查是否在editor页面 - 确保精确匹配/project开头但不是/projects
  const isEditorPage =
    pathname?.startsWith("/project") && !pathname?.startsWith("/projects");

  // 获取正确的图标
  const getIcon = (
    type: string,
    index: number,
    total: number
  ): "Folder" | "File" => {
    // 如果是最后一项，则可能是文件，否则一定是文件夹
    if (index === total - 1) {
      // 可以根据文件名判断是否为文件夹
      // 这里简单判断，如果没有扩展名则认为是文件夹
      return type.includes(".") ? "File" : "Folder";
    } else {
      // 路径中间的项一定是文件夹
      return "Folder";
    }
  };

  return (
    <div className="w-full h-12 px-4 flex items-center justify-between">
      {/* 左侧Logo和汉堡菜单和工作区切换 */}
      <div className="flex justify-start items-center gap-2 flex-shrink-0 min-w-[140px]">
        <Tooltip placement="bottom" delay={1000}>
          <TooltipTrigger asChild>
            <div>
              <Logo />
            </div>
          </TooltipTrigger>
          <TooltipContent>Back to Projects</TooltipContent>
        </Tooltip>
        <div className="relative">
          <motion.button
            ref={triggerRef as React.RefObject<HTMLButtonElement>}
            className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 transition-colors group"
            onClick={handleTriggerClick}
            onMouseEnter={handleTriggerMouseEnter}
            onMouseLeave={handleTriggerMouseLeave}
            animate={{
              opacity: isMenuOpen ? 0.5 : 1,
              scale: isMenuOpen ? 0.95 : 1,
            }}
            transition={{
              duration: 0.2,
              ease: "easeInOut",
            }}
          >
            <Menu
              size={18}
              className="text-secondaryBtn/50 group-hover:text-secondaryBtn"
            />
          </motion.button>

          <AnimatePresence>
            {isMenuOpen && (
              <GlobalMenu
                ref={menuRef}
                onMouseEnter={handleMenuMouseEnter}
                onMouseLeave={handleMenuMouseLeave}
                onMenuItemClick={handleMenuItemClick}
                position="left"
              />
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* 中间文件路径 - 使用固定宽度并居中 */}
      <div className="w-1/2 flex justify-center">
        <div className="max-w-full overflow-hidden text-gray-500">
          {currentFilePath.length > 0 ? (
            <Breadcrumbs
              itemClasses={{
                item: "text-gray-500",
                separator: "text-gray-500",
              }}
            >
              {currentFilePath.map((item, index) => (
                <BreadcrumbItem
                  key={index}
                  className="text-gray-500"
                  startContent={
                    <Icon
                      name={getIcon(item, index, currentFilePath.length)}
                      className="w-3 h-3 items-center"
                      color="#697282"
                    />
                  }
                >
                  {item}
                </BreadcrumbItem>
              ))}
            </Breadcrumbs>
          ) : !isProjectsPage ? (
            <Breadcrumbs
              itemClasses={{
                item: "text-gray-500",
                separator: "text-gray-500",
              }}
            >
              <BreadcrumbItem
                className="text-gray-500"
                startContent={
                  <Icon name="Folder" className="w-3 h-3" color="#697282" />
                }
              >
                Home
              </BreadcrumbItem>
            </Breadcrumbs>
          ) : null}
        </div>
      </div>

      {/* 右侧控制按钮和用户头像 */}
      <div className="w-1/4 flex justify-end items-center">
        {/* 只在editor页面显示控制按钮组 */}
        {isEditorPage && (
          <div className="flex items-center mr-0 gap-1">
            {/* 侧边栏折叠/展开按钮 */}
            <Tooltip placement="bottom" delay={300}>
              <TooltipTrigger asChild>
                <button
                  onClick={handleSidebarToggle}
                  className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 transition-colors"
                >
                  <PanelLeftIcon
                    size={18}
                    isCollapsed={!localSidebarState}
                    className={`text-secondaryBtn ${
                      !localSidebarState ? "opacity-50" : "opacity-80"
                    }`}
                  />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                {!localSidebarState ? "Expand Sidebar" : "Collapse Sidebar"}
              </TooltipContent>
            </Tooltip>

            {/* AI面板折叠/展开按钮 */}
            <Tooltip placement="bottom" delay={300}>
              <TooltipTrigger asChild>
                <button
                  onClick={handleAiPanelToggle}
                  className="p-1.5 rounded-lg hover:bg-secondaryBtn/5 transition-colors"
                >
                  <PanelRightIcon
                    size={18}
                    isCollapsed={!localAiPanelState}
                    className={`text-secondaryBtn ${
                      !localAiPanelState ? "opacity-50" : "opacity-80"
                    }`}
                  />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                {!localAiPanelState ? "Expand AI Panel" : "Collapse AI Panel"}
              </TooltipContent>
            </Tooltip>
          </div>
        )}

        {/* 在projects页面显示铃铛提醒按钮 */}
        {isProjectsPage && (
          <Tooltip placement="bottom" delay={300}>
            <TooltipTrigger asChild>
              <button
                onClick={handleEventPanelToggle}
                className={`p-1.5 rounded-lg hover:bg-secondaryBtn/5 transition-colors ${
                  (isEventPanelMobile || eventPanelViewMode !== "desktop") &&
                  !isEventPanelCollapsed
                    ? "bg-secondaryBtn/10"
                    : ""
                }`}
              >
                <Bell
                  size={18}
                  className={`text-secondaryBtn ${
                    mounted && !localEventPanelState
                      ? "opacity-50"
                      : "opacity-80"
                  } ${
                    (isEventPanelMobile || eventPanelViewMode !== "desktop") &&
                    !isEventPanelCollapsed
                      ? "opacity-100"
                      : ""
                  }`}
                />
              </button>
            </TooltipTrigger>
            <TooltipContent>
              {!localEventPanelState ||
              isEventPanelMobile ||
              eventPanelViewMode !== "desktop"
                ? "Show Events"
                : "Hide Events"}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default TopNav;
