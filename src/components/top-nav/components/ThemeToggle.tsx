"use client";

import React, { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { Sun, MoonStar, MonitorSmartphone } from "lucide-react";

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme, resolvedTheme } = useTheme();

  // 确保组件只在客户端渲染后处理主题
  useEffect(() => {
    setMounted(true);
  }, []);

  // 如果组件尚未挂载，不显示任何内容
  if (!mounted) {
    return <div className="w-4 h-4" />;
  }

  // 切换主题函数
  const toggleTheme = () => {
    if (theme === "light") {
      setTheme("dark");
    } else if (theme === "dark") {
      setTheme("system");
    } else {
      setTheme("light");
    }
  };

  // 根据当前主题返回对应的图标
  const renderThemeIcon = () => {
    switch (theme) {
      case "light":
        // 太阳图标 - 当前是亮色模式
        return <Sun className="w-4 h-4" />;
      case "dark":
        // 月亮图标 - 当前是暗色模式
        return <MoonStar className="w-4 h-4" />;
      default:
        // 电脑图标 - 当前是系统模式
        return <MonitorSmartphone className="w-4 h-4" />;
    }
  };

  // 获取当前主题的简短显示名称
  const getShortThemeName = () => {
    switch (theme) {
      case "light":
        return "Light";
      case "dark":
        return "Dark";
      default:
        return "System";
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-xs px-2 py-0.5 rounded-md bg-foreground/5 text-foreground/80">
        {getShortThemeName()}
      </span>
      <button
        onClick={toggleTheme}
        className="text-foreground focus:outline-none"
        aria-label="Switch Theme"
      >
        {renderThemeIcon()}
      </button>
    </div>
  );
}
