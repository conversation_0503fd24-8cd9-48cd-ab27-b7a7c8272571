"use client";

import React from "react";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import Link from "next/link";

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export default function Logo({
  className = "h-[18px] w-[70px] flex-shrink-0",
  width = 70,
  height = 18,
}: LogoProps) {
  // 使用 next-themes 的 useTheme 钩子
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // 确保组件只在客户端渲染后再检测主题
  useEffect(() => {
    setMounted(true);
  }, []);

  // 客户端渲染前显示占位
  if (!mounted) {
    return <div className={className} style={{ width, height }} />;
  }

  // 根据主题确定文本颜色
  const textColor = resolvedTheme === "dark" ? "#D1D1D0" : "#313131";
  // 品牌颜色保持不变
  const brandColor = resolvedTheme === "dark" ? "#D1D1D0" : "#4034ba";

  return (
    <Link href="/projects" className="inline-block flex-shrink-0">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version="1.1"
        width={width}
        height={height}
        viewBox="0 0 93 24"
        className={className}
      >
        <g>
          <g>
            <path
              d="M90.5035,20.6008C88.06035,20.6008,86.07856,18.6981,86.07856,16.35029L93,16.35009L93,7L79,7L79,17.3226C79,21.0108,82.11192,24,85.95152,24L90.94669999999999,24L90.94669999999999,20.599800000000002L90.5046,20.599800000000002L90.5035,20.6008Z"
              fill={brandColor}
              fillOpacity="1"
              style={{ mixBlendMode: "normal" }}
            />
          </g>
          <g>
            <path
              d="M57.79697873535156,7.837162098388672C56.46937873535156,7.141508098388672,55.04087873535156,6.793682098388672,53.51237873535156,6.793682098388672C51.98389873535156,6.793682098388672,50.549548735351564,7.141508098388672,49.21127873535156,7.837162098388672C47.87300873535156,8.532812098388671,46.806468735351565,9.534862098388672,46.00971873535156,10.842342098388672C45.212969735351564,12.149822098388672,44.81507873535156,13.668302098388672,44.81507873535156,15.396842098388673C44.81507873535156,17.12538209838867,45.212969735351564,18.670882098388674,46.00971873535156,19.96678209838867C46.806468735351565,21.26368209838867,47.868158735351564,22.259882098388672,49.19575873535156,22.95558209838867C50.522378735351566,23.651182098388674,51.961578735351566,23.998982098388673,53.51237873535156,23.998982098388673C55.06317873535156,23.998982098388673,56.495578735351565,23.651182098388674,57.813478735351566,22.95558209838867C59.13037873535156,22.259882098388672,60.181378735351565,21.26368209838867,60.96747873535156,19.96678209838867C61.75257873535156,18.669882098388673,62.14567873535157,17.14658209838867,62.14567873535157,15.396842098388673C62.14567873535157,13.647112098388671,61.75257873535156,12.149822098388672,60.96747873535156,10.842342098388672C60.181378735351565,9.534862098388672,59.12457873535156,8.533782098388672,57.79797873535156,7.837162098388672L57.79697873535156,7.837162098388672ZM55.82207873535156,18.449182098388672C55.19517873535156,19.19788209838867,54.41491873535156,19.57168209838867,53.48035873535156,19.57168209838867C52.54579873535156,19.57168209838867,51.733518735351566,19.19788209838867,51.10659873535156,18.449182098388672C50.47967873535156,17.700582098388672,50.16718873535156,16.683122098388672,50.16718873535156,15.396832098388671C50.16718873535156,14.110552098388672,50.480648735351565,13.088272098388671,51.10659873535156,12.329032098388673C51.732548735351564,11.569782098388671,52.52444873535156,11.190162098388672,53.48035873535156,11.190162098388672C54.43626873535156,11.190162098388672,55.19517873535156,11.569782098388671,55.82207873535156,12.329032098388673C56.44807873535156,13.088272098388671,56.76147873535156,14.111522098388672,56.76147873535156,15.396832098388671C56.76147873535156,16.68215209838867,56.44807873535156,17.700582098388672,55.82207873535156,18.449182098388672Z"
              fill={textColor}
              fillOpacity="1"
              style={{ mixBlendMode: "normal" }}
            />
          </g>
          <g>
            <path
              d="M73.38459374389649,19.571697882080077C72.49270374389648,19.571697882080077,71.88713374389648,19.281697882080078,71.56882374389649,18.70169788208008C71.25050374389649,18.121697882080078,71.09135374389649,17.30559788208008,71.09135374389649,16.250497882080076L71.09124374389648,12.298297882080078L76.12639374389649,12.298297882080078L76.12639374389649,7.001417882080078L71.09124374389648,7.001417882080078L71.09135374389649,1.9857978820800781L65.89840374389648,1.9857978820800781L65.89983374389648,7.000447882080078L62.872493743896484,7.000447882080078L62.872493743896484,12.297297882080079L65.89983374389648,12.297297882080079L65.89840374389648,17.166797882080076C65.89840374389648,19.718197882080077,66.48262374389648,21.49489788208008,67.65105374389648,22.49599788208008C68.81853374389648,23.497997882080078,70.36933374389649,23.998097882080078,72.30249374389649,23.998097882080078C74.46859374389649,23.998097882080078,76.14649374389649,23.470997882080077,77.33529374389649,22.41699788208008L75.51959374389648,18.84329788208008C74.81889374389648,19.328897882080078,74.10659374389648,19.57079788208008,73.38549374389649,19.57079788208008L73.38459374389649,19.571697882080077Z"
              fill={textColor}
              fillOpacity="1"
              style={{ mixBlendMode: "normal" }}
            />
          </g>
          <g>
            <path
              d="M42.474346951293946,7.182926913076516L35.67044695129395,7.182926913076516L35.67044695129395,11.589046177978515L37.28134695129395,11.589046177978515L37.28134695129395,14.921816177978515C37.28134695129395,16.356476177978514,37.015446951293946,17.473226177978518,36.484646951293946,18.274826177978518C35.953746951293944,19.076426177978515,35.199746951293946,19.476326177978514,34.22343695129395,19.476326177978514C33.247156951293945,19.476326177978514,32.539686951293945,19.081226177978515,32.168966951293946,18.290226177978518C31.797276951293945,17.499226177978514,31.610946951293947,16.450906177978517,31.610946951293947,15.143426177978515L31.610946951293947,7.182926177978516L24.855546951293945,7.182926177978516L24.855546951293945,11.663236177978515L26.417996951293944,11.663236177978515L26.417996951293944,16.883516177978514C26.417996951293944,19.223826177978516,26.953686951293946,20.995726177978515,28.027026951293944,22.197226177978514C29.099386951293944,23.398726177978517,30.570606951293946,24.000026177978516,32.43972695129395,24.000026177978516C34.796986951293945,24.000026177978516,36.51764695129395,23.019126177978514,37.600646951293946,21.058426177978518C37.66474695129395,21.333026177978518,37.738446951293945,21.943826177978515,37.823846951293945,22.892926177978516L37.88794695129395,23.588526177978515L44.029946951293944,23.588526177978515L44.029946951293944,19.479226177978518L42.47524695129395,19.479226177978518L42.47524695129395,7.182927648178516L42.474346951293946,7.182926913076516Z"
              fill={textColor}
              fillOpacity="1"
              style={{ mixBlendMode: "normal" }}
            />
          </g>
          <g>
            <path
              d="M21.2745,18.403C22.2935,16.5906,22.803,14.4507,22.803,11.9841C22.803,9.51752,22.2935,7.41033,21.2745,5.59701C20.2555,3.78466,18.8804,2.39817,17.15,1.43852C15.4197,0.479827,13.504,0,11.402,0C9.29996,0,7.37844,0.479827,5.63743,1.43852C3.89641,2.39817,2.52127,3.78369,1.51295,5.59701C0.503671,7.41033,0,9.53872,0,11.9841C0,14.4295,0.503671,16.5906,1.51295,18.403C2.52127,20.2163,3.89059,21.6018,5.62093,22.5615C7.35127,23.5211,9.27861,24,11.402,24L23.8822,24L23.8822,19.5688L20.5282,19.5688C20.7951,19.2017,21.0445,18.8134,21.2755,18.403L21.2745,18.403ZM8.47215,18.1978C7.623,17.6177,6.9592,16.791,6.48173,15.7158C6.00426,14.6405,5.76553,13.3976,5.76553,11.9841C5.76553,10.5706,6.00426,9.36529,6.48173,8.30062C6.96017,7.23594,7.623,6.41407,8.47215,5.83404C9.32131,5.25401,10.2976,4.96399,11.402,4.96399C12.5064,4.96399,13.4875,5.25401,14.3483,5.83404C15.2081,6.41407,15.8768,7.23594,16.3543,8.30062C16.8317,9.36529,17.0705,10.5928,17.0705,11.9841C17.0705,13.3754,16.8317,14.6405,16.3543,15.7158C15.8768,16.791,15.2081,17.6187,14.3483,18.1978C13.4885,18.7778,12.5064,19.0678,11.402,19.0678C10.2976,19.0678,9.32034,18.7788,8.47215,18.1978Z"
              fill={textColor}
              fillOpacity="1"
              style={{ mixBlendMode: "normal" }}
            />
          </g>
        </g>
      </svg>
    </Link>
  );
}
