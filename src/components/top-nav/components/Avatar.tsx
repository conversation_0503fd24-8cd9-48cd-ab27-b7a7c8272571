"use client";

import React from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { GlobalMenu } from "./GlobalMenu";
import { useMenuControl } from "@/hooks/top-nav/useMenuControl";

interface AvatarProps {
  /**
   * 用户头像URL，如果未提供则使用默认头像
   */
  src?: string;
  /**
   * 头像尺寸，默认为40px
   */
  size?: number;
  /**
   * 用户名，用于alt文本
   */
  username?: string;
  /**
   * 用户邮箱
   */
  email?: string;
  /**
   * 点击头像时的回调
   */
  onClick?: () => void;
  /**
   * 退出登录回调
   */
  onSignOut?: () => void;
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  size = 32,
  username = "User",
  email = "<EMAIL>",
  onClick,
  onSignOut,
}) => {
  const {
    isMenuOpen,
    menuRef,
    triggerRef,
    handleTriggerMouseEnter,
    handleTriggerMouseLeave,
    handleMenuMouseEnter,
    handleMenuMouseLeave,
    handleTriggerClick,
    handleMenuItemClick,
  } = useMenuControl();

  // 处理退出登录
  const handleSignOut = () => {
    if (onSignOut) onSignOut();
  };

  // 处理头像点击
  const handleAvatarClick = () => {
    handleTriggerClick();
    if (onClick) onClick();
  };

  return (
    <div
      className="relative"
      ref={triggerRef as React.RefObject<HTMLDivElement>}
      onMouseEnter={handleTriggerMouseEnter}
      onMouseLeave={handleTriggerMouseLeave}
    >
      <motion.button
        className="relative inline-flex items-center justify-center rounded-full overflow-hidden focus:outline-none"
        style={{
          width: size,
          height: size,
        }}
        animate={{
          opacity: isMenuOpen ? 0 : 1,
          scale: isMenuOpen ? 0.8 : 1, // 添加缩放效果
        }}
        transition={{
          opacity: {
            duration: 0.3,
            delay: isMenuOpen ? 0 : 0.2, // 菜单关闭后延迟显示头像
            ease: "easeInOut",
          },
          scale: {
            duration: 0.35, // 缩放动画稍微长一点
            delay: isMenuOpen ? 0 : 0.15, // 略微早于透明度变化开始
            ease: "easeOut", // 使用easeOut让出现效果更加自然
          },
        }}
        onClick={handleAvatarClick}
      >
        {src ? (
          <Image
            src={src}
            alt={`${username}'s avatar`}
            width={size}
            height={size}
            className="object-cover"
          />
        ) : (
          <div className="bg-secondaryBtn text-secondaryBtn-text flex items-center justify-center w-full h-full text-lg font-medium">
            {username.charAt(0).toUpperCase()}
          </div>
        )}
      </motion.button>

      <AnimatePresence>
        {isMenuOpen && (
          <GlobalMenu
            ref={menuRef}
            onMouseEnter={handleMenuMouseEnter}
            onMouseLeave={handleMenuMouseLeave}
            onMenuItemClick={handleMenuItemClick}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Avatar;
