"use client";

import React, { forwardRef, useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import {
  FolderClock,
  Settings,
  Moon,
  Shirt,
  HelpCircle,
  LogOut,
  ChevronRight,
  FileText,
  Briefcase,
  Code,
  FolderMinus,
  Star,
} from "lucide-react";
import { ThemeToggle } from "@/components/top-nav/components/ThemeToggle";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MiddleEllipsis } from "@/components/ui/middle-ellipsis";
import { useAuthActions } from "@/hooks/useAuthActions";
import { useProjectsData } from "@/hooks/projects/data/useProjectsData";

interface GlobalMenuProps {
  /**
   * 鼠标进入菜单时的回调
   */
  onMouseEnter: () => void;
  /**
   * 鼠标离开菜单时的回调
   */
  onMouseLeave: () => void;
  /**
   * 菜单项点击回调
   */
  onMenuItemClick: () => void;
  /**
   * 菜单位置，默认为右上角
   */
  position?: "right" | "left";
}

// TODO: 移除以下模拟数据，现在使用真实API数据
// const mockProjects = [...];

export const GlobalMenu = forwardRef<HTMLDivElement, GlobalMenuProps>(
  (
    { onMouseEnter, onMouseLeave, onMenuItemClick, position = "right" },
    ref
  ) => {
    // 使用统一的认证hook
    const {
      isAuthenticated,
      user,
      loading,
      isLoginExpired,
      getAuthStatusMessage,
      handleLogin,
      handleLogout,
    } = useAuthActions();

    // 添加本地用户信息状态
    const [localUserInfo, setLocalUserInfo] = useState<{
      email?: string;
      name?: string;
      id?: string;
    } | null>(null);

    // 从localStorage获取用户信息
    useEffect(() => {
      try {
        const userStr = localStorage.getItem("user");
        if (userStr) {
          const userData = JSON.parse(userStr);
          console.log("从本地存储获取到用户信息:", userData);
          setLocalUserInfo(userData);
        }
      } catch (error) {
        console.error("从localStorage获取用户信息失败:", error);
      }
    }, []);

    // 监听用户信息变化，更新本地存储
    useEffect(() => {
      if (user && Object.keys(user).length > 0) {
        // 如果SDK提供的用户信息有效，更新本地存储
        localStorage.setItem("user", JSON.stringify(user));
        console.log("更新本地存储的用户信息:", user);
      }
    }, [user]);

    // 获取用户信息 - 仅使用isAuthenticated判断登录状态，提高安全性
    const isUserLoggedIn = isAuthenticated;
    const username = isUserLoggedIn
      ? user?.name ||
        localUserInfo?.name ||
        (user?.email || localUserInfo?.email)?.split("@")[0] ||
        "用户"
      : "未登录";
    const email = user?.email || localUserInfo?.email || "";
    const avatarSrc = user?.avatar || "";

    // 跟踪当前悬停的菜单项
    const [showProjectsSubmenu, setShowProjectsSubmenu] = useState(false);

    // 获取项目数据
    const { projects, isLoading: projectsLoading } = useProjectsData({
      sortBy: "lastModified",
      maxItems: 5, // 只显示最近的5个项目
    });

    // 包装登出处理函数，添加额外的组件特定逻辑
    const handleSignOut = async () => {
      await handleLogout();
      setLocalUserInfo(null);
      onMenuItemClick(); // 关闭菜单
    };

    // 包装登录处理函数，添加额外的组件特定逻辑
    const wrapperHandleLogin = async () => {
      // 关闭菜单
      onMenuItemClick();
      // 调用统一的登录处理
      await handleLogin();
    };

    // 根据位置设置不同的样式
    const positionStyles = {
      right: {
        className: "absolute -right-2 -mt-10",
        transformOrigin: "top right",
      },
      left: {
        className: "fixed left-2 top-10",
        transformOrigin: "top center",
      },
    };

    const currentPosition = positionStyles[position];

    // 如果用户数据正在加载中，可以显示加载状态
    if (loading) {
      return (
        <motion.div
          ref={ref}
          initial={{ opacity: 0, scale: 0.95, y: 0 }}
          animate={{
            opacity: 1,
            scale: 1,
            y: 8,
          }}
          exit={{ opacity: 0, scale: 0.95, y: 0 }}
          transition={{
            duration: 0.2,
            ease: "easeInOut",
          }}
          className={`${currentPosition.className} rounded-2xl bg-floatingBar/90 backdrop-blur-lg shadow-lg border border-floatingBar-border focus:outline-none z-50 overflow-hidden p-4`}
          style={{ transformOrigin: currentPosition.transformOrigin }}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
        >
          <div className="flex items-center justify-center">
            <p className="text-sm text-foreground/70">加载中...</p>
          </div>
        </motion.div>
      );
    }

    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, scale: 0.95, y: 0 }}
        animate={{
          opacity: 1,
          scale: 1,
          y: 8,
        }}
        exit={{ opacity: 0, scale: 0.95, y: 0 }}
        transition={{
          duration: 0.2,
          ease: "easeInOut",
        }}
        className={`${currentPosition.className} rounded-2xl bg-floatingBar/90 backdrop-blur-lg shadow-lg border border-floatingBar-border focus:outline-none z-50 overflow-hidden`}
        style={{ transformOrigin: currentPosition.transformOrigin }}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <motion.div
          animate={{
            width: showProjectsSubmenu ? "32rem" : "14rem", // 当二级菜单显示时，宽度变为原来的两倍
          }}
          transition={{
            duration: 0.2,
            ease: "easeInOut",
          }}
          className="flex"
        >
          {/* 一级菜单 */}
          <div className="w-56 flex-shrink-0">
            <div className="pl-4 pr-2 py-3">
              {/* 用户信息部分 */}
              <div className="flex items-center justify-between gap-3">
                <div className="flex-shrink-0">
                  {isUserLoggedIn ? (
                    avatarSrc ? (
                      <Image
                        src={avatarSrc}
                        alt={`${username}'s avatar`}
                        width={32}
                        height={32}
                        className="rounded-full object-cover"
                      />
                    ) : (
                      <div className="bg-foreground text-background flex items-center justify-center w-8 h-8 rounded-full text-lg font-medium">
                        {username.charAt(0).toUpperCase()}
                      </div>
                    )
                  ) : (
                    <div className="bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 flex items-center justify-center w-8 h-8 rounded-full text-lg font-medium">
                      ?
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0 pl-1">
                  <p className="text-sm font-bold text-foreground/80">
                    <MiddleEllipsis
                      text={username}
                      maxLength={18}
                      startChars={10}
                      endChars={5}
                    />
                  </p>
                  {isUserLoggedIn && (
                    <p className="text-xs text-foreground/60">
                      {email ? (
                        <MiddleEllipsis
                          text={email}
                          maxLength={22}
                          startChars={14}
                          endChars={8}
                        />
                      ) : (
                        "无邮箱信息"
                      )}
                    </p>
                  )}
                  {isLoginExpired() && (
                    <p className="text-xs text-foreground/60 font-medium">
                      {getAuthStatusMessage()}
                    </p>
                  )}
                  {!isUserLoggedIn && !isLoginExpired() && (
                    <p className="text-xs text-foreground/60">
                      点击底部"Sign in"按钮登录
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* 分割线 */}
            <div className="h-px bg-foreground/5 mx-2 my-0"></div>

            {/* 菜单选项 */}
            <div className="py-2">
              {/* My Projects 项目带二级菜单 - 只在登录状态下显示 */}
              {isUserLoggedIn && (
                <div
                  className="relative"
                  onMouseEnter={() => setShowProjectsSubmenu(true)}
                  onMouseLeave={() => setShowProjectsSubmenu(false)}
                >
                  <div className="flex items-center justify-between mx-2 px-3 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg group cursor-pointer">
                    <div className="flex items-center">
                      <FolderClock className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                      Recent Projects
                    </div>
                    <ChevronRight className="h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                  </div>
                </div>
              )}

              <Link
                href="/settings"
                className="flex items-center mx-2 px-3 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg group"
                onClick={onMenuItemClick}
              >
                <Settings className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                Settings
              </Link>
              <div className="flex items-center justify-between mx-2 px-3 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 rounded-lg group">
                <div className="flex items-center">
                  <Shirt className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                  Theme
                </div>
                <div className="flex-shrink-0">
                  <ThemeToggle />
                </div>
              </div>
            </div>

            {/* 分割线 */}
            <div className="h-px bg-foreground/5 mx-2 my-0"></div>

            {/* 底部选项 */}
            <div className="py-2">
              <Link
                href={`${process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL}/help-center`}
                className="flex items-center mx-2 px-3 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg group"
                onClick={onMenuItemClick}
              >
                <HelpCircle className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                Help Center
              </Link>

              {isUserLoggedIn ? (
                <button
                  onClick={handleSignOut}
                  className="text-left w-[calc(100%-1rem)] flex items-center mx-2 px-3 py-2.5 text-sm font-medium text-foreground/70 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-400/10 rounded-lg group"
                >
                  <LogOut className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-red-500" />
                  <span>Sign out</span>
                </button>
              ) : (
                <button
                  onClick={wrapperHandleLogin}
                  className="text-left w-[calc(100%-1rem)] flex items-center mx-2 px-3 py-2.5 text-sm font-medium text-foreground/70 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-400/10 rounded-lg group"
                >
                  <LogOut className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-blue-500" />
                  <span>{isLoginExpired() ? "Re-login" : "Sign in"}</span>
                </button>
              )}
            </div>
          </div>

          {/* 二级菜单 */}
          <AnimatePresence>
            {isUserLoggedIn && showProjectsSubmenu && (
              <motion.div
                initial={{ x: 0, opacity: 0, width: 0 }}
                animate={{ x: 0, opacity: 1, width: "14rem" }}
                exit={{ x: 0, opacity: 0, width: 0 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
                className=""
                onMouseEnter={() => setShowProjectsSubmenu(true)}
                onMouseLeave={() => setShowProjectsSubmenu(false)}
              >
                <div className="py-3 pl-0 pr-3 w-72 h-full flex">
                  <div className="bg-foreground/[0.03] rounded-xl p-2 flex-1 flex flex-col">
                    <h3 className="text-xs font-semibold text-foreground/60 px-3 mb-2">
                      Recent Projects
                    </h3>
                    {projectsLoading ? (
                      <div className="flex items-center justify-center py-4">
                        <div className="text-xs text-foreground/50">
                          Loading projects...
                        </div>
                      </div>
                    ) : projects.length > 0 ? (
                      projects.map((project) => (
                        <Tooltip
                          key={project.id}
                          delay={1000}
                          placement="right"
                        >
                          <TooltipTrigger asChild>
                            <Link
                              href={`/project/${project.id}`}
                              className="flex items-center justify-between px-3 py-2 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg group"
                              onClick={onMenuItemClick}
                            >
                              <div className="flex items-center min-w-0">
                                <span className="mr-3 flex-shrink-0 text-foreground/50 group-hover:text-foreground">
                                  <FolderMinus className="h-4 w-4" />
                                </span>
                                <MiddleEllipsis
                                  text={project.name}
                                  className="max-w-[185px]"
                                  title={project.name}
                                  maxLength={26}
                                  startChars={20}
                                  endChars={6}
                                  chineseWidthRatio={1.8}
                                />
                              </div>
                              {project.is_favorite && (
                                <Star className="h-3.5 w-3.5 text-amber-400" />
                              )}
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="max-w-xs p-1.5 text-xs text-left">
                              {project.description ||
                                "No description available"}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      ))
                    ) : (
                      <div className="flex items-center justify-center py-4">
                        <div className="text-xs text-foreground/50">
                          No recent projects
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    );
  }
);

GlobalMenu.displayName = "GlobalMenu";
