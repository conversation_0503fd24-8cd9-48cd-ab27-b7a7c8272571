import { useState, useEffect, useRef, useCallback } from "react";

/**
 * 管理事件面板的折叠状态
 * 使用localStorage保存状态，在页面刷新后保持一致
 * 新用户首次访问时默认为折叠状态
 * 支持多级响应式布局
 */
export function useEventPanelState() {
  // 初始状态设置为折叠状态(true)，确保新用户首次访问时默认折叠
  const [isCollapsed, setIsCollapsed] = useState<boolean>(true);
  // 使用 useRef 跟踪初始化状态，确保只初始化一次
  const hasInitialized = useRef<boolean>(false);
  // 记录视图模式: desktop(>=900px), tablet(>=560px & <900px), mobile(<560px)
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">(
    "desktop"
  );
  // 记录上一次的折叠状态，用于在视图模式切换时保持状态
  const lastDesktopStateRef = useRef<boolean>(true);

  /**
   * 异步保存数据到localStorage，避免阻塞主线程
   */
  const saveToLocalStorage = useCallback((value: boolean) => {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem("eventPanelCollapsed", JSON.stringify(value));
    } catch (e) {
      console.error("Failed to save event panel state to localStorage:", e);
    }
  }, []);

  // 检测窗口大小变化，判断当前视图模式
  useEffect(() => {
    if (typeof window === "undefined") return;

    const checkViewMode = () => {
      const prevViewMode = viewMode;
      let newViewMode: "desktop" | "tablet" | "mobile" = "desktop";

      const width = window.innerWidth;
      if (width >= 900) {
        // md2断点及以上
        newViewMode = "desktop";
      } else if (width >= 560) {
        // sm断点及以上，md2断点以下
        newViewMode = "tablet";
      } else {
        // sm断点以下
        newViewMode = "mobile";
      }

      // 如果视图模式发生变化
      if (prevViewMode !== newViewMode) {
        if (newViewMode === "desktop") {
          // 切换到桌面视图时，恢复之前保存的状态
          // 这里不立即设置状态，而是在下次切换面板时使用
        } else if (prevViewMode === "desktop") {
          // 从桌面视图切换到其他视图时，保存当前状态
          lastDesktopStateRef.current = isCollapsed;
        }

        setViewMode(newViewMode);
      }
    };

    // 初始检查
    checkViewMode();

    // 监听窗口大小变化
    window.addEventListener("resize", checkViewMode);
    return () => window.removeEventListener("resize", checkViewMode);
  }, [viewMode, isCollapsed]);

  // 在客户端环境中，从localStorage获取保存的状态
  useEffect(() => {
    // 如果不是客户端环境或已经初始化过，则不执行
    if (typeof window === "undefined" || hasInitialized.current) return;

    try {
      const saved = localStorage.getItem("eventPanelCollapsed");
      // 如果localStorage中有值，则使用该值
      // 如果没有值（新用户），则保持默认的折叠状态(true)
      if (saved !== null) {
        const savedState = JSON.parse(saved);
        setIsCollapsed(savedState);
        lastDesktopStateRef.current = savedState;
      } else {
        // 新用户首次访问，将默认折叠状态保存到localStorage
        saveToLocalStorage(true);
      }
      // 标记为已初始化
      hasInitialized.current = true;
    } catch (e) {
      console.error("Failed to load event panel state from localStorage:", e);
    }
  }, [saveToLocalStorage]);

  // 切换折叠状态
  const toggleCollapsed = useCallback(() => {
    setIsCollapsed((prev) => {
      const newState = !prev;

      // 只在桌面视图下保存状态到localStorage
      if (viewMode === "desktop") {
        saveToLocalStorage(newState);
        lastDesktopStateRef.current = newState;
      }

      return newState;
    });
  }, [saveToLocalStorage, viewMode]);

  return {
    isCollapsed,
    toggleCollapsed,
    isMobile: viewMode !== "desktop", // 向后兼容
    viewMode,
  };
}
