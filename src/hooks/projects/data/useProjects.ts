/**
 * 项目管理主 Hook
 * 组合数据管理和 UI 状态管理，提供统一的项目管理接口
 */

import { useCallback, useEffect } from "react";
import {
  useProjectsData,
  UseProjectsDataOptions,
  ProjectFilterType,
} from "./useProjectsData";
import { useProjectsUI, ViewType } from "./useProjectsUI";
import { ProjectItem, Project } from "@/lib/api/types/project";

// Hook 配置选项
interface UseProjectsOptions extends UseProjectsDataOptions {
  initialViewType?: ViewType;
  initialFilter?: ProjectFilterType;
}

// Hook 返回值接口
interface UseProjectsReturn {
  // 数据状态
  projects: ProjectItem[];
  filteredProjects: ProjectItem[];
  isLoading: boolean;
  error: string | null;
  isDataStale: boolean;

  // UI 状态
  viewType: ViewType;
  searchQuery: string;
  activeFilter: ProjectFilterType;
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isCopyModalOpen: boolean;
  isDeleteModalOpen: boolean;
  selectedProject: ProjectItem | null;
  isGridView: boolean;

  // 数据操作方法
  fetchProjects: () => Promise<void>;
  refreshProjects: () => Promise<void>;
  createProject: (data: {
    name: string;
    description: string;
  }) => Promise<Project | null>;
  updateProject: (
    projectId: string,
    data: { name?: string; description?: string }
  ) => Promise<Project | null>;
  deleteProject: (projectId: string) => Promise<boolean>;
  toggleFavorite: (projectId: string, isFavorite: boolean) => Promise<boolean>;
  clearCache: () => void;

  // UI 操作方法
  setViewType: (type: ViewType) => void;
  toggleViewType: () => void;
  setSearchQuery: (query: string) => void;
  setActiveFilter: (filter: ProjectFilterType) => void;
  clearSearch: () => void;
  openCreateModal: () => void;
  openEditModal: (project: ProjectItem) => void;
  openCopyModal: (project: ProjectItem) => void;
  openDeleteModal: (project: ProjectItem) => void;
  closeAllModals: () => void;
  selectProject: (project: ProjectItem | null) => void;
  clearSelection: () => void;

  // 组合操作方法
  handleSearch: (query: string) => void;
  handleFilterChange: (filter: ProjectFilterType) => void;
  handleProjectCreate: (data: {
    name: string;
    description: string;
  }) => Promise<void>;
  handleProjectEdit: (data: {
    name?: string;
    description?: string;
  }) => Promise<void>;
  handleProjectCopy: (data: {
    name: string;
    description: string;
  }) => Promise<void>;
  handleProjectDelete: () => Promise<void>;
  handleFavoriteToggle: (project: ProjectItem) => Promise<void>;
}

/**
 * 项目管理主 Hook
 *
 * ▶︎ **核心特性**:
 * - 统一的项目数据和 UI 状态管理
 * - 自动同步搜索和过滤状态
 * - 简化的操作方法，减少组件复杂度
 * - 完整的错误处理和用户反馈
 *
 * ▶︎ **使用场景**:
 * - 项目页面的主要数据和状态管理
 * - 替代原有的分散状态管理逻辑
 * - 提供统一的项目操作接口
 *
 * ▶︎ **性能优化**:
 * - 智能缓存和数据同步
 * - 乐观更新提升用户体验
 * - 防抖搜索减少 API 调用
 *
 * @param options 配置选项
 * @returns 项目数据、UI 状态和操作方法
 */
export function useProjects(
  options: UseProjectsOptions = {}
): UseProjectsReturn {
  const {
    initialViewType = "grid",
    initialFilter = "all",
    ...dataOptions
  } = options;

  // 数据管理 Hook
  const {
    projects,
    filteredProjects,
    isLoading,
    error,
    isDataStale,
    fetchProjects,
    refreshProjects,
    createProject,
    updateProject,
    deleteProject,
    toggleFavorite,
    applyFilter,
    clearCache,
  } = useProjectsData(dataOptions);

  // UI 状态管理 Hook
  const {
    viewType,
    searchQuery,
    activeFilter,
    isCreateModalOpen,
    isEditModalOpen,
    isCopyModalOpen,
    isDeleteModalOpen,
    selectedProject,
    isGridView,
    setViewType,
    toggleViewType,
    setSearchQuery,
    setActiveFilter,
    clearSearch,
    openCreateModal,
    openEditModal,
    openCopyModal,
    openDeleteModal,
    closeAllModals,
    selectProject,
    clearSelection,
  } = useProjectsUI(initialViewType, initialFilter);

  // 初始化过滤器
  useEffect(() => {
    setActiveFilter(initialFilter);
  }, [initialFilter, setActiveFilter]);

  // 同步搜索和过滤状态到数据层
  useEffect(() => {
    applyFilter(activeFilter, searchQuery);
  }, [activeFilter, searchQuery, applyFilter]);

  // 组合操作方法
  const handleSearch = useCallback(
    (query: string) => {
      setSearchQuery(query);
    },
    [setSearchQuery]
  );

  const handleFilterChange = useCallback(
    (filter: ProjectFilterType) => {
      setActiveFilter(filter);
    },
    [setActiveFilter]
  );

  const handleProjectCreate = useCallback(
    async (data: { name: string; description: string }) => {
      const result = await createProject(data);
      if (result) {
        closeAllModals();
      }
    },
    [createProject, closeAllModals]
  );

  const handleProjectEdit = useCallback(
    async (data: { name?: string; description?: string }) => {
      if (!selectedProject) return;

      const result = await updateProject(selectedProject.id, data);
      if (result) {
        closeAllModals();
      }
    },
    [selectedProject, updateProject, closeAllModals]
  );

  const handleProjectCopy = useCallback(
    async (data: { name: string; description: string }) => {
      if (!selectedProject) return;

      const result = await createProject({
        name: data.name,
        description: data.description || selectedProject.description,
      });
      if (result) {
        closeAllModals();
      }
    },
    [selectedProject, createProject, closeAllModals]
  );

  const handleProjectDelete = useCallback(async () => {
    if (!selectedProject) return;

    const result = await deleteProject(selectedProject.id);
    if (result) {
      closeAllModals();
    }
  }, [selectedProject, deleteProject, closeAllModals]);

  const handleFavoriteToggle = useCallback(
    async (project: ProjectItem) => {
      // await toggleFavorite(project.id, !project.is_favorite);
      // 临时使用固定的 project_id 进行测试
      const TEMP_PROJECT_ID = "00000002-1234-5678-9abc-def012345678";
      await toggleFavorite(TEMP_PROJECT_ID, project.is_favorite);
    },
    [toggleFavorite]
  );

  return {
    // 数据状态
    projects,
    filteredProjects,
    isLoading,
    error,
    isDataStale,

    // UI 状态
    viewType,
    searchQuery,
    activeFilter,
    isCreateModalOpen,
    isEditModalOpen,
    isCopyModalOpen,
    isDeleteModalOpen,
    selectedProject,
    isGridView,

    // 数据操作方法
    fetchProjects,
    refreshProjects,
    createProject,
    updateProject,
    deleteProject,
    toggleFavorite,
    clearCache,

    // UI 操作方法
    setViewType,
    toggleViewType,
    setSearchQuery,
    setActiveFilter,
    clearSearch,
    openCreateModal,
    openEditModal,
    openCopyModal,
    openDeleteModal,
    closeAllModals,
    selectProject,
    clearSelection,

    // 组合操作方法
    handleSearch,
    handleFilterChange,
    handleProjectCreate,
    handleProjectEdit,
    handleProjectCopy,
    handleProjectDelete,
    handleFavoriteToggle,
  };
}

// 导出类型
export type { UseProjectsReturn, UseProjectsOptions };
