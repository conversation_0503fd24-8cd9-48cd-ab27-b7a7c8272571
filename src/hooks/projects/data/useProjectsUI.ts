/**
 * 项目页面 UI 状态管理 Hook
 * 管理项目页面的 UI 状态，如模态框、视图类型、选中项目等
 */

import { useState, useCallback } from 'react';
import { ProjectItem } from '@/lib/api/types/project';
import { ProjectFilterType } from './useProjectsData';

// 视图类型定义
export type ViewType = 'grid' | 'list';

// UI 状态接口
interface UseProjectsUIState {
  // 视图相关
  viewType: ViewType;
  
  // 搜索和过滤
  searchQuery: string;
  activeFilter: ProjectFilterType;
  
  // 模态框状态
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isCopyModalOpen: boolean;
  isDeleteModalOpen: boolean;
  
  // 选中的项目
  selectedProject: ProjectItem | null;
  
  // 其他 UI 状态
  isGridView: boolean;
}

// Hook 返回值接口
interface UseProjectsUIReturn extends UseProjectsUIState {
  // 视图控制
  setViewType: (type: ViewType) => void;
  toggleViewType: () => void;
  
  // 搜索和过滤
  setSearchQuery: (query: string) => void;
  setActiveFilter: (filter: ProjectFilterType) => void;
  clearSearch: () => void;
  
  // 模态框控制
  openCreateModal: () => void;
  openEditModal: (project: ProjectItem) => void;
  openCopyModal: (project: ProjectItem) => void;
  openDeleteModal: (project: ProjectItem) => void;
  closeAllModals: () => void;
  
  // 项目选择
  selectProject: (project: ProjectItem | null) => void;
  clearSelection: () => void;
}

/**
 * 项目页面 UI 状态管理 Hook
 * 
 * ▶︎ **核心特性**:
 * - 统一管理所有 UI 状态
 * - 提供便捷的状态操作方法
 * - 自动处理模态框的项目选择
 * - 响应式视图类型管理
 * 
 * ▶︎ **使用场景**:
 * - 项目页面的视图切换
 * - 模态框的打开和关闭
 * - 搜索和过滤状态管理
 * - 项目选择和操作
 * 
 * @param initialViewType 初始视图类型
 * @param initialFilter 初始过滤器类型
 * @returns UI 状态和操作方法
 */
export function useProjectsUI(
  initialViewType: ViewType = 'grid',
  initialFilter: ProjectFilterType = 'all'
): UseProjectsUIReturn {
  // 视图状态
  const [viewType, setViewType] = useState<ViewType>(initialViewType);
  
  // 搜索和过滤状态
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<ProjectFilterType>(initialFilter);
  
  // 模态框状态
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCopyModalOpen, setIsCopyModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  
  // 选中的项目
  const [selectedProject, setSelectedProject] = useState<ProjectItem | null>(null);
  
  // 计算属性
  const isGridView = viewType === 'grid';
  
  // 视图控制方法
  const toggleViewType = useCallback(() => {
    setViewType(prev => prev === 'grid' ? 'list' : 'grid');
  }, []);
  
  // 搜索控制方法
  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);
  
  // 模态框控制方法
  const openCreateModal = useCallback(() => {
    setSelectedProject(null);
    setIsCreateModalOpen(true);
  }, []);
  
  const openEditModal = useCallback((project: ProjectItem) => {
    setSelectedProject(project);
    setIsEditModalOpen(true);
  }, []);
  
  const openCopyModal = useCallback((project: ProjectItem) => {
    setSelectedProject(project);
    setIsCopyModalOpen(true);
  }, []);
  
  const openDeleteModal = useCallback((project: ProjectItem) => {
    setSelectedProject(project);
    setIsDeleteModalOpen(true);
  }, []);
  
  const closeAllModals = useCallback(() => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsCopyModalOpen(false);
    setIsDeleteModalOpen(false);
    setSelectedProject(null);
  }, []);
  
  // 项目选择方法
  const selectProject = useCallback((project: ProjectItem | null) => {
    setSelectedProject(project);
  }, []);
  
  const clearSelection = useCallback(() => {
    setSelectedProject(null);
  }, []);
  
  return {
    // 状态
    viewType,
    searchQuery,
    activeFilter,
    isCreateModalOpen,
    isEditModalOpen,
    isCopyModalOpen,
    isDeleteModalOpen,
    selectedProject,
    isGridView,
    
    // 视图控制
    setViewType,
    toggleViewType,
    
    // 搜索和过滤
    setSearchQuery,
    setActiveFilter,
    clearSearch,
    
    // 模态框控制
    openCreateModal,
    openEditModal,
    openCopyModal,
    openDeleteModal,
    closeAllModals,
    
    // 项目选择
    selectProject,
    clearSelection,
  };
}

// 导出类型
export type { UseProjectsUIReturn };