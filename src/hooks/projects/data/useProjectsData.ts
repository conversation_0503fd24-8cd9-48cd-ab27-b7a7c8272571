/**
 * 项目数据管理 Hook
 * 统一管理项目数据的获取、缓存、状态管理和错误处理
 */

import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { projectService } from "@/lib/api/services/projectService";
import { ProjectItem, Project } from "@/lib/api/types/project";
import { ProjectStatus } from "@/lib/api/types/common";
import { handleApiError } from "@/lib/api/base/errorHandler";
import { showSuccessToast } from "@/utils/toast";

// 过滤类型定义
export type ProjectFilterType = "all" | "recent";

// 排序类型定义
export type ProjectSortType = "lastModified" | "name" | "favoriteTime";

// Hook 状态接口
interface UseProjectsDataState {
  projects: ProjectItem[];
  filteredProjects: ProjectItem[];
  isLoading: boolean;
  error: string | null;
  lastFetch: Date | null;
}

// Hook 配置选项
interface UseProjectsDataOptions {
  autoFetch?: boolean; // 是否自动获取数据
  cacheTimeout?: number; // 缓存超时时间（毫秒）
  enableOptimisticUpdates?: boolean; // 是否启用乐观更新
  sortBy?: ProjectSortType; // 排序方式
  maxItems?: number; // 最大项目数量限制
}

// Hook 返回值接口
interface UseProjectsDataReturn extends UseProjectsDataState {
  // 数据操作方法
  fetchProjects: () => Promise<void>;
  refreshProjects: () => Promise<void>;

  // 项目 CRUD 操作
  createProject: (data: {
    name: string;
    description: string;
  }) => Promise<Project | null>;
  updateProject: (
    projectId: string,
    data: { name?: string; description?: string }
  ) => Promise<Project | null>;
  deleteProject: (projectId: string) => Promise<boolean>;
  toggleFavorite: (projectId: string, isFavorite: boolean) => Promise<boolean>;

  // 过滤和搜索
  applyFilter: (filter: ProjectFilterType, searchQuery?: string) => void;

  // 缓存管理
  clearCache: () => void;
  isDataStale: boolean;
}

/**
 * 项目数据管理 Hook
 *
 * ▶︎ **核心特性**:
 * - 统一的数据获取和缓存机制
 * - 乐观更新支持，提升用户体验
 * - 自动错误处理和用户提示
 * - 灵活的过滤和搜索功能
 * - 内存缓存和数据新鲜度检查
 *
 * ▶︎ **性能优化**:
 * - 使用 useMemo 缓存计算结果
 * - useCallback 防止不必要的重渲染
 * - 智能缓存策略，减少 API 调用
 *
 * @param options 配置选项
 * @returns 项目数据和操作方法
 */
export function useProjectsData(
  options: UseProjectsDataOptions = {}
): UseProjectsDataReturn {
  const {
    autoFetch = true,
    cacheTimeout = 5 * 60 * 1000, // 默认5分钟缓存
    enableOptimisticUpdates = true,
    sortBy = "lastModified",
    maxItems,
  } = options;

  // 状态管理
  const [state, setState] = useState<UseProjectsDataState>({
    projects: [],
    filteredProjects: [],
    isLoading: false,
    error: null,
    lastFetch: null,
  });

  // 防止重复调用的 ref
  const isRequestingRef = useRef(false);

  // 当前过滤条件
  const [currentFilter, setCurrentFilter] = useState<ProjectFilterType>("all");
  const [searchQuery, setSearchQuery] = useState("");

  // 检查数据是否过期
  const isDataStale = useMemo(() => {
    if (!state.lastFetch) return true;
    return Date.now() - state.lastFetch.getTime() > cacheTimeout;
  }, [state.lastFetch, cacheTimeout]);

  // 获取项目数据
  const fetchProjects = useCallback(async () => {
    console.log("fetchProjects called");

    // 防止重复调用
    if (isRequestingRef.current) {
      console.log("Request already in progress, skipping");
      return;
    }

    isRequestingRef.current = true;

    // 简化的状态检查和设置
    setState((prev) => {
      console.log("fetchProjects setState:", {
        prevIsLoading: prev.isLoading,
        prevError: prev.error,
      });

      return { ...prev, isLoading: true, error: null };
    });

    try {
      console.log("Starting API request...");
      // 直接从项目服务获取项目列表
      const projects = await projectService.getProjects();

      console.log("API request successful, projects:", projects.length);
      setState((prev) => ({
        ...prev,
        projects,
        isLoading: false,
        error: null, // 成功时清除错误状态
        lastFetch: new Date(),
      }));
    } catch (error) {
      console.log("fetchProjects error:", error);

      // 检查是否已经是处理过的 ApiError
      let errorMessage: string;
      if (error instanceof Error && error.name === "ApiError") {
        // 已经是处理过的 ApiError，直接使用其消息，不再显示 toast
        errorMessage = error.message;
        console.log("Error already processed by service layer");
      } else {
        // 原始错误，需要处理
        const apiError = handleApiError(error, {
          showToast: true,
          dedupeWindow: 5000,
        });
        errorMessage = apiError.message;
        console.log("Processing raw error");
      }

      console.log("Setting error state:", errorMessage);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
        lastFetch: new Date(), // 设置 lastFetch 防止立即重试
      }));
    } finally {
      // 重置请求标志
      isRequestingRef.current = false;
    }
  }, []); // 空依赖数组，避免函数重新创建

  // 强制刷新数据
  const refreshProjects = useCallback(async () => {
    setState((prev) => ({ ...prev, lastFetch: null }));
    await fetchProjects();
  }, [fetchProjects]);

  // 创建项目
  const createProject = useCallback(
    async (data: {
      name: string;
      description: string;
    }): Promise<Project | null> => {
      try {
        const newProject = await projectService.createProject({
          ...data,
          status: ProjectStatus.ARCHIVED, // 默认状态
        });

        // 乐观更新：立即添加到本地状态
        if (enableOptimisticUpdates) {
          const projectItem: ProjectItem = {
            ...newProject,
            is_favorite: false,
          };
          setState((prev) => ({
            ...prev,
            projects: [projectItem, ...prev.projects],
          }));
        }

        showSuccessToast("Project created successfully");

        // 刷新数据以确保同步
        if (!enableOptimisticUpdates) {
          await refreshProjects();
        }

        return newProject;
      } catch (error) {
        handleApiError(error, { showToast: true });
        return null;
      }
    },
    [enableOptimisticUpdates, refreshProjects]
  );

  // 更新项目
  const updateProject = useCallback(
    async (
      projectId: string,
      data: { name?: string; description?: string }
    ): Promise<Project | null> => {
      try {
        // 乐观更新
        if (enableOptimisticUpdates) {
          setState((prev) => ({
            ...prev,
            projects: prev.projects.map((p) =>
              p.id === projectId
                ? { ...p, ...data, last_edited_at: new Date().toISOString() }
                : p
            ),
          }));
        }

        const updatedProject = await projectService.updateProject(
          projectId,
          data
        );

        showSuccessToast("Project updated successfully");

        // 如果没有乐观更新，刷新数据
        if (!enableOptimisticUpdates) {
          await refreshProjects();
        }

        return updatedProject;
      } catch (error) {
        // 如果乐观更新失败，回滚状态
        if (enableOptimisticUpdates) {
          await refreshProjects();
        }

        handleApiError(error, { showToast: true });
        return null;
      }
    },
    [enableOptimisticUpdates, refreshProjects]
  );

  // 删除项目
  const deleteProject = useCallback(
    async (projectId: string): Promise<boolean> => {
      let originalProjects: ProjectItem[] = [];

      try {
        // 乐观更新：立即从本地状态移除
        if (enableOptimisticUpdates) {
          setState((prev) => {
            originalProjects = prev.projects;
            return {
              ...prev,
              projects: prev.projects.filter((p) => p.id !== projectId),
            };
          });
        }

        await projectService.deleteProject(projectId);
        showSuccessToast("Project deleted successfully");

        // 如果没有乐观更新，刷新数据
        if (!enableOptimisticUpdates) {
          await refreshProjects();
        }

        return true;
      } catch (error) {
        // 如果乐观更新失败，回滚状态
        if (enableOptimisticUpdates && originalProjects.length > 0) {
          setState((prev) => ({ ...prev, projects: originalProjects }));
        }

        handleApiError(error, { showToast: true });
        return false;
      }
    },
    [enableOptimisticUpdates, refreshProjects]
  );

  // 切换收藏状态 - 暂时注释掉，等待API支持个性化设置
  // const toggleFavorite = useCallback(
  //   async (projectId: string, isFavorite: boolean): Promise<boolean> => {
  //     try {
  //       // 乐观更新
  //       if (enableOptimisticUpdates) {
  //         setState((prev) => ({
  //           ...prev,
  //           projects: prev.projects.map((p) =>
  //             p.id === projectId ? { ...p, is_favorite: isFavorite } : p
  //           ),
  //         }));
  //       }

  //       await projectService.updatePersonalization(projectId, {
  //         is_favorite: isFavorite,
  //       });
  //       showSuccessToast(
  //         isFavorite ? "Added to favorites" : "Removed from favorites"
  //       );

  //       // 如果没有乐观更新，刷新数据
  //       if (!enableOptimisticUpdates) {
  //         await refreshProjects();
  //       }

  //       return true;
  //     } catch (error) {
  //       // 如果乐观更新失败，回滚状态
  //       if (enableOptimisticUpdates) {
  //         setState((prev) => ({
  //           ...prev,
  //           projects: prev.projects.map((p) =>
  //             p.id === projectId ? { ...p, is_favorite: !isFavorite } : p
  //           ),
  //         }));
  //       }

  //       handleApiError(error, { showToast: true });
  //       return false;
  //     }
  //   },
  //   [enableOptimisticUpdates, refreshProjects]
  // );

  // 临时的 toggleFavorite 函数，直接返回 false 表示功能暂不可用
  const toggleFavorite = useCallback(
    async (projectId: string, isFavorite: boolean): Promise<boolean> => {
      console.warn("toggleFavorite 功能暂时不可用，等待API支持");
      return false;
    },
    []
  );

  // 应用过滤和搜索
  const applyFilter = useCallback((filter: ProjectFilterType, search = "") => {
    setCurrentFilter(filter);
    setSearchQuery(search);
  }, []);

  // 清除缓存
  const clearCache = useCallback(() => {
    setState((prev) => ({ ...prev, lastFetch: null }));
  }, []);

  // 计算过滤后的项目列表
  const filteredProjects = useMemo(() => {
    let filtered = [...state.projects];

    // 应用过滤器
    switch (currentFilter) {
      case "recent":
        filtered = filtered
          .sort(
            (a, b) =>
              new Date(b.last_edited_at).getTime() -
              new Date(a.last_edited_at).getTime()
          )
          .slice(0, 5);
        break;
      case "all":
      default:
        // 'all' - 按创建时间排序（最新的在前）
        filtered = filtered.sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        break;
    }

    // 应用搜索
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (project) =>
          project.name.toLowerCase().includes(query) ||
          project.description.toLowerCase().includes(query)
      );
    }

    // 应用排序（如果指定了 sortBy）
    if (sortBy) {
      switch (sortBy) {
        case "lastModified":
          filtered = filtered.sort(
            (a, b) =>
              new Date(b.last_edited_at).getTime() -
              new Date(a.last_edited_at).getTime()
          );
          break;
        case "name":
          filtered = filtered.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case "favoriteTime":
          // 收藏的项目排在前面，然后按最后编辑时间排序
          filtered = filtered.sort((a, b) => {
            if (a.is_favorite && !b.is_favorite) return -1;
            if (!a.is_favorite && b.is_favorite) return 1;
            return (
              new Date(b.last_edited_at).getTime() -
              new Date(a.last_edited_at).getTime()
            );
          });
          break;
      }
    }

    // 应用数量限制
    if (maxItems && maxItems > 0) {
      filtered = filtered.slice(0, maxItems);
    }

    return filtered;
  }, [state.projects, currentFilter, searchQuery, sortBy, maxItems]);

  // 更新状态中的过滤结果
  useEffect(() => {
    setState((prev) => ({ ...prev, filteredProjects }));
  }, [filteredProjects]);

  // 自动获取数据
  useEffect(() => {
    console.log("useEffect triggered:", {
      autoFetch,
      hasError: !!state.error,
      error: state.error,
      projectsLength: state.projects.length,
      isDataStale,
      shouldFetch:
        autoFetch &&
        !state.error &&
        (state.projects.length === 0 || isDataStale),
    });

    if (
      autoFetch &&
      !state.error &&
      (state.projects.length === 0 || isDataStale)
    ) {
      console.log("Calling fetchProjects...");
      fetchProjects();
    }
  }, [
    autoFetch,
    fetchProjects,
    isDataStale,
    state.projects.length,
    state.error,
  ]);

  return {
    ...state,
    fetchProjects,
    refreshProjects,
    createProject,
    updateProject,
    deleteProject,
    toggleFavorite,
    applyFilter,
    clearCache,
    isDataStale,
  };
}

// 导出类型
export type { UseProjectsDataReturn, UseProjectsDataOptions };
