import { useState, useRef, useEffect } from "react";

interface UseMenuControlProps {
  /**
   * 菜单打开后的回调函数
   */
  onOpen?: () => void;
  /**
   * 菜单关闭后的回调函数
   */
  onClose?: () => void;
  /**
   * 鼠标悬停延迟打开的时间（毫秒）
   */
  hoverDelay?: number;
  /**
   * 鼠标离开延迟关闭的时间（毫秒）
   */
  leaveDelay?: number;
  /**
   * 菜单打开和关闭的最小间隔时间（毫秒）
   */
  toggleInterval?: number;
}

interface UseMenuControlReturn {
  /**
   * 菜单是否打开
   */
  isMenuOpen: boolean;
  /**
   * 菜单引用
   */
  menuRef: React.RefObject<HTMLDivElement>;
  /**
   * 触发器引用
   */
  triggerRef: React.RefObject<HTMLElement>;
  /**
   * 处理鼠标进入触发器区域
   */
  handleTriggerMouseEnter: () => void;
  /**
   * 处理鼠标离开触发器区域
   */
  handleTriggerMouseLeave: () => void;
  /**
   * 处理鼠标进入菜单区域
   */
  handleMenuMouseEnter: () => void;
  /**
   * 处理鼠标离开菜单区域
   */
  handleMenuMouseLeave: () => void;
  /**
   * 处理点击触发器
   */
  handleTriggerClick: () => void;
  /**
   * 处理菜单项点击
   */
  handleMenuItemClick: () => void;
}

/**
 * 菜单控制 Hook，用于处理菜单的显示、隐藏和交互逻辑
 */
export function useMenuControl({
  onOpen,
  onClose,
  hoverDelay = 300,
  leaveDelay = 300,
  toggleInterval = 500,
}: UseMenuControlProps = {}): UseMenuControlReturn {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [menuOpenTime, setMenuOpenTime] = useState<number>(0);
  const [menuCloseTime, setMenuCloseTime] = useState<number>(0);

  const menuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 检查菜单是否可以打开（关闭后至少指定时间）
  const canOpenMenu = () => {
    const timeSinceClose = Date.now() - menuCloseTime;
    return (
      !isMenuOpen && (menuCloseTime === 0 || timeSinceClose > toggleInterval)
    );
  };

  // 检查菜单是否可以关闭（打开后至少指定时间）
  const canCloseMenu = () => {
    const timeSinceOpen = Date.now() - menuOpenTime;
    return isMenuOpen && timeSinceOpen > toggleInterval;
  };

  // 处理鼠标进入触发器区域
  const handleTriggerMouseEnter = () => {
    // 注释掉 hover 自动打开菜单的逻辑
    /*
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // 只有当菜单可以打开时才添加延迟显示
    if (canOpenMenu()) {
      // 添加延迟显示菜单
      hoverTimeoutRef.current = setTimeout(() => {
        setIsMenuOpen(true);
        setMenuOpenTime(Date.now());
        if (onOpen) onOpen();
      }, hoverDelay);
    }
    */
  };

  // 处理鼠标离开触发器区域
  const handleTriggerMouseLeave = () => {
    // 注释掉 hover 离开自动关闭菜单的逻辑
    /*
    // 清除延迟显示的定时器
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    timeoutRef.current = setTimeout(() => {
      if (isMenuOpen) {
        setIsMenuOpen(false);
        setMenuCloseTime(Date.now());
        if (onClose) onClose();
      }
    }, leaveDelay);
    */
  };

  // 处理鼠标进入菜单区域
  const handleMenuMouseEnter = () => {
    // 注释掉 hover 相关逻辑
    /*
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    */
  };

  // 处理鼠标离开菜单区域
  const handleMenuMouseLeave = () => {
    // 注释掉 hover 离开自动关闭菜单的逻辑
    /*
    timeoutRef.current = setTimeout(() => {
      if (isMenuOpen) {
        setIsMenuOpen(false);
        setMenuCloseTime(Date.now());
        if (onClose) onClose();
      }
    }, leaveDelay);
    */
  };

  // 处理点击触发器
  const handleTriggerClick = () => {
    // 清除所有定时器
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    // 如果菜单未打开且可以打开，则打开菜单
    if (!isMenuOpen && canOpenMenu()) {
      setIsMenuOpen(true);
      setMenuOpenTime(Date.now());
      if (onOpen) onOpen();
    }
    // 如果菜单已打开且可以关闭，则关闭菜单
    else if (isMenuOpen && canCloseMenu()) {
      setIsMenuOpen(false);
      setMenuCloseTime(Date.now());
      if (onClose) onClose();
    }
  };

  // 处理菜单项点击
  const handleMenuItemClick = () => {
    setIsMenuOpen(false);
    setMenuCloseTime(Date.now());
    if (onClose) onClose();
  };

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        triggerRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        if (isMenuOpen) {
          setIsMenuOpen(false);
          setMenuCloseTime(Date.now());
          if (onClose) onClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMenuOpen, onClose]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  return {
    isMenuOpen,
    menuRef,
    triggerRef,
    handleTriggerMouseEnter,
    handleTriggerMouseLeave,
    handleMenuMouseEnter,
    handleMenuMouseLeave,
    handleTriggerClick,
    handleMenuItemClick,
  };
}
