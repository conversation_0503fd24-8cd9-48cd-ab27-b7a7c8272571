"use client";

import * as React from "react";
import { Editor } from "@tiptap/react";
import {
  useFloating,
  autoUpdate,
  offset,
  shift,
  flip,
  inline,
  limitShift,
} from "@floating-ui/react";

interface TextSelection {
  /**
   * 是否有文本被选中
   */
  hasSelection: boolean;
  /**
   * 选中的文本内容
   */
  selectedText: string;
  /**
   * 选中区域的位置坐标（仅作为备用）
   */
  position: {
    x: number;
    y: number;
  };
  /**
   * 是否在可见区域内（用于边界剪裁）
   */
  isVisible: boolean;
  /**
   * Floating UI 提供的引用对象
   */
  refs: {
    setReference: (node: Element | null) => void;
    setFloating: (node: HTMLElement | null) => void;
  };
  /**
   * Floating UI 提供的样式
   */
  floatingStyles: React.CSSProperties;
}

/**
 * 文本选择检测 Hook
 * 监听编辑器中的文本选择状态，返回选中文本和位置信息
 * 使用 Floating UI 实现精确定位和跟随
 */
export const useTextSelection = (editor: Editor | null): TextSelection => {
  // 创建一个 DOM 元素作为虚拟引用点
  const [referenceElement, setReferenceElement] =
    React.useState<HTMLElement | null>(null);

  // 基本状态
  const [selection, setSelection] = React.useState({
    hasSelection: false,
    selectedText: "",
    position: { x: 0, y: 0 },
    isVisible: false,
  });

  // 存储当前选择的 from 和 to 位置
  const selectionPosRef = React.useRef<{
    from: number;
    to: number;
  } | null>(null);

  // 获取编辑器内容区域的引用
  const editorContentRef = React.useRef<HTMLElement | null>(null);

  // 获取当前缩放比例
  const getEditorScale = React.useCallback((): number => {
    if (editorContentRef.current) {
      // 尝试获取编辑器缩放容器
      const scaleContainer = editorContentRef.current.querySelector(
        '[style*="transform"]'
      );
      if (scaleContainer) {
        const transform = window.getComputedStyle(scaleContainer).transform;
        if (transform && transform !== "none") {
          // 从transform矩阵中提取缩放值
          const matrix = transform.match(/matrix\(([^)]+)\)/);
          if (matrix && matrix[1]) {
            const values = matrix[1].split(",");
            // 使用a值作为缩放比例 (matrix结构: a,b,c,d,tx,ty)
            if (values.length >= 1) {
              return parseFloat(values[0]);
            }
          }
        }
      }
    }
    // 如果无法获取，返回默认值1
    return 1;
  }, []);

  // 添加下边界冗余区域的大小（像素）
  const BOTTOM_BUFFER = 20;

  // 跟踪鼠标按下状态
  const isMouseDownRef = React.useRef(false);

  // 初始化虚拟引用元素
  React.useEffect(() => {
    if (!referenceElement) {
      // 创建一个真实的 DOM 元素作为引用点
      const virtualEl = document.createElement("div");
      virtualEl.style.position = "absolute";
      virtualEl.style.width = "0";
      virtualEl.style.height = "0";
      virtualEl.style.pointerEvents = "none";
      virtualEl.style.opacity = "0";
      document.body.appendChild(virtualEl);
      setReferenceElement(virtualEl);

      // 清理函数
      return () => {
        // 添加安全检查，确保元素存在于 document.body 中
        if (document.body.contains(virtualEl)) {
          document.body.removeChild(virtualEl);
        }
      };
    }
  }, []);

  // 使用 Floating UI
  const { refs, floatingStyles, update } = useFloating({
    // 设置元素出现在选中文本上方
    middleware: [
      offset({ mainAxis: -50, crossAxis: -82 }), // 上方偏移 50px
      inline(), // 处理内联元素
      flip({
        fallbackAxisSideDirection: "start", // 如果空间不足，优先翻转到起始位置
        crossAxis: false, // 禁用交叉轴翻转
      }),
      shift({
        limiter: limitShift(), // 限制移位
        padding: { top: 60, bottom: 40, left: 20, right: 20 }, // 添加内边距，确保不会太靠近边缘
      }),
    ],
    // 使用 autoUpdate 确保位置实时更新
    whileElementsMounted: autoUpdate,
  });

  // 更新虚拟元素位置，考虑缩放因素
  const updateVirtualElementPosition = React.useCallback(() => {
    if (!editor || !selectionPosRef.current || !referenceElement) {
      return;
    }

    try {
      const { from, to } = selectionPosRef.current;
      const { view } = editor;

      // 获取编辑器当前缩放比例
      const scale = getEditorScale();

      // 获取选区的视觉边界，而不是逻辑位置
      // 这样可以避免行尾选择问题
      const domSelection = window.getSelection();

      if (domSelection && domSelection.rangeCount > 0) {
        // 使用DOM选区获取实际的视觉边界
        const range = domSelection.getRangeAt(0);
        const rects = range.getClientRects();

        if (rects.length > 0) {
          // 找出所有选区矩形中最右侧的位置
          let rightMost = 0;
          let topMost = Number.MAX_SAFE_INTEGER;

          // 遍历所有矩形，找出最右侧的位置和最上方的位置
          for (let i = 0; i < rects.length; i++) {
            const rect = rects[i];
            rightMost = Math.max(rightMost, rect.right);
            topMost = Math.min(topMost, rect.top);
          }

          // 计算位置：X轴使用最右侧位置，Y轴使用最上方位置
          const positionX = rightMost;
          const positionY = topMost;

          // 获取编辑器内容区域
          if (!editorContentRef.current) {
            editorContentRef.current =
              view.dom.closest(".overflow-auto") ||
              view.dom.closest(".flex-1") ||
              view.dom.parentElement;
          }

          // 确保编辑器内容区域存在
          if (!editorContentRef.current) {
            console.warn("Editor content container not found");
            return;
          }

          // 获取内容区域的边界
          const contentRect = editorContentRef.current.getBoundingClientRect();

          // 检查选中区域是否在可见边界内，为下边界添加额外冗余
          const isVisible =
            positionX >= contentRect.left &&
            positionX <= contentRect.right &&
            positionY >= contentRect.top &&
            // 添加下边界冗余，使 actionbar 真正移出后才消失
            positionY <= contentRect.bottom + BOTTOM_BUFFER;

          // 如果不可见且之前也不可见，则不更新位置
          if (!isVisible && !selection.isVisible) {
            return;
          }

          // 更新虚拟 DOM 元素的位置
          if (referenceElement) {
            // 使用transform3d触发GPU加速，减少重排
            const newTransform = `translate3d(${positionX}px, ${positionY}px, 0)`;
            if (referenceElement.style.transform !== newTransform) {
              referenceElement.style.transform = newTransform;
            }

            // 设置引用元素
            refs.setReference(referenceElement);

            // 手动触发位置更新
            update();
          }

          // 更新基本状态中的位置信息（作为备用）
          // 只有当鼠标释放后才显示 actionbar
          setSelection((prev) => {
            // 如果位置和可见性没有变化，则不更新状态
            if (
              prev.position.x === positionX &&
              prev.position.y === positionY &&
              prev.isVisible === (isVisible && !isMouseDownRef.current)
            ) {
              return prev;
            }

            return {
              ...prev,
              position: { x: positionX, y: positionY },
              isVisible: isVisible && !isMouseDownRef.current,
            };
          });

          return;
        }
      }

      // 如果无法获取DOM选区，回退到原始方法
      const start = view.coordsAtPos(from);
      const end = view.coordsAtPos(to);

      // 计算位置：X轴使用最右侧位置，Y轴使用最上方位置
      const positionX = Math.max(start.right, end.right);
      const positionY = Math.min(start.top, end.top);

      // 获取编辑器内容区域
      if (!editorContentRef.current) {
        editorContentRef.current =
          view.dom.closest(".overflow-auto") ||
          view.dom.closest(".flex-1") ||
          view.dom.parentElement;
      }

      // 确保编辑器内容区域存在
      if (!editorContentRef.current) {
        console.warn("Editor content container not found");
        return;
      }

      // 获取内容区域的边界
      const contentRect = editorContentRef.current.getBoundingClientRect();

      // 检查选中区域是否在可见边界内，为下边界添加额外冗余
      const isVisible =
        positionX >= contentRect.left &&
        positionX <= contentRect.right &&
        positionY >= contentRect.top &&
        // 添加下边界冗余，使 actionbar 真正移出后才消失
        positionY <= contentRect.bottom + BOTTOM_BUFFER;

      // 如果不可见且之前也不可见，则不更新位置
      if (!isVisible && !selection.isVisible) {
        return;
      }

      // 更新虚拟 DOM 元素的位置
      if (referenceElement) {
        // 使用transform3d触发GPU加速，减少重排
        const newTransform = `translate3d(${positionX}px, ${positionY}px, 0)`;
        if (referenceElement.style.transform !== newTransform) {
          referenceElement.style.transform = newTransform;
        }

        // 设置引用元素
        refs.setReference(referenceElement);

        // 手动触发位置更新
        update();
      }

      // 更新基本状态中的位置信息（作为备用）
      // 只有当鼠标释放后才显示 actionbar
      setSelection((prev) => {
        // 如果位置和可见性没有变化，则不更新状态
        if (
          prev.position.x === positionX &&
          prev.position.y === positionY &&
          prev.isVisible === (isVisible && !isMouseDownRef.current)
        ) {
          return prev;
        }

        return {
          ...prev,
          position: { x: positionX, y: positionY },
          isVisible: isVisible && !isMouseDownRef.current,
        };
      });
    } catch (error) {
      console.error("Failed to update position:", error);
    }
  }, [
    editor,
    update,
    refs,
    referenceElement,
    getEditorScale,
    selection.isVisible,
  ]);

  // 监听选择变化
  React.useEffect(() => {
    if (!editor) return;

    // 获取编辑器内容区域
    editorContentRef.current =
      editor.view.dom.closest(".overflow-auto") ||
      editor.view.dom.closest(".flex-1") ||
      editor.view.dom.parentElement;

    // 监听缩放变化
    const scaleObserver = new MutationObserver(() => {
      // 只有在有选择且QuickActionBar可见时才更新位置
      if (selectionPosRef.current && selection.isVisible) {
        // 使用requestAnimationFrame减少不必要的重绘
        requestAnimationFrame(() => {
          updateVirtualElementPosition();
        });
      }
    });

    // 找到可能的缩放容器
    const scaleContainer = editorContentRef.current?.querySelector(
      '[style*="transform"]'
    );
    if (scaleContainer) {
      scaleObserver.observe(scaleContainer, {
        attributes: true,
        attributeFilter: ["style"],
      });
    }

    const handleSelectionChange = () => {
      const { state } = editor;
      const { from, to } = state.selection;
      const hasSelection = from !== to;

      if (!hasSelection) {
        selectionPosRef.current = null;
        setSelection({
          hasSelection: false,
          selectedText: "",
          position: { x: 0, y: 0 },
          isVisible: false,
        });
        return;
      }

      // 获取选中文本
      const selectedText = state.doc.textBetween(from, to, " ");

      // 如果选中的文本为空或只有空格，不显示操作栏
      if (!selectedText.trim()) {
        selectionPosRef.current = null;
        setSelection({
          hasSelection: false,
          selectedText: "",
          position: { x: 0, y: 0 },
          isVisible: false,
        });
        return;
      }

      // 存储选择位置
      selectionPosRef.current = { from, to };

      // 更新选中状态
      setSelection((prev) => ({
        ...prev,
        hasSelection: true,
        selectedText,
        // 只有当鼠标释放后才显示 actionbar
        isVisible: !isMouseDownRef.current && prev.isVisible,
      }));

      // 更新位置
      updateVirtualElementPosition();
    };

    // 监听选择和更新事件
    editor.on("selectionUpdate", handleSelectionChange);
    editor.on("update", handleSelectionChange);

    // 监听滚动事件
    const handleScroll = () => {
      if (selectionPosRef.current) {
        updateVirtualElementPosition();
      }
    };

    // 获取可能的滚动容器
    const editorContainer = editorContentRef.current;

    if (editorContainer) {
      editorContainer.addEventListener("scroll", handleScroll, {
        passive: true,
      });
    }

    // 监听全局滚动
    window.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener("resize", handleScroll, { passive: true });

    // 清理
    return () => {
      editor.off("selectionUpdate", handleSelectionChange);
      editor.off("update", handleSelectionChange);

      if (editorContainer) {
        editorContainer.removeEventListener("scroll", handleScroll);
      }
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);

      // 清理缩放观察器
      scaleObserver.disconnect();
    };
  }, [editor, updateVirtualElementPosition]);

  // 监听鼠标按下和释放事件
  React.useEffect(() => {
    if (!editor) return;

    const editorDom = editor.view.dom;

    const handleMouseDown = () => {
      isMouseDownRef.current = true;
      // 鼠标按下时隐藏 actionbar
      setSelection((prev) => ({
        ...prev,
        isVisible: false,
      }));
    };

    const handleMouseUp = () => {
      isMouseDownRef.current = false;
      // 鼠标释放后，如果有选中文本，则显示 actionbar
      if (selectionPosRef.current) {
        setTimeout(() => {
          updateVirtualElementPosition();
        }, 0);
      }
    };

    // 为编辑器添加鼠标事件监听
    editorDom.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      editorDom.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [editor, updateVirtualElementPosition]);

  // 监听全局点击，当点击编辑器外部时清除选择
  React.useEffect(() => {
    if (!editor) return;

    const handleGlobalClick = (event: MouseEvent) => {
      const editorElement = editor.view.dom;
      if (!editorElement.contains(event.target as Node)) {
        selectionPosRef.current = null;
        setSelection({
          hasSelection: false,
          selectedText: "",
          position: { x: 0, y: 0 },
          isVisible: false,
        });
      }
    };

    document.addEventListener("click", handleGlobalClick);
    return () => {
      document.removeEventListener("click", handleGlobalClick);
    };
  }, [editor]);

  // 返回完整的状态对象
  return {
    ...selection,
    refs,
    floatingStyles,
  };
};
