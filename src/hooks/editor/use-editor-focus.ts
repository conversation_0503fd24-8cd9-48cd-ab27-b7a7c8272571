"use client";

import { useState, useEffect } from "react";
import type { Editor } from "@tiptap/react";

/**
 * 自定义钩子，用于检测编辑器的焦点状态和键盘显示状态
 * @param editor TipTap编辑器实例
 * @returns 包含编辑器焦点状态的对象
 */
export function useEditorFocus(editor: Editor | null) {
  const [isFocused, setIsFocused] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测设备是否为移动端
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 540);
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, []);

  // 监听编辑器焦点变化
  useEffect(() => {
    if (!editor) return;

    const onFocus = () => {
      setIsFocused(true);
    };

    const onBlur = () => {
      setIsFocused(false);
      // 在失去焦点时，延迟一点时间再设置isEditing为false
      // 这是为了防止工具栏在点击时因失去焦点而消失
      setTimeout(() => {
        setIsEditing(false);
      }, 300);
    };

    const onSelectionUpdate = ({ editor }: { editor: Editor }) => {
      const hasSelection = !editor.state.selection.empty;
      setIsEditing(hasSelection || editor.isFocused);
    };

    editor.on("focus", onFocus);
    editor.on("blur", onBlur);
    editor.on("selectionUpdate", onSelectionUpdate);

    return () => {
      editor.off("focus", onFocus);
      editor.off("blur", onBlur);
      editor.off("selectionUpdate", onSelectionUpdate);
    };
  }, [editor]);

  return {
    isFocused,
    isEditing,
    isMobile,
    // 只有在移动端且正在编辑时才显示工具栏
    shouldShowMobileToolbar: isMobile && isEditing,
  };
}
