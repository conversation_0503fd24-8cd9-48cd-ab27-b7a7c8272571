"use client";

import { useState, useEffect, useCallback } from "react";
import { Editor } from "@tiptap/react";
import { create } from "zustand";

// 创建一个全局状态存储
interface FontFamilyState {
  isSerif: boolean;
  setIsSerif: (value: boolean) => void;
}

const useFontFamilyStore = create<FontFamilyState>((set) => ({
  isSerif: false,
  setIsSerif: (value) => set({ isSerif: value }),
}));

export const useFontFamily = (editor: Editor | null) => {
  // 使用全局状态
  const { isSerif, setIsSerif } = useFontFamilyStore();

  // 初始化和更新全局状态
  useEffect(() => {
    if (!editor) return;

    // 检查编辑器中当前使用的字体
    const currentFont = editor.getAttributes("textStyle").fontFamily || "";
    const isSerifFont = currentFont.includes("Serif");

    if (isSerifFont !== isSerif) {
      setIsSerif(isSerifFont);
    }
  }, [editor, isSerif, setIsSerif]);

  // 应用字体到所有内容的帮助函数
  const applyFontToAllContent = useCallback(
    (fontFamily: string) => {
      if (!editor) return;

      const currentSelection = editor.state.selection;
      editor.commands.selectAll();
      editor.commands.setFontFamily(fontFamily);
      editor.commands.setTextSelection(currentSelection);
    },
    [editor]
  );

  // 监听粘贴事件
  useEffect(() => {
    if (!editor) return;

    const handlePaste = () => {
      // 延迟处理，确保内容已插入
      setTimeout(() => {
        const fontFamily = isSerif ? "Noto Serif SC" : "Noto Sans SC";
        applyFontToAllContent(fontFamily);
      }, 10);
    };

    editor.on("paste", handlePaste);

    return () => {
      editor.off("paste", handlePaste);
    };
  }, [editor, isSerif, applyFontToAllContent]);

  // 字体切换函数
  const toggleFontFamily = useCallback(() => {
    if (!editor) return;

    const newIsSerif = !isSerif;
    const newFontFamily = newIsSerif ? "Noto Serif SC" : "Noto Sans SC";

    setIsSerif(newIsSerif);
    applyFontToAllContent(newFontFamily);
  }, [editor, isSerif, setIsSerif, applyFontToAllContent]);

  return {
    isSerif,
    toggleFontFamily,
  };
};
