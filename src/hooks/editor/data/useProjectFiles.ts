/**
 * 项目文件树数据 Hook
 * 用于获取和管理项目文件树数据
 */

import { useState, useEffect, useCallback } from "react";
import { projectService } from "@/lib/api/services/projectService";
import { ProjectTree } from "@/lib/api/types";
import { showErrorToast } from "@/utils/toast";

/**
 * Hook 返回值接口
 */
export interface UseProjectFilesReturn {
  files: ProjectTree[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * 项目文件树数据 Hook
 * @param projectId 项目ID（必需，用于获取指定项目的文件树）
 */
export const useProjectFiles = (projectId: string): UseProjectFilesReturn => {
  const [files, setFiles] = useState<ProjectTree[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取项目文件树数据
   */
  const fetchProjectFiles = useCallback(async () => {
    // 如果没有提供项目ID，则无法获取文件树
    if (!projectId) {
      setError("Project ID is required to fetch project files");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const projectFiles: ProjectTree[] = await projectService.getProjectTree(
        projectId
      );

      // 直接返回文件数组，让 FileList 组件处理树形结构构建
      setFiles(projectFiles);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch project files";
      setError(errorMessage);
      showErrorToast(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  /**
   * 重新获取数据
   */
  const refetch = useCallback(async () => {
    await fetchProjectFiles();
  }, [fetchProjectFiles]);

  // 组件挂载时获取数据
  useEffect(() => {
    fetchProjectFiles();
  }, [fetchProjectFiles]);

  return {
    files,
    isLoading,
    error,
    refetch,
  };
};

/**
 * 默认导出
 */
export default useProjectFiles;
