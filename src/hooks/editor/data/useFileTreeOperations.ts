import React from "react";
import { FileItem } from "../../../components/sidebar/components/filelist";
import { Region } from "../../../lib/api/types/common";
import { docService } from "../../../lib/api/services/docService";
import { showSuccessToast, showErrorToast } from "../../../utils/toast";
import { collectAllFiles, collectAllSubFolders } from "../../../utils/editor";
import {
  deleteVirtualFolder,
  collectVirtualFoldersInFolder,
  createVirtualFolder,
  cleanupVirtualFoldersOnFileCreate,
  createVirtualFoldersOnFileDelete,
} from "../../../utils/virtualFolders";

/**
 * 文件树操作 Hook
 * 提供统一的文件和文件夹 CRUD 操作
 */
export const useFileTreeOperations = (
  projectId: string,
  onRefresh?: () => Promise<void>
) => {
  /**
   * 创建文件
   */
  const createFile = React.useCallback(
    async (fileName: string, region: Region, parentPath?: string) => {
      if (!projectId) throw new Error("Project ID is required");

      try {
        const fullPath = parentPath ? `${parentPath}/${fileName}` : fileName;
        const normalizedPath = fullPath.startsWith("/")
          ? fullPath.substring(1)
          : fullPath;

        await docService.createDoc({
          project_id: projectId,
          region: region,
          doc_name: normalizedPath,
          content: "",
        });

        // 清理虚拟文件夹
        if (parentPath) {
          cleanupVirtualFoldersOnFileCreate(projectId, parentPath);
        }

        showSuccessToast(`File "${fileName}" created successfully`);
        await onRefresh?.();
      } catch (error) {
        const message =
          error instanceof Error ? error.message : "Failed to create file";
        showErrorToast(message);
        throw error;
      }
    },
    [projectId, onRefresh]
  );

  /**
   * 创建文件夹
   */
  const createFolder = React.useCallback(
    async (folderName: string, region: Region, parentPath?: string) => {
      if (!projectId) throw new Error("Project ID is required");

      try {
        const fullPath = parentPath
          ? `${parentPath}/${folderName}`
          : `/${folderName}`;

        createVirtualFolder(projectId, fullPath, region);
        showSuccessToast(`Folder "${folderName}" created successfully`);
        await onRefresh?.();
      } catch (error) {
        const message =
          error instanceof Error ? error.message : "Failed to create folder";
        showErrorToast(message);
        throw error;
      }
    },
    [projectId, onRefresh]
  );

  /**
   * 删除项目（文件或文件夹）
   */
  const deleteItem = React.useCallback(
    async (target: FileItem, region: Region) => {
      if (!projectId) throw new Error("Project ID is required");

      try {
        if (target.type === "folder") {
          await deleteFolderRecursively(target, region);
        } else {
          // 删除单个文件
          await docService.deleteDoc(target.id!);

          // 创建虚拟文件夹以保留文件夹结构
          if (target.path && target.path.includes("/")) {
            createVirtualFoldersOnFileDelete(projectId, target.path, region);
          }

          showSuccessToast(`File "${target.name}" deleted successfully`);
        }

        await onRefresh?.();
      } catch (error) {
        const message =
          error instanceof Error ? error.message : "Failed to delete item";
        showErrorToast(message);
        throw error;
      }
    },
    [projectId, onRefresh]
  );

  /**
   * 递归删除文件夹及其所有内容
   */
  const deleteFolderRecursively = React.useCallback(
    async (folder: FileItem, region: Region) => {
      console.log("🗑️ deleteFolderRecursively called:", folder);

      // 收集所有需要删除的内容
      const allFiles = collectAllFiles(folder);
      const userVirtualFolders = collectVirtualFoldersInFolder(
        projectId,
        folder.path!,
        region
      );

      let deletedCount = 0;
      const totalItems = allFiles.length + userVirtualFolders.length;

      console.log("📊 Delete analysis:", {
        allFiles: allFiles.length,
        userVirtualFolders: userVirtualFolders.length,
        totalItems,
        isVirtual: folder.isVirtual,
      });

      try {
        // 删除所有真实文件
        if (allFiles.length > 0) {
          console.log(`🗑️ Deleting ${allFiles.length} real files...`);
          const deletePromises = allFiles.map(async (file) => {
            await docService.deleteDoc(file.id!);
            deletedCount++;
            return file;
          });

          await Promise.all(deletePromises);
          console.log(`✅ Deleted ${allFiles.length} real files`);
        }

        // 删除所有用户创建的虚拟文件夹
        if (userVirtualFolders.length > 0) {
          console.log(
            `🗑️ Deleting ${userVirtualFolders.length} virtual folders...`
          );
          userVirtualFolders.forEach((virtualFolder) => {
            deleteVirtualFolder(projectId, virtualFolder.path);
            deletedCount++;
          });
          console.log(
            `✅ Deleted ${userVirtualFolders.length} virtual folders`
          );
        }

        // 如果目标文件夹本身是虚拟的，也要删除它
        if (folder.isVirtual) {
          console.log(`🗑️ Deleting target virtual folder: ${folder.path}`);
          deleteVirtualFolder(projectId, folder.path!);
          deletedCount++;
          console.log(`✅ Deleted target virtual folder`);
        }

        const successMessage =
          totalItems > 0
            ? `Folder "${folder.name}" and ${totalItems} item${
                totalItems > 1 ? "s" : ""
              } deleted successfully`
            : `Folder "${folder.name}" deleted successfully`;

        showSuccessToast(successMessage);
      } catch (error) {
        const errorMessage = `Failed to delete folder. ${deletedCount} of ${totalItems} items were deleted.`;
        showErrorToast(errorMessage);
        throw error;
      }
    },
    [projectId]
  );

  return {
    createFile,
    createFolder,
    deleteItem,
  };
};
