import React from "react";
import { Region } from "../../../lib/api/types/common";
import {
  getVirtualFolders,
  createVirtualFolder,
  deleteVirtualFolder,
  VirtualFolder,
} from "../../../utils/virtualFolders";

/**
 * 虚拟文件夹管理 Hook
 * 负责虚拟文件夹的生命周期管理
 */
export const useVirtualFolderManager = (projectId: string, region: Region) => {
  // 获取当前 region 的虚拟文件夹
  const virtualFolders = React.useMemo(() => {
    if (!projectId) return [];

    const allVirtualFolders = getVirtualFolders(projectId);
    return allVirtualFolders.filter((folder) => folder.region === region);
  }, [projectId, region]);

  /**
   * 创建虚拟文件夹
   */
  const createVirtualFolderInRegion = React.useCallback(
    (folderPath: string) => {
      if (!projectId) throw new Error("Project ID is required");

      createVirtualFolder(projectId, folderPath, region);
    },
    [projectId, region]
  );

  /**
   * 删除虚拟文件夹
   */
  const deleteVirtualFolderInRegion = React.useCallback(
    (folderPath: string) => {
      if (!projectId) throw new Error("Project ID is required");

      deleteVirtualFolder(projectId, folderPath);
    },
    [projectId]
  );

  /**
   * 检查虚拟文件夹是否存在
   */
  const hasVirtualFolder = React.useCallback(
    (folderPath: string): boolean => {
      return virtualFolders.some((folder) => folder.path === folderPath);
    },
    [virtualFolders]
  );

  /**
   * 获取指定路径下的虚拟子文件夹
   */
  const getVirtualSubFolders = React.useCallback(
    (parentPath: string): VirtualFolder[] => {
      return virtualFolders.filter(
        (folder) =>
          folder.path.startsWith(parentPath + "/") && folder.path !== parentPath
      );
    },
    [virtualFolders]
  );

  return {
    virtualFolders,
    createVirtualFolderInRegion,
    deleteVirtualFolderInRegion,
    hasVirtualFolder,
    getVirtualSubFolders,
  };
};
