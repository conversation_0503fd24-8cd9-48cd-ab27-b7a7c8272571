import React from "react";
import { Region } from "../../../lib/api/types/common";
import {
  FileItem,
  buildFileTree,
} from "../../../components/sidebar/components/filelist";
import { useProjectFiles } from "./useProjectFiles";

/**
 * 项目文件树管理 Hook
 * 负责构建和管理当前 region 的文件树
 */
export const useProjectFileTree = (projectId: string, region: Region) => {
  // 获取原始文件数据
  const { files, isLoading, error, refetch } = useProjectFiles(projectId || "");

  // 构建当前 region 的文件树
  const fileTree = React.useMemo(() => {
    if (!projectId || !files) return [];

    // 过滤当前 region 的文件
    const filteredFiles = files.filter((file) => file.region === region);

    // 构建文件树
    return buildFileTree(filteredFiles, projectId, region);
  }, [files, projectId, region]);

  // 刷新文件树
  const refreshFileTree = React.useCallback(async () => {
    await refetch();
  }, [refetch]);

  return {
    fileTree,
    isLoading,
    error,
    refreshFileTree,
    // 原始数据（用于某些特殊场景）
    rawFiles: files,
  };
};
