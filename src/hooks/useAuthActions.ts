import { useAuth } from "@quote/auth-client/react";
import { showSuccessToast, showErrorToast } from "@/utils/toast";

/**
 * 统一处理认证相关的操作，包括登录和登出
 * 确保整个应用中认证逻辑保持一致
 */
export function useAuthActions() {
  const {
    isAuthenticated,
    user,
    loading,
    error,
    statusCode,
    login,
    logout,
    checkSSORedirect,
    verifyStatus,
  } = useAuth();

  /**
   * 处理用户登录
   * 统一的登录流程：调用认证SDK的login方法，成功后刷新页面
   */
  const handleLogin = async () => {
    try {
      console.log("开始登录...");
      await login();

      // 登录成功后刷新页面以确保状态更新
      setTimeout(() => {
        console.log("登录成功，刷新页面以更新状态");
        showSuccessToast("登录成功");
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("登录失败:", error);
      showErrorToast("登录失败，请检查网络连接或联系管理员");
    }
  };

  /**
   * 处理用户登出
   * 统一的登出流程：调用认证SDK的logout方法，会自动清除localStorage中的用户信息
   */
  const handleLogout = async () => {
    try {
      console.log("开始退出登录...");
      await logout();

      showSuccessToast("已成功退出登录");
    } catch (error) {
      console.error("退出登录失败:", error);
      showErrorToast("退出登录失败，请稍后再试");
    }
  };

  /**
   * 获取认证状态的文本描述
   * 基于状态码返回对应的状态描述
   */
  const getAuthStatusMessage = () => {
    if (!statusCode) return "";

    switch (statusCode) {
      case 200:
        return "用户认证有效";
      case 401:
        return "登录已过期，请重新登录";
      case 500:
        return "认证服务异常";
      default:
        return "未知认证状态";
    }
  };

  /**
   * 检查登录是否已过期
   * 当状态码为401时表示登录已过期
   */
  const isLoginExpired = () => {
    return statusCode === 401;
  };

  return {
    isAuthenticated,
    user,
    loading,
    error,
    statusCode,
    handleLogin,
    handleLogout,
    getAuthStatusMessage,
    isLoginExpired,
    // 新增 SSO 重定向相关功能
    checkSSORedirect,
    verifyStatus,
  };
}
