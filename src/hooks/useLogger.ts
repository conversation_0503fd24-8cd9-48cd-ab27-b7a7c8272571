/**
 * useLogger Hook
 * 在React组件中方便地使用日志工具
 */
import { useMemo } from "react";
import logger from "../utils/logger";

/**
 * 日志Hook，为组件提供专属的日志记录器
 * @param componentName 组件名称，将作为日志前缀
 * @returns 日志记录器对象
 */
export function useLogger(componentName: string) {
  const componentLogger = useMemo(() => {
    return logger.createPrefixed(componentName);
  }, [componentName]);

  return componentLogger;
}

export default useLogger;
