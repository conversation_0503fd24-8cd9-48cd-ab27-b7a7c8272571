import { useState, useEffect, useCallback, useRef } from "react";
import { UseResizablePanelReturn } from "./useResizablePanel";

interface UseCollapsiblePanelProps {
  /**
   * 来自 useResizablePanel 的返回值
   */
  resizablePanel: UseResizablePanelReturn;
  /**
   * 默认宽度，用于首次展开时
   */
  defaultWidth: number;
  /**
   * 折叠状态存储的键名
   */
  storageKeyCollapsed: string;
  /**
   * 折叠前宽度存储的键名
   */
  storageKeyWidthBeforeCollapse: string;
  /**
   * 动画持续时间(ms)
   */
  animationDuration?: number;
  /**
   * 折叠状态下的最小宽度，默认为0
   */
  minCollapsedWidth?: number;
  /**
   * 内容淡入淡出的持续时间(ms)，默认为150
   */
  fadeAnimationDuration?: number;
}

interface UseCollapsiblePanelReturn {
  /**
   * 面板是否折叠
   */
  isCollapsed: boolean;
  /**
   * 面板是否正在动画过程中
   */
  isAnimating: boolean;
  /**
   * 切换面板折叠/展开状态
   */
  toggleCollapse: () => void;
  /**
   * 内容是否应该可见
   */
  isContentVisible: boolean;
}

/**
 * 异步保存数据到localStorage，避免阻塞主线程
 */
const saveToLocalStorage = (key: string, value: string) => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(key, value);
  } catch (e) {
    console.error("Failed to save to localStorage:", e);
  }
};

/**
 * 可折叠面板的Hook，管理面板的折叠/展开状态和动画
 */
export default function useCollapsiblePanel({
  resizablePanel,
  defaultWidth,
  storageKeyCollapsed,
  storageKeyWidthBeforeCollapse,
  animationDuration = 300,
  minCollapsedWidth = 0,
  fadeAnimationDuration = 150,
}: UseCollapsiblePanelProps): UseCollapsiblePanelReturn {
  // 折叠状态
  const [isCollapsed, setIsCollapsed] = useState(false);
  // 记录折叠前的宽度，用于恢复展开状态
  const [widthBeforeCollapse, setWidthBeforeCollapse] =
    useState<number>(defaultWidth);
  // 动画状态
  const [isAnimating, setIsAnimating] = useState(false);
  // 内容可见状态（用于控制淡入淡出）
  const [isContentVisible, setIsContentVisible] = useState(true);
  // 是否已完成初始化
  const hasInitialized = useRef(false);

  // 从本地存储加载折叠状态 - 只在组件挂载时执行一次
  useEffect(() => {
    if (typeof window === "undefined" || hasInitialized.current) return;

    try {
      // 1. 加载折叠状态
      const isInitiallyCollapsed =
        localStorage.getItem(storageKeyCollapsed) === "true";

      // 2. 加载折叠前的宽度（如果有）
      const savedWidthStr = localStorage.getItem(storageKeyWidthBeforeCollapse);
      const savedWidth = savedWidthStr
        ? parseInt(savedWidthStr, 10)
        : defaultWidth;

      // 3. 设置初始状态
      if (isInitiallyCollapsed) {
        setIsCollapsed(true);
        setIsContentVisible(false);
        setWidthBeforeCollapse(savedWidth); // 保存折叠前的宽度
        resizablePanel.setWidth(minCollapsedWidth);
      } else {
        // 如果不是折叠状态，宽度由 useResizablePanel 自己管理
        // resizablePanel 已经在其初始化时从 localStorage 加载了宽度
        setWidthBeforeCollapse(savedWidth);
      }

      hasInitialized.current = true;
    } catch (err) {
      console.error("Failed to load panel state:", err);
    }
  }, [
    storageKeyCollapsed,
    storageKeyWidthBeforeCollapse,
    minCollapsedWidth,
    defaultWidth,
    resizablePanel,
  ]);

  // 切换折叠/展开状态
  const toggleCollapse = useCallback(() => {
    // 如果正在动画中或拖拽中，不处理新的点击
    if (isAnimating || resizablePanel.isDragging) {
      return;
    }

    // 标记动画开始
    setIsAnimating(true);

    if (isCollapsed) {
      // 从折叠状态展开
      const targetWidth = widthBeforeCollapse || defaultWidth;

      // 先执行宽度变化，使用 requestAnimationFrame 确保动画平滑
      requestAnimationFrame(() => {
        // 先确保有一个起始宽度，以便动画能够正常工作
        if (resizablePanel.width < minCollapsedWidth) {
          resizablePanel.setWidth(minCollapsedWidth);
        }

        // 在下一帧执行展开动画
        requestAnimationFrame(() => {
          resizablePanel.setWidth(targetWidth);

          // 更新状态
          setIsCollapsed(false);
          saveToLocalStorage(storageKeyCollapsed, "false");

          // 等待宽度变化完成一部分后，再显示内容
          setTimeout(() => {
            setIsContentVisible(true);

            // 动画完成后清除动画状态
            setTimeout(() => {
              setIsAnimating(false);
            }, fadeAnimationDuration);
          }, animationDuration / 2);
        });
      });
    } else {
      // 从展开状态折叠
      // 保存当前宽度
      const currentWidth = resizablePanel.width;

      // 只有当宽度大于最小值时才保存
      if (currentWidth > minCollapsedWidth) {
        setWidthBeforeCollapse(currentWidth);
        // 保存到localStorage，这样刷新页面后仍能记住宽度
        saveToLocalStorage(storageKeyWidthBeforeCollapse, String(currentWidth));
      }

      // 先隐藏内容
      setIsContentVisible(false);

      // 等待内容淡出后再折叠
      setTimeout(() => {
        // 使用 requestAnimationFrame 确保动画平滑
        requestAnimationFrame(() => {
          resizablePanel.setWidth(minCollapsedWidth);

          // 等待宽度变化完成后更新状态
          setTimeout(() => {
            setIsCollapsed(true);
            setIsAnimating(false);
            saveToLocalStorage(storageKeyCollapsed, "true");
          }, animationDuration);
        });
      }, fadeAnimationDuration);
    }
  }, [
    isCollapsed,
    isAnimating,
    resizablePanel,
    widthBeforeCollapse,
    defaultWidth,
    minCollapsedWidth,
    animationDuration,
    fadeAnimationDuration,
    storageKeyCollapsed,
    storageKeyWidthBeforeCollapse,
  ]);

  return {
    isCollapsed,
    isAnimating,
    toggleCollapse,
    isContentVisible,
  };
}

/**
 * 导出 UseResizablePanelReturn 类型
 */
export type { UseResizablePanelReturn } from "./useResizablePanel";
