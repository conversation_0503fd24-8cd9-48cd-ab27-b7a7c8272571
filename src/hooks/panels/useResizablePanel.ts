import { useState, useEffect, useRef, useCallback } from "react";

interface UseResizablePanelProps {
  defaultWidth: number;
  minWidth: number;
  maxWidthRatio: number;
  storageKey?: string;
  containerRef: React.RefObject<HTMLElement>;
  direction?: "left" | "right";
}

export interface UseResizablePanelReturn {
  width: number;
  isDragging: boolean;
  isResetting: boolean;
  showContextMenu: boolean;
  contextMenuPosition: { x: number; y: number };
  clickPosition: { x: number; y: number } | null;
  dividerRef: React.RefObject<HTMLDivElement>;
  handleDragStart: (e: React.MouseEvent) => void;
  handleContextMenu: (e: React.MouseEvent) => void;
  handleResetWidth: () => void;
  setShowContextMenu: (show: boolean) => void;
  setWidth: (width: number) => void;
}

export default function useResizablePanel({
  defaultWidth,
  minWidth,
  maxWidthRatio,
  storageKey,
  containerRef,
  direction = "right",
}: UseResizablePanelProps): UseResizablePanelReturn {
  // 状态管理
  const [width, setWidth] = useState<number>(() => {
    // 直接在初始化时从localStorage获取宽度
    if (typeof window !== "undefined" && storageKey) {
      try {
        const savedWidth = localStorage.getItem(storageKey);
        if (savedWidth) {
          const parsedWidth = parseInt(savedWidth, 10);
          if (!isNaN(parsedWidth) && parsedWidth >= minWidth) {
            return parsedWidth;
          }
        }
      } catch (err) {
        console.error("Error reading from localStorage:", err);
      }
    }
    return defaultWidth;
  });

  const [isDragging, setIsDragging] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({
    x: 0,
    y: 0,
  });
  const [clickPosition, setClickPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // Refs
  const isDraggingRef = useRef(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);
  const previousWidthRef = useRef(width);
  const currentWidthRef = useRef(width); // 用于追踪拖拽过程中的当前宽度
  const dividerRef = useRef<HTMLDivElement>(null); // 添加对divider元素的引用

  // 保存宽度到 localStorage
  const saveToStorage = useCallback(
    (valueToSave: number) => {
      if (typeof window !== "undefined" && storageKey) {
        try {
          localStorage.setItem(storageKey, String(valueToSave));
        } catch (err) {
          console.error("Error saving to localStorage:", err);
        }
      }
    },
    [storageKey]
  );

  // 更新宽度和引用，但不触发额外的重新渲染
  const updateWidthRef = useCallback((newWidth: number) => {
    currentWidthRef.current = newWidth;
  }, []);

  // 更新宽度状态并保存到localStorage
  const updateWidth = useCallback(
    (newWidth: number) => {
      updateWidthRef(newWidth);
      previousWidthRef.current = newWidth;
      setWidth(newWidth);
      saveToStorage(newWidth);
    },
    [saveToStorage, updateWidthRef]
  );

  // 获取容器当前宽度的最大限制
  const getMaxWidth = useCallback(() => {
    if (!containerRef.current) return 10000; // 一个很大的数，防止报错

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    return Math.max(minWidth, containerWidth * maxWidthRatio);
  }, [containerRef, maxWidthRatio, minWidth]);

  // 验证宽度是否超出限制并调整
  const validateWidth = useCallback(() => {
    const maxWidth = getMaxWidth();
    if (width > maxWidth) {
      updateWidth(maxWidth);
      return true;
    }
    return false;
  }, [width, getMaxWidth, updateWidth]);

  // 处理窗口大小调整
  useEffect(() => {
    if (typeof window === "undefined") return;

    const handleResize = () => {
      if (!isDragging && !isResetting) {
        validateWidth();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [validateWidth, isDragging, isResetting]);

  // 初始化宽度验证
  useEffect(() => {
    // 确保容器已加载并且宽度是有效值
    if (containerRef.current && width !== previousWidthRef.current) {
      validateWidth();
    }
  }, [width, validateWidth, containerRef]);

  // 组件挂载后检查宽度，确保在合理范围
  useEffect(() => {
    if (!containerRef.current) return;

    // 使用setTimeout确保DOM已完全加载
    const timeoutId = setTimeout(() => {
      validateWidth();
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [validateWidth]);

  // 处理拖拽开始
  const handleDragStart = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      isDraggingRef.current = true;
      setIsDragging(true);
      startXRef.current = e.clientX;
      startWidthRef.current = width;
      currentWidthRef.current = width; // 初始化当前宽度引用
      document.body.classList.add("select-none");

      const handleDragMove = (e: MouseEvent) => {
        if (!isDraggingRef.current || !containerRef.current) return;

        // 计算鼠标移动的距离（根据方向决定计算方式）
        let deltaX;
        if (direction === "right") {
          // 从右往左拖拽（适用于 AI Panel）
          deltaX = startXRef.current - e.clientX;
        } else {
          // 从左往右拖拽（适用于 Sidebar）
          deltaX = e.clientX - startXRef.current;
        }

        // 计算新宽度
        let newWidth = startWidthRef.current + deltaX;
        const maxWidth = getMaxWidth();
        newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));

        // 更新引用中的当前宽度值
        updateWidthRef(newWidth);

        // 立即更新UI显示，不使用过渡效果
        setWidth(newWidth);
      };

      const handleDragEnd = () => {
        isDraggingRef.current = false;

        // 使用引用中的最新宽度值保存到localStorage
        saveToStorage(currentWidthRef.current);
        previousWidthRef.current = currentWidthRef.current;

        // 确保再次使用 requestAnimationFrame 来处理状态更新
        // 这可以避免与渲染周期的潜在冲突
        requestAnimationFrame(() => {
          setIsDragging(false);
        });

        document.removeEventListener("mousemove", handleDragMove);
        document.removeEventListener("mouseup", handleDragEnd);
        document.body.classList.remove("select-none");
      };

      document.addEventListener("mousemove", handleDragMove);
      document.addEventListener("mouseup", handleDragEnd);
    },
    [
      width,
      containerRef,
      getMaxWidth,
      minWidth,
      direction,
      updateWidthRef,
      saveToStorage,
    ]
  );

  // 处理右键菜单
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 记录鼠标右键点击的确切位置
    setClickPosition({ x: e.clientX, y: e.clientY });

    // 直接使用原始点击位置，不再计算menuX
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);
  }, []);

  // 重置宽度
  const handleResetWidth = useCallback(() => {
    setShowContextMenu(false);
    setClickPosition(null); // 清除点击位置
    setIsResetting(true);

    // 更新所有宽度相关的状态和引用
    setWidth(defaultWidth);
    currentWidthRef.current = defaultWidth;
    previousWidthRef.current = defaultWidth;

    // 移除localStorage中的记录
    if (storageKey) {
      try {
        localStorage.removeItem(storageKey);
      } catch (err) {
        console.error("Error removing from localStorage:", err);
      }
    }

    // 动画结束后取消重置状态
    setTimeout(() => {
      setIsResetting(false);
    }, 150);
  }, [defaultWidth, storageKey]);

  // 确保在组件卸载或菜单关闭时清除点击位置
  useEffect(() => {
    if (!showContextMenu) {
      setClickPosition(null);
    }
  }, [showContextMenu]);

  return {
    width,
    isDragging,
    isResetting,
    showContextMenu,
    contextMenuPosition,
    clickPosition,
    dividerRef,
    handleDragStart,
    handleContextMenu,
    handleResetWidth,
    setShowContextMenu,
    setWidth: updateWidth,
  };
}
