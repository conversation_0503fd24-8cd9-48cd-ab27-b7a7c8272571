# 服务层 (Services)

服务层包含所有与外部 API 交互的代码，遵循统一的接口规范与错误处理模式。

## 目录结构

```
/services/
  /chat/                      # 聊天相关服务
    ChatService.interface.ts  # 聊天服务通用接口
    RealChatService.ts        # 后端API聊天服务实现
    OpenAIService.ts          # OpenAI API聊天服务实现
    index.ts                  # 导出所有聊天服务
  index.ts                    # 服务主入口点
```

## 设计原则

1. **统一接口**: 所有服务都实现了共同的接口，方便替换和测试
2. **Fail-fast 错误处理**: 尽早暴露错误，保留原始错误信息，不进行不必要的错误包装
3. **功能内聚**: 将相关功能组织在同一目录下，使代码更易于维护
4. **清晰的导出**: 通过索引文件统一导出接口和实现

## 最佳实践

1. 添加新服务时，请创建适当的接口并确保实现遵循项目的错误处理规范
2. 服务应该只负责数据获取和处理，不应包含 UI 或业务逻辑
3. 服务方法应当返回原始数据或类型化的响应，不应进行数据转换
4. 错误处理应遵循 fail-fast 原则，保留原始错误信息

### API交互

- 使用标准的fetch API或axios
- API调用应在services目录下组织
- 处理API错误并展示适当的用户提示
- 环境变量管理规范：
  - 所有API地址、密钥等配置必须通过环境变量管理
  - 在项目根目录维护 `.env.example` 文件，列出所有必需的环境变量及说明
  - 本地开发使用 `.env.local` 文件（已在 .gitignore 中忽略）
  - 严禁在代码中硬编码API地址、密钥等敏感信息
  - 严禁在代码中为环境变量设置默认值，例如：
    ```typescript
    // ❌ 错误示例
    const apiUrl = process.env.API_URL || 'https://api.example.com'
    
    // ✅ 正确示例
    const apiUrl = process.env.API_URL
    if (!apiUrl) {
      throw new Error('环境变量 API_URL 未设置')
    }
    ```
  - 环境变量命名规范：
    - 使用大写字母和下划线
    - 使用有意义的前缀（如：API_、AUTH_等）
    - 示例：API_BASE_URL、AUTH_TOKEN_SECRET
  - 在代码中使用 `process.env` 访问环境变量
  - 确保在使用环境变量前进行存在性检查
