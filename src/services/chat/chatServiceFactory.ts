import { IChatService } from "./ChatService.interface";
// import { OpenAIService } from './OpenAIService';
import { RealChatService } from "./RealChatService";

export type ChatServiceType = "real" | "openai";

/**
 * 聊天服务工厂函数
 * 根据指定的类型创建不同的聊天服务实例
 */
export function createChatService(
  type: ChatServiceType = "real",
  options?: any
): IChatService {
  switch (type) {
    // case 'openai':
    //   return new OpenAIService(options?.apiKey);
    case "real":
    default:
      return new RealChatService();
  }
}
