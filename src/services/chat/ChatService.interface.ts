import { SSEEvent } from "framesound-ui";

export interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

export interface ChatServiceOptions {
  signal?: AbortSignal;
  onProgress?: (chunk: string) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

export interface StreamChatServiceOptions extends ChatServiceOptions {
  onEvent?: (event: SSEEvent) => void;
}

/**
 * 聊天服务通用接口
 * 所有聊天服务实现都应该实现此接口
 */
export interface IChatService {
  /**
   * 发送聊天消息并获取响应
   */
  generateChatResponse?: (messages: ChatMessage[]) => Promise<string>;

  /**
   * 流式响应聊天消息
   */
  streamResponse?: (
    messages: ChatMessage[],
    options?: ChatServiceOptions
  ) => AsyncGenerator<string, void, unknown>;

  /**
   * 流式响应聊天消息，以SSE事件形式返回
   */
  chatStream?: (
    messages: ChatMessage[],
    options?: StreamChatServiceOptions
  ) => AsyncGenerator<SSEEvent, void, unknown>;

  /**
   * 根据消息生成标题
   */
  generateTitle?: (message: string) => Promise<string>;

  /**
   * 根据最近消息生成标题
   */
  generateTitleFromRecentMessages?: (
    messages: ChatMessage[]
  ) => Promise<string>;
}
