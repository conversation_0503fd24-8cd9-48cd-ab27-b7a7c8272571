// import OpenAI from "openai";
// import {
//   ChatMessage,
//   ChatServiceOptions,
//   IChatService,
// } from "./ChatService.interface";

// /**
//  * OpenAI聊天服务实现
//  */
// export class OpenAIService implements IChatService {
//   private openai: OpenAI;

//   constructor(apiKey?: string) {
//     this.openai = new OpenAI({
//       apiKey: process.env.OPENAI_API_KEY,
//     });
//   }

//   /**
//    * 生成聊天响应
//    */
//   async generateChatResponse(messages: ChatMessage[]): Promise<string> {
//     try {
//       const completion = await this.openai.chat.completions.create({
//         model: "gpt-4.1-nano",
//         messages,
//         temperature: 0.7,
//         max_tokens: 1000,
//         stream: false,
//       });
//       return (
//         completion.choices[0]?.message?.content ||
//         "Sorry, I could not generate a response."
//       );
//     } catch (error) {
//       console.error("OpenAI API Error:", error);
//       throw error; // 遵循fail-fast原则，不包装错误
//     }
//   }

//   /**
//    * 流式生成聊天响应
//    */
//   async *streamResponse(
//     messages: ChatMessage[],
//     options?: ChatServiceOptions
//   ): AsyncGenerator<string, void, unknown> {
//     try {
//       const controller = new AbortController();
//       const signal = options?.signal;

//       // 如果传入了外部的AbortSignal，监听它并同步到本地的controller
//       if (signal) {
//         if (signal.aborted) {
//           controller.abort();
//           return;
//         }

//         signal.addEventListener("abort", () => {
//           controller.abort();
//         });
//       }

//       const stream = await this.openai.chat.completions.create(
//         {
//           model: "gpt-4.1-nano",
//           messages,
//           temperature: 0.7,
//           max_tokens: 8192,
//           stream: true,
//         },
//         {
//           signal: controller.signal,
//         }
//       );

//       let buffer = "";
//       const CHUNK_SIZE = 1;
//       let lastChunkTime = Date.now();
//       const MIN_CHUNK_INTERVAL = 1;

//       for await (const chunk of stream) {
//         // 检查是否已经被中断
//         if (controller.signal.aborted || (signal && signal.aborted)) {
//           console.log("Stream aborted");
//           return;
//         }

//         const content = chunk.choices[0]?.delta?.content || "";
//         if (content) {
//           buffer += content;
//           const now = Date.now();
//           const timeSinceLastChunk = now - lastChunkTime;
//           if (
//             buffer.length >= CHUNK_SIZE &&
//             timeSinceLastChunk >= MIN_CHUNK_INTERVAL
//           ) {
//             const sendChunk = buffer.slice(0, CHUNK_SIZE);
//             buffer = buffer.slice(CHUNK_SIZE);
//             lastChunkTime = now;
//             options?.onProgress?.(sendChunk);
//             yield sendChunk;
//             while (buffer.length >= CHUNK_SIZE * 2) {
//               // 再次检查是否已被中断
//               if (controller.signal.aborted || (signal && signal.aborted)) {
//                 console.log("Stream aborted during buffer processing");
//                 return;
//               }

//               const nextChunk = buffer.slice(0, CHUNK_SIZE);
//               buffer = buffer.slice(CHUNK_SIZE);
//               options?.onProgress?.(nextChunk);
//               yield nextChunk;
//             }
//           }
//         }
//       }

//       while (buffer.length > 0) {
//         // 最后一次检查是否已被中断
//         if (controller.signal.aborted || (signal && signal.aborted)) {
//           console.log("Stream aborted during final buffer processing");
//           return;
//         }

//         const remainingChunkSize = Math.min(CHUNK_SIZE, buffer.length);
//         const sendChunk = buffer.slice(0, remainingChunkSize);
//         buffer = buffer.slice(remainingChunkSize);
//         options?.onProgress?.(sendChunk);
//         yield sendChunk;
//         if (buffer.length > 0) {
//           await new Promise((resolve) =>
//             setTimeout(resolve, MIN_CHUNK_INTERVAL)
//           );
//         }
//       }

//       options?.onComplete?.();
//     } catch (error: any) {
//       // 判断是否是因为中断导致的错误
//       if (error.name === "AbortError") {
//         console.log("Request was aborted");
//         return;
//       }

//       console.error("OpenAI Stream Error:", error);
//       options?.onError?.(error);
//       throw error; // 遵循fail-fast原则，不包装错误
//     }
//   }

//   /**
//    * 根据单条消息生成标题
//    */
//   async generateTitle(message: string): Promise<string> {
//     try {
//       const completion = await this.openai.chat.completions.create({
//         model: "gpt-4.1-nano",
//         messages: [
//           {
//             role: "system",
//             content:
//               '根据用户的第一条消息生成一个简洁的会话标题，尽可能看起来是以第三人称视角对于对话的总结，如"咨询 xxx 相关问题"，或"xxxx方案"。标题字数必须严格控制在15个字符以内。保持标题简单，专注于关键话题。不要包含任何额外的文本、解释或符号，只返回标题本身。',
//           },
//           {
//             role: "user",
//             content: message,
//           },
//         ],
//         temperature: 0.8,
//         max_tokens: 40,
//         stream: false,
//       });

//       const title = completion.choices[0]?.message?.content || "未命名会话";
//       return title.length > 24 ? title.substring(0, 24) : title;
//     } catch (error) {
//       console.error("OpenAI Title Generation Error:", error);
//       throw error; // 遵循fail-fast原则，但保留回退方案
//     }
//   }

//   /**
//    * 根据最近多条消息生成标题
//    */
//   async generateTitleFromRecentMessages(
//     messages: ChatMessage[]
//   ): Promise<string> {
//     try {
//       // 确保至少有一条消息
//       if (!messages || messages.length === 0) {
//         return "未命名会话";
//       }

//       // 最多取最近3条消息（如果有的话）
//       const recentMessages = messages.slice(-Math.min(3, messages.length));

//       // 将消息格式化为字符串
//       const messagesText = recentMessages
//         .map((msg) => `${msg.role === "user" ? "用户" : "AI"}: ${msg.content}`)
//         .join("\n");

//       const completion = await this.openai.chat.completions.create({
//         model: "gpt-4.1-nano",
//         messages: [
//           {
//             role: "system",
//             content:
//               '根据最近几条对话内容生成一个简洁的会话标题，尽可能看起来是以第三人称视角对于对话的总结，如"咨询 xxx 相关问题"，或"xxxx方案"。标题字数必须严格控制在15个字符以内。保持标题简单，专注于关键话题。不要包含任何额外的文本、解释或符号，只返回标题本身。',
//           },
//           {
//             role: "user",
//             content: `请根据以下对话生成一个简洁的标题：\n${messagesText}`,
//           },
//         ],
//         temperature: 0.8,
//         max_tokens: 40,
//         stream: false,
//       });

//       const title = completion.choices[0]?.message?.content || "未命名会话";
//       return title.length > 24 ? title.substring(0, 24) : title;
//     } catch (error) {
//       console.error("OpenAI Title Generation Error:", error);
//       throw error; // 遵循fail-fast原则
//     }
//   }
// }
