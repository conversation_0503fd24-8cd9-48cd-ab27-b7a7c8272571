import { SSEEvent, SSEEventType } from "framesound-ui";
import {
  ChatMessage,
  ChatServiceOptions,
  IChatService,
  StreamChatServiceOptions,
} from "./ChatService.interface";

/**
 * 真实 API 聊天服务实现
 */
export class RealChatService implements IChatService {
  private readonly apiUrl: string;

  constructor() {
    // 从环境变量获取 API URL，如果未设置则抛出错误
    const apiUrl = process.env.NEXT_PUBLIC_AGENT_API_URL;
    if (!apiUrl) {
      throw new Error(
        "NEXT_PUBLIC_AGENT_API_URL environment variable is not set"
      );
    }
    this.apiUrl = apiUrl;
  }

  // 私有 logRequest 方法，用于调试
  private logRequest(
    method: string,
    url: string,
    headers: Record<string, string>,
    body: any
  ) {
    let formattedBody: string;
    try {
      formattedBody = JSON.stringify(JSON.parse(body), null, 2);
    } catch {
      formattedBody = String(body);
    }
    console.log(
      "\n[RealChatService] === API Request Debug Info ===\n" +
        `${method} ${url}\n` +
        "Headers:\n" +
        Object.entries(headers)
          .map(([k, v]) => `  ${k}: ${v}`)
          .join("\n") +
        "\nBody:\n" +
        formattedBody +
        "\n[RealChatService] === END ===\n"
    );
  }

  /**
   * 流式响应，返回SSE事件
   */
  async *chatStream(
    messages: ChatMessage[],
    options?: StreamChatServiceOptions
  ): AsyncGenerator<SSEEvent, void, unknown> {
    const lastUserMessage = [...messages]
      .reverse()
      .find((m) => m.role === "user");
    const input = lastUserMessage?.content || "cat";

    try {
      const method = "POST";
      const headers = {
        "Content-Type": "application/json",
        Accept: "text/event-stream",
      };
      const body = JSON.stringify({
        input: input,
        stream: true,
      });
      this.logRequest(method, this.apiUrl, headers, body);

      console.log(
        "[RealChatService] chatStream fetch 启动, signal:",
        options?.signal
      );
      // 先发送一个启动事件
      yield {
        type: "response.created" as SSEEventType,
        data: {
          type: "start",
          response: { id: `resp_${Date.now()}` },
        },
      };

      const response = await fetch(this.apiUrl, {
        method,
        headers,
        body,
        signal: options?.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `API request failed with status ${response.status}: ${errorText}`
        );
      }

      if (!response.body) {
        throw new Error("Response body is null");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let fullContent = "";
      let lastEventTypeFromEventLine: SSEEventType | null = null;

      console.log("chatStream 开始读取响应");

      while (true) {
        if (options?.signal?.aborted) {
          console.warn(
            "[RealChatService] chatStream 检测到 signal.aborted，reader.cancel"
          );
          reader.cancel("Request aborted by user");
          break;
        }

        const { done, value } = await reader.read();
        const currentChunkDecoded = decoder.decode(value, { stream: done });
        buffer += currentChunkDecoded;

        if (done && buffer.length === 0) {
          break;
        }

        let newlineIndex;
        // Process all complete lines in the buffer
        // Also process the remainder if `done` is true and buffer has content
        while (
          (newlineIndex = buffer.indexOf("\n")) >= 0 ||
          (done && buffer.length > 0)
        ) {
          let line: string;
          if (newlineIndex >= 0) {
            line = buffer.substring(0, newlineIndex).trim();
            buffer = buffer.substring(newlineIndex + 1);
          } else {
            // This is the last part of the buffer when done is true
            line = buffer.trim();
            buffer = ""; // Clear buffer as it's all processed
          }

          if (line === "") {
            // SSE spec: empty line dispatches the event.
            // We reset context here, actual dispatch happens on 'data:'
            lastEventTypeFromEventLine = null;
            console.log("Processed empty line (event separator).");
            if (done && buffer.length === 0) break;
            continue;
          }

          if (line.startsWith("event:")) {
            lastEventTypeFromEventLine = line
              .substring(6)
              .trim() as SSEEventType;
            console.log(
              "SSE Event type specified:",
              lastEventTypeFromEventLine
            );
            if (done && buffer.length === 0) break;
            continue;
          }

          let dataContent = line;
          if (line.startsWith("data:")) {
            dataContent = line.substring(5).trim();
          }

          if (dataContent) {
            try {
              const parsedJson = JSON.parse(dataContent);
              if (
                parsedJson.type &&
                typeof parsedJson.type === "string" &&
                parsedJson.type.startsWith("response.")
              ) {
                const finalEventType = parsedJson.type as SSEEventType;
                const blockType = finalEventType.split(".")[1];
                const sseDataPayload: any = {
                  type: blockType,
                  index: parsedJson.index !== undefined ? parsedJson.index : 0,
                };

                if (parsedJson.delta !== undefined) {
                  sseDataPayload.delta = parsedJson.delta;
                  // Accumulate delta for text-like events to fullContent
                  if (
                    blockType === "text" ||
                    finalEventType.endsWith(".delta")
                  ) {
                    fullContent += parsedJson.delta;
                  }
                }
                if (parsedJson.output_text !== undefined) {
                  sseDataPayload.output_text = parsedJson.output_text;
                }
                for (const key in parsedJson) {
                  if (
                    !["type", "index", "delta", "output_text"].includes(key) &&
                    !sseDataPayload.hasOwnProperty(key)
                  ) {
                    sseDataPayload[key] = parsedJson[key];
                  }
                }

                const sseEvent: SSEEvent = {
                  type: finalEventType,
                  data: sseDataPayload,
                };
                console.log(
                  `Yielding structured event: ${JSON.stringify(
                    sseEvent
                  ).substring(0, 150)}`
                );
                yield sseEvent;
                options?.onEvent?.(sseEvent);
                lastEventTypeFromEventLine = null;
              } else {
                // JSON, but not a known "response." type event, treat as simple text
                console.log(
                  "Yielding unhandled JSON as text delta:",
                  dataContent.substring(0, 50)
                );
                fullContent += dataContent;
                // 使用直接的内容更新，而不是文本事件块
                const contentEvent: SSEEvent = {
                  type: "response.content.update" as SSEEventType,
                  data: { type: "content", delta: dataContent },
                };
                yield contentEvent;
                options?.onEvent?.(contentEvent);
              }
            } catch (e) {
              // Not JSON or parse error
              if (
                lastEventTypeFromEventLine &&
                lastEventTypeFromEventLine.endsWith(".delta")
              ) {
                const blockType = lastEventTypeFromEventLine.split(".")[1];
                const sseEvent: SSEEvent = {
                  type: lastEventTypeFromEventLine,
                  data: { type: blockType, delta: dataContent, index: 0 },
                };
                console.log(
                  `Yielding text as delta for ${lastEventTypeFromEventLine}: ${dataContent.substring(
                    0,
                    50
                  )}`
                );
                yield sseEvent;
                options?.onEvent?.(sseEvent);
                fullContent += dataContent;
                lastEventTypeFromEventLine = null;
              } else {
                console.log(
                  "Yielding generic text as content update:",
                  dataContent.substring(0, 50)
                );
                fullContent += dataContent;
                // 使用直接的内容更新，而不是文本事件块
                const contentEvent: SSEEvent = {
                  type: "response.content.update" as SSEEventType,
                  data: { type: "content", delta: dataContent },
                };
                yield contentEvent;
                options?.onEvent?.(contentEvent);
              }
            }
          }
          if (done && buffer.length === 0) break;
        }

        if (done) {
          break;
        }
      }

      console.log("Stream reading complete. Finalizing.");
      // 发送完成事件
      yield {
        type: "response.completed" as SSEEventType,
        data: {
          type: "complete",
          output_text: { id: `complete_${Date.now()}` },
        },
      };
      console.log("chatStream 响应完成, 总内容长度:", fullContent.length);
    } catch (error) {
      const errorName = error instanceof Error ? error.name : typeof error;
      console.error(
        "[RealChatService] chatStream error:",
        error,
        "类型:",
        errorName
      );
      if (options?.signal?.aborted) {
        console.warn(
          "[RealChatService] chatStream 捕获到 aborted 状态，终止流"
        );
        return;
      }
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      console.log("chatStream 发生错误:", errorMessage);

      // 发送错误事件
      const errorEvent: SSEEvent = {
        type: "response.text.stop" as SSEEventType,
        data: {
          type: "error",
          output_text: { id: `error_${Date.now()}` },
        },
      };

      options?.onError?.(
        error instanceof Error ? error : new Error(errorMessage)
      );
      options?.onEvent?.(errorEvent);
      yield errorEvent;
    } finally {
      options?.onComplete?.();
    }
  }
}
