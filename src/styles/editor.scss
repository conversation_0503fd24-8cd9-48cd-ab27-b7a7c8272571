/* ==================== Chrome 浏览器专用样式配置 ==================== */
.ProseMirror {
  /* Chrome 基础配置 */
  --base-line-height: 1.8;
  line-height: var(--base-line-height);
  font-family: "Noto Sans SC", sans-serif;
  color: #282828;

  /* Chrome 暗色模式 */
  .dark & {
    color: #e2e2e2;
  }

  /* Chrome 加粗文本 */
  strong,
  b {
    color: #171717;
    font-weight: 600;

    .dark & {
      color: #ffffff;
    }
  }

  /* Chrome 标题样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.8;
    font-family: "Noto Sans SC", sans-serif;
    color: #171717;

    .dark & {
      color: #ffffff;
    }
  }

  /* Chrome Title 样式 */
  h1.title-heading {
    line-height: 1.4;
    font-size: 20pt;
    font-weight: 800;
    margin: 0;
    letter-spacing: 0em;
    color: #171717;
    padding-bottom: 0.2em;

    .dark & {
      color: #ffffff;
    }
  }

  /* Chrome 标题大小 */
  h1 {
    font-size: 18pt;
    font-weight: 600;
    margin: 0.2em 0;
  }
  h2 {
    font-size: 16pt;
    font-weight: 600;
    margin: 0.2em 0;
  }
  h3 {
    font-size: 15pt;
    font-weight: 600;
    margin: 0.2em 0;
  }
  h4 {
    font-size: 14pt;
    font-weight: 600;
    margin: 0.2em 0;
  }

  /* Chrome 段落样式 */
  p {
    font-size: 14pt;
    color: #282828;
    line-height: var(--base-line-height);
    margin: 0em 0;

    .dark & {
      color: #e2e2e2;
    }
  }

  /* Chrome 列表基础样式 */
  ul,
  ol {
    line-height: var(--base-line-height);
    margin: 0.5em 0;

    li {
      margin: 0;
      line-height: inherit;
      position: relative;
      padding-left: 0;

      > p {
        margin: 0.2em 0;
        text-indent: 1.2em;
      }

      > ul,
      > ol {
        margin: 0;
        padding-left: 1.1em;
      }
    }
  }

  /* Chrome 有序列表配置 */
  ol {
    list-style-position: outside;
    padding-left: 1.1em;
  }

  /* Chrome 无序列表配置 */
  ul {
    list-style-position: outside;
    padding-left: 1.1em;
  }

  /* Chrome 列表项对齐 */
  ul li,
  ol li {
    padding-left: 0;
    text-indent: 0;
  }

  /* Chrome 列表项首行缩进 */
  ul li p:first-child,
  ol li p:first-child {
    text-indent: 1.2em;
  }

  /* Chrome 缩进状态列表项首行 */
  ul.indented li p:first-child,
  ol.indented li p:first-child {
    text-indent: 3em;
  }

  /* Chrome 列表项段落对齐 */
  ul li p,
  ol li p {
    display: block;
    position: relative;
    margin-left: -17px;
  }

  /* Chrome 缩进状态列表项段落 */
  ul.indented li p,
  ol.indented li p {
    margin-left: -54px;
  }

  /* Chrome 嵌套列表项段落 */
  ul li > ul li p,
  ul li > ol li p,
  ol li > ul li p,
  ol li > ol li p {
    margin-left: 0px;
  }

  /* Chrome 嵌套列表项缩进 */
  ul li > ul li,
  ul li > ol li,
  ol li > ul li,
  ol li > ol li {
    margin-left: 6px;
  }

  /* Chrome 嵌套列表项首行缩进 */
  ul li > ul li p:first-child,
  ul li > ol li p:first-child,
  ol li > ul li p:first-child,
  ol li > ol li p:first-child {
    text-indent: 0em;
  }

  /* Chrome 嵌套列表缩进段落 */
  ul li > ul li p.indented,
  ul li > ol li p.indented,
  ol li > ul li p.indented,
  ol li > ol li p.indented {
    text-indent: 0em !important;
  }

  /* Chrome 引用块样式 */
  blockquote {
    line-height: var(--base-line-height);
    margin: 1em 0;
    padding: 0em 1em;

    .dark & {
      color: #d0d0d0;
      border-left-color: #4a4a4a;
    }
  }

  /* Chrome 段落缩进 */
  p.indented {
    text-indent: 2em;
  }

  /* Chrome 标题缩进 */
  h1.indented,
  h2.indented,
  h3.indented,
  h4.indented,
  h5.indented,
  h6.indented {
    text-indent: 38px;
  }

  /* Chrome 列表容器缩进 */
  ul.indented,
  ol.indented {
    padding-left: 56px;
  }

  /* Chrome 列表项段落缩进 */
  ol li p.indented,
  ul li p.indented {
    text-indent: 1.2em !important;
  }

  /* Chrome 缩进状态列表项段落缩进 */
  ol.indented li p.indented,
  ul.indented li p.indented {
    text-indent: 3em !important;
  }

  /* Chrome 列表项缩进移除 */
  ol.indented li.indented,
  ul.indented li.indented {
    padding-left: 0 !important;
  }

  /* Chrome 嵌套列表缩进保持 */
  ul.indented li > ul,
  ul.indented li > ol,
  ol.indented li > ul,
  ol.indented li > ol {
    margin: 0;
    padding-left: 1.1em;
  }

  /* Chrome 嵌套列表项缩进恢复 */
  ul.indented li > ul li p:first-child,
  ul.indented li > ol li p:first-child,
  ol.indented li > ul li p:first-child,
  ol.indented li > ol li p:first-child {
    text-indent: 0em;
  }

  /* Chrome 嵌套列表项段落缩进恢复 */
  ul.indented li > ul li p,
  ul.indented li > ol li p,
  ol.indented li > ul li p,
  ol.indented li > ol li p {
    margin-left: 0px;
  }

  /* Chrome 嵌套列表缩进段落恢复 */
  ul.indented li > ul li p.indented,
  ul.indented li > ol li p.indented,
  ol.indented li > ul li p.indented,
  ol.indented li > ol li p.indented {
    text-indent: 0em !important;
  }

  /* Chrome 列表项暗色模式 */
  ul li,
  ol li {
    .dark & {
      color: #e2e2e2;
    }
  }
}

/* ==================== Safari 浏览器专用样式配置 ==================== */
@supports (-webkit-hyphens: none) {
  .ProseMirror {
    /* Safari 基础配置 */
    --base-line-height: 1.8;
    line-height: var(--base-line-height);
    font-family: "Noto Sans SC", sans-serif;
    color: #282828;
    -webkit-font-smoothing: antialiased;

    /* Safari 暗色模式 */
    .dark & {
      color: #e2e2e2;
    }

    /* Safari 加粗文本 */
    strong,
    b {
      color: #171717;
      font-weight: 600;
      -webkit-font-smoothing: antialiased;

      .dark & {
        color: #ffffff;
      }
    }

    /* Safari 标题样式 */
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      line-height: 1.8;
      font-family: "Noto Sans SC", sans-serif;
      color: #171717;
      -webkit-font-smoothing: antialiased;

      .dark & {
        color: #ffffff;
      }
    }

    /* Safari Title 样式 */
    h1.title-heading {
      line-height: 1.4;
      font-size: 20pt;
      font-weight: 800;
      margin: 0;
      letter-spacing: 0em;
      color: #171717;
      padding-bottom: 0.2em;
      -webkit-font-smoothing: antialiased;

      .dark & {
        color: #ffffff;
      }
    }

    /* Safari 标题大小 */
    h1 {
      font-size: 18pt;
      font-weight: 600;
      margin: 0.2em 0;
    }
    h2 {
      font-size: 16pt;
      font-weight: 600;
      margin: 0.2em 0;
    }
    h3 {
      font-size: 15pt;
      font-weight: 600;
      margin: 0.2em 0;
    }
    h4 {
      font-size: 14pt;
      font-weight: 600;
      margin: 0.2em 0;
    }

    /* Safari 段落样式 */
    p {
      font-size: 14pt;
      color: #282828;
      line-height: var(--base-line-height);
      margin: 0em 0;
      -webkit-font-smoothing: antialiased;

      .dark & {
        color: #e2e2e2;
      }
    }

    /* Safari 列表基础样式 */
    ul,
    ol {
      line-height: var(--base-line-height);
      margin: 0.5em 0;
      -webkit-margin-before: 0.5em;
      -webkit-margin-after: 0.5em;

      li {
        margin: 0;
        line-height: inherit;
        position: relative;
        padding-left: 0;
        -webkit-margin-before: 0;
        -webkit-margin-after: 0;

        > p {
          margin: 0.2em 0;
          text-indent: 1.2em;
          -webkit-margin-before: 0.2em;
          -webkit-margin-after: 0.2em;
        }

        > ul,
        > ol {
          margin: 0;
          padding-left: 1.1em;
          -webkit-margin-before: 0;
          -webkit-margin-after: 0;
          -webkit-padding-start: 1.1em;
        }
      }
    }

    /* Safari 有序列表配置 */
    ol {
      list-style-position: inside;
      padding-left: 1.1em;
      -webkit-padding-start: 0em;
    }

    /* Safari 无序列表配置 */
    ul {
      list-style-position: inside;
      padding-left: 0em;
      -webkit-padding-start: 0em;
    }

    /* Safari 缩进状态列表符号强制跟随 */
    // ul.indented,
    // ol.indented {
    //   list-style-position: inside !important;
    // }

    /* Safari 列表项对齐 */
    ul li,
    ol li {
      padding-left: 0;
      text-indent: 0;
      -webkit-padding-start: 0;
    }

    /* Safari 缩进状态列表项 */
    ul.indented li,
    ol.indented li {
      padding-left: 0em;
      -webkit-padding-start: 0em;
      margin-left: 0em;
      -webkit-margin-start: 0em;
    }

    /* Safari 缩进状态列表项首行 */
    ul.indented li p:first-child,
    ol.indented li p:first-child {
      text-indent: 0em;
      -webkit-text-indent: 0em;
    }

    /* Safari 列表项段落对齐 */
    ul li p,
    ol li p {
      display: block;
      position: relative;
      margin-left: -17px;
      -webkit-margin-start: 0px;
      text-indent: 0;
    }

    /* Safari 列表项首行悬挂缩进 */
    ul li p:first-child,
    ol li p:first-child {
      text-indent: 0em;
      -webkit-text-indent: 0em;
    }

    /* Safari 缩进状态列表项段落 */
    ul.indented li p,
    ol.indented li p {
      -webkit-margin-start: -56px;
    }

    /* Safari 嵌套列表项段落 */
    ul li > ul li p,
    ul li > ol li p,
    ol li > ul li p,
    ol li > ol li p {
      margin-left: 0px;
      -webkit-margin-start: 0px;
    }

    /* Safari 嵌套列表项缩进 */
    ul li > ul li,
    ul li > ol li,
    ol li > ul li,
    ol li > ol li {
      margin-left: 6px;
      -webkit-margin-start: 6px;
    }

    /* Safari 嵌套列表项首行缩进 */
    ul li > ul li p:first-child,
    ul li > ol li p:first-child,
    ol li > ul li p:first-child,
    ol li > ol li p:first-child {
      text-indent: 0em;
    }

    /* Safari 嵌套列表缩进段落 */
    ul li > ul li p.indented,
    ul li > ol li p.indented,
    ol li > ul li p.indented,
    ol li > ol li p.indented {
      text-indent: 0em !important;
    }

    /* Safari 引用块样式 */
    blockquote {
      line-height: var(--base-line-height);
      margin: 1em 0;
      padding: 0em 1em;
      -webkit-margin-before: 1em;
      -webkit-margin-after: 1em;
      -webkit-padding-start: 1em;

      .dark & {
        color: #d0d0d0;
        border-left-color: #4a4a4a;
      }
    }

    /* Safari 段落缩进 */
    p.indented {
      text-indent: 2em;
    }

    /* Safari 标题缩进 */
    h1.indented,
    h2.indented,
    h3.indented,
    h4.indented,
    h5.indented,
    h6.indented {
      text-indent: 38px;
    }

    /* Safari 列表容器缩进 */
    ul.indented,
    ol.indented {
      padding-left: 24px;
      -webkit-padding-start: 24px;
      margin-left: 32px;
      -webkit-margin-start: 32px;
    }

    /* Safari 列表项段落缩进 */
    ol li p.indented,
    ul li p.indented {
      text-indent: 1.2em !important;
      -webkit-text-indent: 1.2em !important;
    }

    /* Safari 缩进状态列表项段落缩进 */
    ol.indented li p.indented,
    ul.indented li p.indented {
      text-indent: 2em !important;
      -webkit-text-indent: 1.8em !important;
    }

    /* Safari 列表项缩进移除 */
    ol.indented li.indented,
    ul.indented li.indented {
      padding-left: 0 !important;
      -webkit-padding-start: 0 !important;
    }

    /* Safari 嵌套列表缩进保持 */
    ul.indented li > ul,
    ul.indented li > ol,
    ol.indented li > ul,
    ol.indented li > ol {
      margin: 0;
      padding-left: 1.1em;
      -webkit-margin-before: 0;
      -webkit-margin-after: 0;
      -webkit-padding-start: 1.1em;
    }

    /* Safari 嵌套列表项缩进恢复 */
    ul.indented li > ul li p:first-child,
    ul.indented li > ol li p:first-child,
    ol.indented li > ul li p:first-child,
    ol.indented li > ol li p:first-child {
      text-indent: 0em;
    }

    /* Safari 嵌套列表项段落缩进恢复 */
    ul.indented li > ul li p,
    ul.indented li > ol li p,
    ol.indented li > ul li p,
    ol.indented li > ol li p {
      margin-left: 0px;
      -webkit-margin-start: 0px;
    }

    /* Safari 嵌套列表缩进段落恢复 */
    ul.indented li > ul li p.indented,
    ul.indented li > ol li p.indented,
    ol.indented li > ul li p.indented,
    ol.indented li > ol li p.indented {
      text-indent: 0em !important;
    }

    /* Safari 列表项暗色模式 */
    ul li,
    ol li {
      .dark & {
        color: #e2e2e2;
      }
    }
  }
}
