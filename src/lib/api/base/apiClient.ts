/**
 * API 客户端基础配置
 * 提供统一的 HTTP 请求配置和拦截器
 */

import logger from "@/utils/logger";

// 创建 API 客户端专用的 logger
const apiLogger = logger.createPrefixed("API");

// 获取 API 基础 URL - 使用内部 API 路由代理
const getApiBaseUrl = (): string => {
  // 使用 Next.js API 路由作为代理，避免暴露真实后端地址
  if (typeof window !== "undefined") {
    // 客户端：使用相对路径访问 API 路由
    return "/api/proxy";
  } else {
    // 服务端：从私有环境变量获取真实 API 地址
    const baseUrl = process.env.API_BASE_URL;
    if (!baseUrl) {
      throw new Error("环境变量 API_BASE_URL 未设置");
    }
    return baseUrl;
  }
};

// API 基础配置
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  TIMEOUT: 10000,
  HEADERS: {
    "Content-Type": "application/json",
  },
} as const;

// 请求配置接口
export interface RequestConfig {
  method?: "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
  headers?: Record<string, string>;
  body?: any;
  signal?: AbortSignal;
}

// 响应接口
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
}

/**
 * 基础 API 客户端类
 */
export class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL?: string) {
    this.baseURL = baseURL || API_CONFIG.BASE_URL;
    this.defaultHeaders = { ...API_CONFIG.HEADERS };
  }

  /**
   * 发送 HTTP 请求
   */
  async request<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const { method = "GET", headers = {}, body, signal } = config;

    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
      // 明确指定接受的编码格式，避免压缩问题
      "Accept-Encoding": "gzip, deflate, br, identity",
      Accept: "application/json, text/plain, */*",
    };

    const requestInit: RequestInit = {
      method,
      headers: requestHeaders,
      signal,
    };

    if (body && method !== "GET") {
      requestInit.body = JSON.stringify(body);
    }

    try {
      apiLogger.log("🌐 发送请求:", { url, method, headers: requestHeaders });
      const response = await fetch(url, requestInit);
      apiLogger.log("📡 收到响应:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      });

      let data: T;
      const contentType = response.headers.get("content-type");
      const contentEncoding = response.headers.get("content-encoding");

      apiLogger.log("📋 响应头信息:", { contentType, contentEncoding });

      // 特殊处理204 No Content响应
      if (response.status === 204) {
        apiLogger.log("✅ 204 No Content 响应，无需解析响应体");
        data = undefined as unknown as T;
      } else if (contentType && contentType.includes("application/json")) {
        try {
          // 对于 gzip 压缩的响应，先检查是否可以正常读取
          if (contentEncoding === "gzip") {
            apiLogger.log("🗜️ 检测到 gzip 压缩响应，尝试解析...");
          }

          data = await response.json();
          apiLogger.log("✅ JSON解析成功:", data);
        } catch (jsonError) {
          apiLogger.error("❌ JSON解析失败:", jsonError);
          apiLogger.error("响应详情:", {
            status: response.status,
            statusText: response.statusText,
            contentType,
            contentEncoding,
            url: response.url,
          });

          // 尝试作为文本读取以获取更多信息
          try {
            const responseClone = response.clone();
            const textContent = await responseClone.text();
            apiLogger.error(
              "原始响应内容:",
              textContent.substring(0, 200) + "..."
            );
          } catch (textError) {
            apiLogger.error("无法读取响应文本:", textError);
          }

          throw new Error(`Failed to parse JSON response: ${jsonError}`);
        }
      } else {
        try {
          data = (await response.text()) as unknown as T;
          apiLogger.log("✅ 文本解析成功:", data);
        } catch (textError) {
          apiLogger.error("❌ 文本解析失败:", textError);
          throw new Error(`Failed to parse text response: ${textError}`);
        }
      }

      if (!response.ok) {
        const error = new Error(
          `HTTP ${response.status}: ${response.statusText}`
        );
        // 将响应数据附加到错误对象上，供错误处理器使用
        (error as any).responseData = data;
        (error as any).status = response.status;
        throw error;
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
      };
    } catch (error) {
      apiLogger.error("🚨 请求失败:", error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("Unknown error occurred");
    }
  }

  /**
   * GET 请求
   */
  get<T = any>(
    endpoint: string,
    config?: Omit<RequestConfig, "method" | "body">
  ) {
    return this.request<T>(endpoint, { ...config, method: "GET" });
  }

  /**
   * POST 请求
   */
  post<T = any>(
    endpoint: string,
    body?: any,
    config?: Omit<RequestConfig, "method">
  ) {
    return this.request<T>(endpoint, { ...config, method: "POST", body });
  }

  /**
   * PUT 请求
   */
  put<T = any>(
    endpoint: string,
    body?: any,
    config?: Omit<RequestConfig, "method">
  ) {
    return this.request<T>(endpoint, { ...config, method: "PUT", body });
  }

  /**
   * PATCH 请求
   */
  patch<T = any>(
    endpoint: string,
    body?: any,
    config?: Omit<RequestConfig, "method">
  ) {
    return this.request<T>(endpoint, { ...config, method: "PATCH", body });
  }

  /**
   * DELETE 请求
   */
  delete<T = any>(
    endpoint: string,
    config?: Omit<RequestConfig, "method" | "body">
  ) {
    return this.request<T>(endpoint, { ...config, method: "DELETE" });
  }
}

// 默认 API 客户端实例
export const apiClient = new ApiClient();
