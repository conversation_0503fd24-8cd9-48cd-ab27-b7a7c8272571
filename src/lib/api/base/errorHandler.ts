/*
 * API 错误处理模块
 * 提供统一的错误处理和错误类型定义
 */

import { showErrorToast } from "@/utils/toast";

// 验证错误详情接口（基于 API 文档的 422 错误格式）
export interface ValidationErrorDetail {
  loc: (string | number)[];
  msg: string;
  type: string;
}

// 验证错误响应接口
export interface ValidationErrorResponse {
  detail: ValidationErrorDetail[];
}

// API 错误类型枚举
export enum ApiErrorType {
  VALIDATION_ERROR = "VALIDATION_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
  SERVER_ERROR = "SERVER_ERROR",
  UNAUTHORIZED = "UNAUTHORIZED",
  NOT_FOUND = "NOT_FOUND",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

// 错误处理配置选项
export interface ErrorHandlerOptions {
  showToast?: boolean; // 是否显示 toast 提示，默认 true
  dedupeWindow?: number; // 错误去重时间窗口（毫秒），默认 5000
  logError?: boolean; // 是否记录错误日志，默认 true
}

// 错误缓存，用于去重
const errorCache = new Map<string, number>();

/**
 * 检查是否应该显示错误提示（去重机制）
 */
function shouldShowError(error: ApiError, dedupeWindow = 5000): boolean {
  const key = `${error.type}-${error.status || "unknown"}-${error.message}`;
  const now = Date.now();
  const lastShown = errorCache.get(key);

  if (!lastShown || now - lastShown > dedupeWindow) {
    errorCache.set(key, now);
    return true;
  }
  return false;
}

/**
 * 清理过期的错误缓存
 */
function cleanupErrorCache(): void {
  const now = Date.now();
  const maxAge = 60000; // 1分钟后清理

  for (const [key, timestamp] of errorCache.entries()) {
    if (now - timestamp > maxAge) {
      errorCache.delete(key);
    }
  }
}

// 定期清理错误缓存
setInterval(cleanupErrorCache, 30000); // 每30秒清理一次

// API 错误类
export class ApiError extends Error {
  public readonly type: ApiErrorType;
  public readonly status?: number;
  public readonly details?: ValidationErrorDetail[];

  constructor(
    message: string,
    type: ApiErrorType = ApiErrorType.UNKNOWN_ERROR,
    status?: number,
    details?: ValidationErrorDetail[]
  ) {
    super(message);
    this.name = "ApiError";
    this.type = type;
    this.status = status;
    this.details = details;
  }
}

/**
 * 处理 API 响应错误
 * @param error 原始错误对象
 * @param options 错误处理配置选项
 * @returns 处理后的 ApiError 实例
 */
export function handleApiError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  const { showToast = true, dedupeWindow = 5000, logError = true } = options;

  // 创建 ApiError 实例
  let apiError: ApiError;

  // 网络错误
  if (error instanceof TypeError && error.message.includes("fetch")) {
    apiError = new ApiError(
      "Network connection failed, please check your connection",
      ApiErrorType.NETWORK_ERROR
    );
  }
  // HTTP 错误
  else if (error.message && error.message.includes("HTTP")) {
    const statusMatch = error.message.match(/HTTP (\d+)/);
    const status = statusMatch ? parseInt(statusMatch[1]) : undefined;

    switch (status) {
      case 401:
        apiError = new ApiError(
          "Unauthorized access, please login again",
          ApiErrorType.UNAUTHORIZED,
          status
        );
        break;
      case 404:
        apiError = new ApiError(
          "Requested resource not found",
          ApiErrorType.NOT_FOUND,
          status
        );
        break;
      case 422:
        // 尝试解析验证错误详情
        try {
          // 从 apiClient 传递的响应数据中获取详情
          const responseData = (error as any).responseData;
          const details = responseData?.detail as ValidationErrorDetail[];
          apiError = new ApiError(
            "Request validation failed",
            ApiErrorType.VALIDATION_ERROR,
            status,
            details
          );
        } catch {
          apiError = new ApiError(
            "Request validation failed",
            ApiErrorType.VALIDATION_ERROR,
            status
          );
        }
        break;
      case 500:
      case 502:
      case 503:
        apiError = new ApiError(
          "Server error, please try again later",
          ApiErrorType.SERVER_ERROR,
          status
        );
        break;
      default:
        apiError = new ApiError(
          error.message || "Request failed",
          ApiErrorType.UNKNOWN_ERROR,
          status
        );
        break;
    }
  }
  // 其他错误
  else {
    apiError = new ApiError(
      error.message || "Unknown error",
      ApiErrorType.UNKNOWN_ERROR
    );
  }

  // 统一的错误处理逻辑
  if (logError) {
    console.error("API Error:", {
      type: apiError.type,
      status: apiError.status,
      message: apiError.message,
      details: apiError.details,
      originalError: error,
    });
  }

  // 显示用户提示（带去重）
  if (showToast && shouldShowError(apiError, dedupeWindow)) {
    showErrorToast(apiError.message);
  }

  return apiError;
}

/**
 * 格式化验证错误信息
 */
export function formatValidationErrors(
  details: ValidationErrorDetail[]
): string {
  return details
    .map((detail) => {
      const field = detail.loc.join(".");
      return `${field}: ${detail.msg}`;
    })
    .join("; ");
}
