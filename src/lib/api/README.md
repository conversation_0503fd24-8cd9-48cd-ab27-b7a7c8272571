# API 架构文档

这是 FrameSound Doc View 项目的 API 客户端架构，提供了类型安全、易于使用的 API 接口。

## 📁 目录结构

```
src/lib/api/
├── base/                   # 基础配置
│   ├── apiClient.ts       # HTTP 客户端
│   └── errorHandler.ts    # 错误处理
├── types/                 # 类型定义
│   ├── common.ts         # 通用类型
│   ├── user.ts           # 用户类型
│   ├── project.ts        # 项目类型
│   ├── system.ts         # 系统类型
│   └── index.ts          # 统一导出
├── services/             # 服务层
│   ├── userService.ts    # 用户服务
│   ├── projectService.ts # 项目服务
│   ├── systemService.ts  # 系统服务
│   └── index.ts          # 统一导出
├── hooks/                # React Hooks
│   └── useUser.ts        # 用户相关 hooks
├── examples/             # 使用示例
│   └── usage.ts          # 使用示例
├── README.md             # 本文档
└── index.ts              # 主入口
```

## 🚀 快速开始

### 1. 基础使用

```typescript
import { api } from "@/lib/api";

// 获取项目列表
const projects = await api.project.getProjects();

// 获取项目详情
const project = await api.project.getProject("project-id");

// 系统健康检查
const health = await api.system.healthCheck();
```

### 2. 在 React 组件中使用

```typescript
import { useUserProfile } from "@/lib/api/hooks/useUser";

function UserProfile() {
  const { data, loading, error, updateProfile } = useUserProfile();

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      <h1>{data?.real_name}</h1>
      <p>{data?.company_name}</p>
    </div>
  );
}
```

### 3. 错误处理

```typescript
import { api, ApiError, ApiErrorType } from "@/lib/api";

try {
  const result = await api.project.getProjects();
} catch (error) {
  if (error instanceof ApiError) {
    switch (error.type) {
      case ApiErrorType.VALIDATION_ERROR:
        console.log("验证错误:", error.details);
        break;
      case ApiErrorType.UNAUTHORIZED:
        // 重定向到登录页
        break;
      case ApiErrorType.NETWORK_ERROR:
        // 显示网络错误提示
        break;
    }
  }
}
```

## 🔧 配置

### API 基础配置

```typescript
// src/lib/api/base/apiClient.ts
export const API_CONFIG = {
  BASE_URL: "http:/xxxx/v0",
  TIMEOUT: 10000,
  HEADERS: {
    "Content-Type": "application/json",
  },
};
```

### Mock 环境设置

在 Mock 环境下，需要先设置用户 ID：

```typescript
import { api } from "@/lib/api";

// 设置用户ID（仅Mock环境）
await api.system.setUserId({ user_id: "test-user-123" });
```

## 📋 已实现的功能

### ✅ 用户服务 (UserService)

- `getProfile()` - 获取用户资料
- `updateProfile(data)` - 更新用户资料

### ✅ 项目服务 (ProjectService)

- `getProjects()` - 获取项目列表
- `createProject(data)` - 创建项目
- `updateProject(id, data)` - 更新项目
- `deleteProject(id)` - 删除项目
- `getProjectTree(id)` - 获取项目文件树
- ~~`getProject(id)` - 获取项目详情~~ (API 暂不支持)
- ~~`updatePersonalization(id, data)` - 更新个性化设置~~ (API 暂不支持)

### ✅ 系统服务 (SystemService)

- `setUserId(data)` - 设置用户 ID（Mock 环境）
- `healthCheck()` - 健康检查

### ✅ 文档服务 (DocService)

- `getDoc(id)` - 根据 ID 获取文档
- `createDoc(data)` - 创建文档（支持 meta_data）
- `updateDoc(id, data)` - 更新文档（部分更新）
- `deleteDoc(id)` - 删除文档（软删除）
- ~~`replaceDoc(id, data)` - 完整替换文档~~ (API 已移除)

### ✅ 文件服务 (FileService)

- `getFile(id)` - 根据 ID 获取文件
- `createFile(data)` - 创建文件
- `updateFile(id, data)` - 更新文件
- `deleteFile(id)` - 删除文件
- `moveFile(id, data)` - 跨项目移动文件

## 🔮 待扩展功能

以下服务可根据需要添加：

- **AI 服务 (AgentService)** - AI 相关功能
- **搜索服务 (SearchService)** - 全局搜索功能
- **通知服务 (NotificationService)** - 消息通知功能

## 🎯 设计原则

1. **类型安全** - 完整的 TypeScript 类型定义
2. **统一错误处理** - 标准化的错误处理机制
3. **易于扩展** - 模块化设计，便于添加新功能
4. **简洁易用** - 提供直观的 API 接口
5. **React 友好** - 提供开箱即用的 React Hooks

## 📝 注意事项

1. 所有 API 调用都会自动处理错误，并抛出 `ApiError` 实例
2. 在 Mock 环境下，需要先调用 `setUserId` 设置用户身份
3. 所有服务方法都是异步的，需要使用 `await` 或 `.then()`
4. 类型定义严格遵循 API 文档规范

## 🔄 扩展指南

要添加新的服务模块：

1. 在 `types/` 目录下添加相应的类型定义
2. 在 `services/` 目录下创建服务类
3. 在 `services/index.ts` 中导出新服务
4. 在 `index.ts` 的 `api` 对象中添加新服务
5. 可选：在 `hooks/` 目录下添加相应的 React Hooks
