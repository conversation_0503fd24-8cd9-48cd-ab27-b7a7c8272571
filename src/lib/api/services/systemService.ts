/**
 * 系统服务模块
 * 处理系统相关的 API 请求
 */

import { apiClient } from "../base/apiClient";
import { handleApiError } from "../base/errorHandler";
import {
  SetUserIdRequest,
  SetUserIdResponse,
  HealthCheckResponse,
} from "../types";

/**
 * 系统服务类
 */
export class SystemService {
  /**
   * 设置用户ID到Cookie（Mock环境专用）
   * POST /set_user_id
   */
  async setUserId(data: SetUserIdRequest): Promise<SetUserIdResponse> {
    try {
      const response = await apiClient.post<SetUserIdResponse>(
        "/set_user_id",
        data
      );
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 健康检查
   * GET /health
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    try {
      const response = await apiClient.get<HealthCheckResponse>("/ping");
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }
}

// 导出系统服务实例
export const systemService = new SystemService();
