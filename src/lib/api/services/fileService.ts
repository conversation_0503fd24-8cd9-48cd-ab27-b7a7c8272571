/**
 * 文件服务
 * 基于 API 文档的文件端点实现
 */

import { apiClient } from '../base/apiClient';
import { handleApiError } from '../base/errorHandler';
import {
  File,
  FileCreateRequest,
  FileUpdateRequest,
  FileMoveRequest,
  UUID,
} from '../types';

export class FileService {
  /**
   * 根据ID获取文件
   * GET /files/{file_id}
   */
  async getFile(fileId: UUID): Promise<File> {
    try {
      const response = await apiClient.get<File>(`/files/${fileId}`);
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 创建文件
   * POST /files/
   */
  async createFile(request: FileCreateRequest): Promise<File> {
    try {
      const response = await apiClient.post<File>('/files/', request);
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 更新文件
   * PATCH /files/{file_id}
   */
  async updateFile(fileId: UUID, request: FileUpdateRequest): Promise<File> {
    try {
      const response = await apiClient.patch<File>(`/files/${fileId}`, request);
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 删除文件
   * DELETE /files/{file_id}
   */
  async deleteFile(fileId: UUID): Promise<string> {
    try {
      const response = await apiClient.delete<string>(`/files/${fileId}`);
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 跨项目移动文件
   * POST /files/{file_id}/move
   */
  async moveFile(fileId: UUID, request: FileMoveRequest): Promise<File> {
    try {
      const response = await apiClient.post<File>(`/files/${fileId}/move`, request);
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }
}

// 导出服务实例
export const fileService = new FileService();