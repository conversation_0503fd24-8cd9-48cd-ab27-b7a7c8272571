/**
 * 用户服务模块
 * 处理用户相关的 API 请求
 */

import { apiClient } from "../base/apiClient";
import { handleApiError } from "../base/errorHandler";
import { UserProfile, ProfileUpdateRequest } from "../types";

/**
 * 用户服务类
 */
export class UserService {
  /**
   * 获取用户资料
   * GET /users/profile
   */
  async getProfile(): Promise<UserProfile> {
    try {
      const response = await apiClient.get<UserProfile>("/users/profile");
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 更新用户资料
   * PATCH /users/profile
   */
  async updateProfile(data: ProfileUpdateRequest): Promise<UserProfile> {
    try {
      const response = await apiClient.patch<UserProfile>(
        "/users/profile",
        data
      );
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }
}

// 导出用户服务实例
export const userService = new UserService();
