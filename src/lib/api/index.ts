/**
 * API 模块主入口
 * 统一导出所有 API 相关功能
 */

// 基础配置和客户端
export { apiClient, ApiClient, API_CONFIG } from './base/apiClient';
export { handleApiError, ApiError, ApiErrorType } from './base/errorHandler';

// 类型定义
export * from './types';

// 服务层
export * from './services';

// 便捷的服务访问对象
import { userService, projectService, systemService, docService, fileService } from './services';

// 统一的 API 对象
export const api = {
  user: userService,
  project: projectService,
  system: systemService,
  doc: docService,
  file: fileService,
  // 预留其他服务
} as const;