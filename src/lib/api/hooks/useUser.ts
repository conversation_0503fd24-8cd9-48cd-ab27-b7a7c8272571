/**
 * 用户相关的 React Hooks
 * 提供用户数据获取和操作的便捷接口
 */

import { useState, useEffect } from "react";
import { userService } from "../services";
import { UserProfile, ProfileUpdateRequest } from "../types";
import { ApiError } from "../base/errorHandler";

// Hook 状态接口
interface UseUserState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

/**
 * 获取用户资料的 Hook
 */
export function useUserProfile() {
  const [state, setState] = useState<UseUserState<UserProfile>>({
    data: null,
    loading: true,
    error: null,
  });

  const refetch = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const data = await userService.getProfile();
      setState({ data, loading: false, error: null });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error as ApiError,
      });
    }
  };

  const updateProfile = async (updateData: ProfileUpdateRequest) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const data = await userService.updateProfile(updateData);
      setState({ data, loading: false, error: null });
      return data;
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error as ApiError,
      }));
      throw error;
    }
  };

  useEffect(() => {
    refetch();
  }, []);

  return {
    ...state,
    refetch,
    updateProfile,
  };
}
