/**
 * 用户相关类型定义
 * 基于 API 文档的用户端点定义
 */

import { UUID, BaseEntity } from "./common";

// 用户资料接口
export interface UserProfile {
  id: UUID;
  real_name: string;
  company_name: string;
  company_size: string;
  position: string;
  avatar: string;
}

// 用户资料更新请求接口
export interface ProfileUpdateRequest {
  real_name?: string;
  company_name?: string;
  company_size?: string;
  position?: string;
  avatar?: string;
}
