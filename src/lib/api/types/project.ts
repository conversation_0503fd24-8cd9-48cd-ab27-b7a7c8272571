/**
 * 项目相关类型定义
 * 基于 API 文档的项目端点定义
 */

import { UUID, BaseEntity, Timestamp, ProjectStatus, Region } from "./common";

// 项目接口
export interface Project extends BaseEntity {
  name: string;
  last_edited_at: Timestamp;
  description: string;
  status: ProjectStatus;
}

// 项目条目接口（用于列表显示）
export interface ProjectItem extends Project {
  is_favorite: boolean;
}

// 项目创建请求接口
export interface ProjectCreateRequest {
  name: string;
  description: string;
  status: ProjectStatus;
}

// 项目更新请求接口
export interface ProjectUpdateRequest {
  name?: string;
  description?: string;
  status?: ProjectStatus;
}

// 项目个性化设置更新请求接口
export interface ProjectPersonalizationUpdateRequest {
  is_favorite: boolean;
}

// 项目文件树接口（根据新API规范）
export interface ProjectTree {
  created_at: Timestamp;
  updated_at: Timestamp;
  id: UUID;
  region: Region;
  name: string;
}
