/**
 * 通用类型定义
 * 包含所有 API 共用的基础类型和枚举
 */

// UUID 类型
export type UUID = string;

// 时间戳类型（ISO 8601 格式）
export type Timestamp = string;

// 项目状态枚举（基于 API 文档）
export enum ProjectStatus {
  ARCHIVED = "ARCHIVED",
  ACTIVE = "ACTIVE",
  INBOX = "INBOX",
}

// 区域枚举（基于 API 文档）
export enum Region {
  PERSONAL = "PERSONAL",
  SHARED = "SHARED",
  REFERENCE = "REFERENCE",
}

// 基础实体接口
export interface BaseEntity {
  id: UUID;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// API 响应基础格式
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
}

// 分页参数接口（预留扩展）
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 分页响应接口（预留扩展）
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
}
