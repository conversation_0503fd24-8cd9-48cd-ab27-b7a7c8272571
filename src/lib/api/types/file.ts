/**
 * 文件相关类型定义
 * 基于 API 文档的文件端点定义
 */

import { UUID, BaseEntity, Region } from './common';

// 文件接口
export interface File extends BaseEntity {
  project_id: UUID;
  region: Region;
  file_name: string;
  owner_user_id: UUID;
  content: string;
  file_size: number;
  mime_type: string;
  entity_type: string;
}

// 文件创建请求接口
export interface FileCreateRequest {
  project_id: UUID;
  region: Region;
  file_name: string;
  content: string;
  metadata?: Record<string, any>;
}

// 文件更新请求接口
export interface FileUpdateRequest {
  id: UUID;
  region?: Region;
  file_name?: string;
  content?: string;
}

// 文件移动请求接口
export interface FileMoveRequest {
  id: UUID; // user id
  project_id: UUID; // 目标项目ID
}