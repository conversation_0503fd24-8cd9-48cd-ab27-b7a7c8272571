/**
 * Toast 通知工具函数
 * 提供统一的用户提示接口
 * 基于 HeroUI Toast 组件实现
 */

import { addToast } from "@heroui/toast";
import { ReactNode } from "react";

/**
 * Toast 配置选项接口
 */
export interface ToastOptions {
  /** 超时时间（毫秒），默认 6000ms */
  timeout?: number;
  /** 描述文本 */
  description?: string;
  /** 图标 */
  icon?: ReactNode;
  /** 变体样式 */
  variant?: "solid" | "bordered" | "flat";
  /** 圆角大小 */
  radius?: "none" | "sm" | "md" | "lg" | "full";
  /** 是否隐藏图标 */
  hideIcon?: boolean;
  /** 是否隐藏关闭按钮 */
  hideCloseButton?: boolean;
  /** 是否显示超时进度条 */
  shouldShowTimeoutProgress?: boolean;
  /** 结束内容 */
  endContent?: ReactNode;
  /** 自定义关闭图标 */
  closeIcon?: ReactNode;
  /** 自定义样式类名 */
  classNames?: Partial<Record<
    | "base"
    | "content"
    | "wrapper"
    | "title"
    | "description"
    | "icon"
    | "loadingComponent"
    | "progressTrack"
    | "progressIndicator"
    | "motionDiv"
    | "closeButton"
    | "closeIcon",
    string
  >>;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 显示通用 toast
 * @param message 提示消息
 * @param type toast 类型
 * @param options 额外选项
 */
export const showToast = (
  message: string,
  type:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger" = "default",
  options?: ToastOptions
) => {
  return addToast({
    title: message,
    color: type,
    timeout: options?.timeout ?? 6000,
    description: options?.description,
    icon: options?.icon,
    variant: options?.variant ?? "flat",
    radius: options?.radius ?? "md",
    hideIcon: options?.hideIcon ?? false,
    hideCloseButton: options?.hideCloseButton ?? false,
    shouldShowTimeoutProgress: options?.shouldShowTimeoutProgress ?? false,
    endContent: options?.endContent,
    closeIcon: options?.closeIcon,
    classNames: options?.classNames,
    onClose: options?.onClose,
  });
};

/**
 * 显示成功提示
 * @param message 提示消息
 * @param options 额外选项
 */
export function showSuccessToast(message: string, options?: ToastOptions) {
  return showToast(message, "success", {
    timeout: 3000,
    ...options,
  });
}

/**
 * 显示错误提示
 * @param message 错误消息
 * @param options 额外选项
 */
export function showErrorToast(message: string, options?: ToastOptions) {
  return showToast(message, "danger", {
    timeout: 5000,
    ...options,
  });
}

/**
 * 显示警告提示
 * @param message 警告消息
 * @param options 额外选项
 */
export function showWarningToast(message: string, options?: ToastOptions) {
  return showToast(message, "warning", {
    timeout: 4000,
    ...options,
  });
}

/**
 * 显示信息提示
 * @param message 信息消息
 * @param options 额外选项
 */
export function showInfoToast(message: string, options?: ToastOptions) {
  return showToast(message, "primary", {
    timeout: 3000,
    ...options,
  });
}

/**
 * 显示加载提示（支持 Promise）
 * @param message 加载消息
 * @param promise 可选的 Promise，用于自动更新状态
 * @param options 额外选项
 * @returns toast 实例
 */
export function showLoadingToast(
  message: string,
  promise?: Promise<any>,
  options?: ToastOptions
) {
  return showToast(message, "default", {
    timeout: promise ? 0 : 6000, // 如果有 Promise，不自动关闭
    ...options,
    ...(promise && {
      // 使用 HeroUI 的 Promise 支持
      promise,
    }),
  });
}

/**
 * 显示带描述的 toast
 * @param title 标题
 * @param description 描述
 * @param type toast 类型
 * @param options 额外选项
 */
export function showToastWithDescription(
  title: string,
  description: string,
  type:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger" = "default",
  options?: ToastOptions
) {
  return showToast(title, type, {
    description,
    ...options,
  });
}

/**
 * 显示带图标的 toast
 * @param message 提示消息
 * @param icon 图标
 * @param type toast 类型
 * @param options 额外选项
 */
export function showToastWithIcon(
  message: string,
  icon: ReactNode,
  type:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger" = "default",
  options?: ToastOptions
) {
  return showToast(message, type, {
    icon,
    ...options,
  });
}

/**
 * 显示进度 toast
 * @param message 提示消息
 * @param type toast 类型
 * @param options 额外选项
 */
export function showProgressToast(
  message: string,
  type:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger" = "default",
  options?: ToastOptions
) {
  return showToast(message, type, {
    shouldShowTimeoutProgress: true,
    ...options,
  });
}

/**
 * 关闭指定的 toast
 * @param toastId toast ID
 * @deprecated HeroUI toast 可能不支持单独关闭功能
 */
export function dismissToast(toastId: string | number) {
  console.warn('dismissToast: HeroUI toast may not support individual toast dismissal');
}

/**
 * 关闭所有 toast
 * @deprecated HeroUI toast 可能不支持关闭所有功能
 */
export function dismissAllToasts() {
  console.warn('dismissAllToasts: HeroUI toast may not support dismissing all toasts');
}