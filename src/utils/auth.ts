import logger from "./logger";

const authLogger = logger.createPrefixed("Auth");

/**
 * 从环境变量获取敏感域名列表
 * @returns 敏感域名数组
 */
export function getSensitiveHostnames(): string[] {
  const sensitiveHostnamesStr = process.env.NEXT_PUBLIC_SENSITIVE_HOSTNAMES;
  let sensitiveHostnames: string[] = [];

  if (sensitiveHostnamesStr) {
    // 将逗号分隔的字符串转换为数组，并去除空白
    sensitiveHostnames = sensitiveHostnamesStr
      .split(",")
      .map((host) => host.trim());
  } else {
    authLogger.warn("未配置敏感域名列表，使用空列表");
  }

  return sensitiveHostnames;
}

/**
 * 检查当前域名是否为敏感域名
 * @returns 是否为敏感域名
 */
export function isCurrentHostnameSensitive(): boolean {
  // 获取敏感域名列表
  const sensitiveHostnames = getSensitiveHostnames();

  // 如果没有配置敏感域名，则返回false
  if (sensitiveHostnames.length === 0) {
    return false;
  }

  // 获取当前域名
  const currentHostname =
    typeof window !== "undefined" ? window.location.hostname : "";
  const currentPath =
    typeof window !== "undefined" ? window.location.pathname : "";
  const currentHostWithPath = `${currentHostname}${currentPath}`;

  // 检查当前域名是否在敏感域名列表中
  return sensitiveHostnames.some((host) => {
    // 完全匹配
    if (currentHostname === host) return true;
    // 部分路径匹配 (如 example.com/projects)
    if (currentHostWithPath.includes(host)) return true;
    return false;
  });
}
