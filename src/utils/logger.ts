/**
 * 日志工具函数
 * 在开发环境下显示console log，在生产环境下不显示
 */

type LogLevel = "log" | "info" | "warn" | "error" | "debug";

/**
 * 是否模拟生产环境（用于测试）
 * 设置为true时，无论当前实际环境如何，都会按生产环境处理日志
 */
const MOCK_PRODUCTION = false; // 模拟生产环境，设置为false恢复开发环境

/**
 * 判断当前是否为开发环境
 */
const isDevelopment =
  !MOCK_PRODUCTION && process.env.NODE_ENV === "development";

// 输出当前环境信息（仅在实际开发环境下显示）
if (process.env.NODE_ENV === "development") {
  console.log(`当前环境: ${process.env.NODE_ENV}`);
  console.log(`是否显示日志: ${isDevelopment}`);
  console.log(`是否模拟生产环境: ${MOCK_PRODUCTION}`);
}

/**
 * 日志工具对象
 */
const logger = {
  log: (...args: any[]): void => {
    if (isDevelopment) {
      console.log(...args);
    }
  },

  info: (...args: any[]): void => {
    if (isDevelopment) {
      console.info(...args);
    }
  },

  warn: (...args: any[]): void => {
    if (isDevelopment) {
      console.warn(...args);
    }
  },

  error: (...args: any[]): void => {
    // 错误日志在生产环境下也需要记录
    console.error(...args);
  },

  debug: (...args: any[]): void => {
    if (isDevelopment) {
      console.debug(...args);
    }
  },

  /**
   * 创建带前缀的日志记录器
   * @param prefix 日志前缀
   * @returns 带前缀的日志记录器
   */
  createPrefixed: (prefix: string) => {
    return {
      log: (...args: any[]): void => {
        if (isDevelopment) {
          console.log(`[${prefix}]`, ...args);
        }
      },

      info: (...args: any[]): void => {
        if (isDevelopment) {
          console.info(`[${prefix}]`, ...args);
        }
      },

      warn: (...args: any[]): void => {
        if (isDevelopment) {
          console.warn(`[${prefix}]`, ...args);
        }
      },

      error: (...args: any[]): void => {
        // 错误日志在生产环境下也需要记录
        console.error(`[${prefix}]`, ...args);
      },

      debug: (...args: any[]): void => {
        if (isDevelopment) {
          console.debug(`[${prefix}]`, ...args);
        }
      },
    };
  },
};

export default logger;
