/**
 * logger工具使用示例
 */
import logger from "./logger";

// 基本用法
function basicUsage() {
  // 普通日志，仅在开发环境显示
  logger.log("这是一条普通日志");
  logger.info("这是一条信息日志");
  logger.warn("这是一条警告日志");

  // 错误日志，在所有环境都会显示
  logger.error("这是一条错误日志");

  // 调试日志，仅在开发环境显示
  logger.debug("这是一条调试日志");
}

// 使用带前缀的日志记录器
function prefixedLoggerUsage() {
  // 创建带前缀的日志记录器
  const componentLogger = logger.createPrefixed("UserComponent");

  componentLogger.log("组件已初始化");
  componentLogger.info("用户数据已加载");
  componentLogger.warn("权限不足");
  componentLogger.error("API请求失败");
  componentLogger.debug("渲染耗时: 120ms");
}

// 在组件中使用
function useInComponent() {
  // 为每个组件创建专属的日志记录器
  const editorLogger = logger.createPrefixed("Editor");

  // 组件生命周期中使用
  function onMount() {
    editorLogger.log("编辑器已挂载");
  }

  function onUpdate() {
    editorLogger.debug("编辑器已更新");
  }

  function onError(error: Error) {
    editorLogger.error("编辑器错误:", error);
  }
}

// 导出示例函数
export { basicUsage, prefixedLoggerUsage, useInComponent };
