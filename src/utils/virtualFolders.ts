/**
 * 虚拟文件夹管理工具
 * 用于管理本地存储的空文件夹，避免与真实文件夹冲突
 */

import { Region } from "../lib/api/types/common";

export interface VirtualFolder {
  id: string;
  path: string;
  name: string;
  projectId: string;
  region: Region;
  createdAt: string;
}

// 本地存储键名
const VIRTUAL_FOLDERS_KEY = "framesound-virtual-folders";

/**
 * 获取所有虚拟文件夹
 */
export const getVirtualFolders = (projectId?: string): VirtualFolder[] => {
  try {
    if (typeof window === "undefined") return [];

    const stored = localStorage.getItem(VIRTUAL_FOLDERS_KEY);
    if (!stored) return [];

    const allFolders: VirtualFolder[] = JSON.parse(stored);

    // 如果指定了项目ID，只返回该项目的文件夹
    if (projectId) {
      return allFolders.filter((folder) => folder.projectId === projectId);
    }

    return allFolders;
  } catch (error) {
    console.error("Error loading virtual folders:", error);
    return [];
  }
};

/**
 * 保存虚拟文件夹
 */
const saveVirtualFolders = (folders: VirtualFolder[]): void => {
  try {
    if (typeof window === "undefined") return;
    localStorage.setItem(VIRTUAL_FOLDERS_KEY, JSON.stringify(folders));
  } catch (error) {
    console.error("Error saving virtual folders:", error);
  }
};

/**
 * 创建新的虚拟文件夹
 */
export const createVirtualFolder = (
  projectId: string,
  folderName: string,
  region: Region,
  parentPath?: string
): VirtualFolder => {
  const allFolders = getVirtualFolders();

  // 构建完整路径
  const fullPath = parentPath
    ? `${parentPath}/${folderName}`.replace(/\/+/g, "/")
    : `/${folderName}`;

  // 检查是否已存在同名文件夹
  const existingFolder = allFolders.find(
    (folder) => folder.projectId === projectId && folder.path === fullPath
  );

  if (existingFolder) {
    throw new Error(`Folder "${folderName}" already exists`);
  }

  // 创建新文件夹
  const newFolder: VirtualFolder = {
    id: `virtual-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    path: fullPath,
    name: folderName,
    projectId,
    region,
    createdAt: new Date().toISOString(),
  };

  // 保存到本地存储
  allFolders.push(newFolder);
  saveVirtualFolders(allFolders);

  // 调试日志（已注释）
  // console.log("💾 Virtual folder saved to localStorage:", newFolder);
  // console.log("📁 All virtual folders:", allFolders);

  return newFolder;
};

/**
 * 删除虚拟文件夹
 */
export const deleteVirtualFolder = (
  projectId: string,
  folderPath: string
): void => {
  const allFolders = getVirtualFolders();
  const updatedFolders = allFolders.filter(
    (folder) => !(folder.projectId === projectId && folder.path === folderPath)
  );
  saveVirtualFolders(updatedFolders);
};

/**
 * 重命名虚拟文件夹
 */
export const renameVirtualFolder = (
  projectId: string,
  oldPath: string,
  newName: string
): VirtualFolder | null => {
  const allFolders = getVirtualFolders();
  const folderIndex = allFolders.findIndex(
    (folder) => folder.projectId === projectId && folder.path === oldPath
  );

  if (folderIndex === -1) return null;

  // 构建新路径
  const pathParts = oldPath.split("/").filter(Boolean);
  pathParts[pathParts.length - 1] = newName;
  const newPath = "/" + pathParts.join("/");

  // 检查新路径是否已存在
  const existingFolder = allFolders.find(
    (folder) => folder.projectId === projectId && folder.path === newPath
  );

  if (existingFolder) {
    throw new Error(`Folder "${newName}" already exists`);
  }

  // 更新文件夹信息
  allFolders[folderIndex] = {
    ...allFolders[folderIndex],
    path: newPath,
    name: newName,
  };

  saveVirtualFolders(allFolders);
  return allFolders[folderIndex];
};

/**
 * 检查路径是否为虚拟文件夹
 */
export const isVirtualFolder = (
  projectId: string,
  folderPath: string
): boolean => {
  const virtualFolders = getVirtualFolders(projectId);
  return virtualFolders.some((folder) => folder.path === folderPath);
};

/**
 * 当真实文件创建在虚拟文件夹路径下时，自动清理虚拟文件夹
 */
export const cleanupVirtualFoldersOnFileCreate = (
  projectId: string,
  filePath: string
): void => {
  const allFolders = getVirtualFolders();
  const fileDir = filePath.substring(0, filePath.lastIndexOf("/")) || "/";

  // 找到所有需要清理的虚拟文件夹（文件路径的父目录）
  const foldersToRemove = allFolders.filter(
    (folder) =>
      folder.projectId === projectId &&
      (filePath.startsWith(folder.path + "/") || folder.path === fileDir)
  );

  if (foldersToRemove.length > 0) {
    const updatedFolders = allFolders.filter(
      (folder) =>
        !foldersToRemove.some(
          (toRemove) =>
            toRemove.projectId === folder.projectId &&
            toRemove.path === folder.path
        )
    );
    saveVirtualFolders(updatedFolders);
  }
};

/**
 * 验证文件夹名称
 */
export const validateFolderName = (
  name: string
): { isValid: boolean; error?: string } => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: "Folder name cannot be empty" };
  }

  if (name.length > 255) {
    return { isValid: false, error: "Folder name is too long" };
  }

  // 检查非法字符
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(name)) {
    return { isValid: false, error: "Folder name contains invalid characters" };
  }

  // 检查保留名称
  const reservedNames = [
    "CON",
    "PRN",
    "AUX",
    "NUL",
    "COM1",
    "COM2",
    "COM3",
    "COM4",
    "COM5",
    "COM6",
    "COM7",
    "COM8",
    "COM9",
    "LPT1",
    "LPT2",
    "LPT3",
    "LPT4",
    "LPT5",
    "LPT6",
    "LPT7",
    "LPT8",
    "LPT9",
  ];
  if (reservedNames.includes(name.toUpperCase())) {
    return { isValid: false, error: "Folder name is reserved" };
  }

  return { isValid: true };
};

/**
 * 当真实文件删除时，将包含的文件夹转换为虚拟文件夹（保留文件夹结构）
 */
export const createVirtualFoldersOnFileDelete = (
  projectId: string,
  filePath: string,
  region: Region
): void => {
  const pathParts = filePath.split("/").filter(Boolean);

  // 如果文件在根目录，不需要创建虚拟文件夹
  if (pathParts.length <= 1) return;

  // 移除文件名，只保留文件夹路径
  const folderParts = pathParts.slice(0, -1);

  // 为每个文件夹层级创建虚拟文件夹（如果不存在的话）
  let currentPath = "";
  folderParts.forEach((part) => {
    currentPath = currentPath ? `${currentPath}/${part}` : `/${part}`;

    // 检查虚拟文件夹是否已存在
    const allFolders = getVirtualFolders();
    const exists = allFolders.some(
      (folder) =>
        folder.projectId === projectId &&
        folder.path === currentPath &&
        folder.region === region
    );

    if (!exists) {
      // 创建虚拟文件夹
      const newFolder: VirtualFolder = {
        id: `virtual-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 11)}`,
        path: currentPath,
        name: part,
        projectId,
        region,
        createdAt: new Date().toISOString(),
      };

      allFolders.push(newFolder);
      saveVirtualFolders(allFolders);
    }
  });
};

/**
 * 递归收集文件夹中的所有文件
 */
export const collectFilesInFolder = (
  folderPath: string,
  allFiles: any[]
): any[] => {
  const filesInFolder: any[] = [];

  allFiles.forEach((file) => {
    // 处理不同的数据结构
    let filePath: string;

    if (file.path) {
      // 如果是文件树中的数据（已经有 path 属性）
      filePath = file.path;
    } else if (file.name) {
      // 如果是后端原始数据（只有 name 属性）
      // name 可能包含路径，如 "folder/subfolder/file.md"
      filePath = file.name.startsWith("/") ? file.name : `/${file.name}`;
    } else {
      return; // 跳过无效数据
    }

    // 检查文件是否在指定文件夹中
    // 只匹配直接在文件夹中的文件，不包括子文件夹中的文件
    if (filePath.startsWith(folderPath + "/")) {
      // 确保不是更深层的文件
      const relativePath = filePath.substring(folderPath.length + 1);
      if (!relativePath.includes("/")) {
        filesInFolder.push(file);
      }
    }
  });

  return filesInFolder;
};

/**
 * 递归收集文件夹中的所有虚拟子文件夹
 */
export const collectVirtualFoldersInFolder = (
  projectId: string,
  folderPath: string,
  region: Region
): VirtualFolder[] => {
  const allVirtualFolders = getVirtualFolders(projectId);
  const foldersInFolder: VirtualFolder[] = [];

  allVirtualFolders.forEach((folder) => {
    if (
      folder.region === region &&
      (folder.path.startsWith(folderPath + "/") || folder.path === folderPath)
    ) {
      foldersInFolder.push(folder);
    }
  });

  return foldersInFolder;
};
