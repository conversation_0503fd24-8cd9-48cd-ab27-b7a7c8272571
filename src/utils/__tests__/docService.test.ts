import { docService } from "../../lib/api/services/docService";
import { apiClient } from "../../lib/api/base/apiClient";
import {
  Doc,
  DocCreateRequest,
  DocUpdateRequest,
  DocReplaceRequest,
} from "../../lib/api/types/doc";
import { Region } from "../../lib/api/types/common";

// Mock apiClient
jest.mock("../../lib/api/base/apiClient", () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Fixed project ID as specified
const FIXED_PROJECT_ID = "0000002e-1234-5678-9abc-def012345678";
const FIXED_DOC_ID = "3fa85f64-5717-4562-b3fc-2c963f66afa6";
const FIXED_USER_ID = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

describe("DocService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getDoc", () => {
    it("should get document by ID successfully", async () => {
      const mockDoc: Doc = {
        created_at: "2025-07-22T14:54:42.750Z",
        updated_at: "2025-07-22T14:54:42.750Z",
        id: FIXED_DOC_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        doc_name: "Test Document",
        content: "This is a test document content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "document",
      };

      mockApiClient.get.mockResolvedValue({
        data: mockDoc,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.getDoc(FIXED_DOC_ID);

      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/v0/docs/${FIXED_DOC_ID}`
      );
      expect(result).toEqual(mockDoc);
    });

    it("should handle validation error when getting document", async () => {
      const mockError = {
        detail: [
          {
            loc: ["path", "doc_id"],
            msg: "invalid document id format",
            type: "value_error.uuid",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.get.mockRejectedValue(httpError);

      await expect(docService.getDoc("invalid-id")).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("updateDoc", () => {
    it("should update document successfully", async () => {
      const updateRequest: DocUpdateRequest = {
        id: FIXED_DOC_ID,
        region: Region.PERSONAL,
        doc_name: "Updated Document",
        content: "Updated document content",
      };

      const mockUpdatedDoc: Doc = {
        created_at: "2025-07-22T14:54:42.750Z",
        updated_at: "2025-07-22T14:54:42.750Z",
        id: FIXED_DOC_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        doc_name: "Updated Document",
        content: "Updated document content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "document",
      };

      mockApiClient.patch.mockResolvedValue({
        data: mockUpdatedDoc,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.updateDoc(FIXED_DOC_ID, updateRequest);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        `/v0/docs/${FIXED_DOC_ID}`,
        updateRequest
      );
      expect(result).toEqual(mockUpdatedDoc);
    });

    it("should handle validation error when updating document", async () => {
      const updateRequest: DocUpdateRequest = {
        id: FIXED_DOC_ID,
        region: Region.PERSONAL,
        doc_name: "",
        content: "Content",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "doc_name"],
            msg: "document name cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.patch.mockRejectedValue(httpError);

      await expect(
        docService.updateDoc(FIXED_DOC_ID, updateRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("deleteDoc", () => {
    it("should delete document successfully", async () => {
      const mockResponse = "Document deleted successfully";

      mockApiClient.delete.mockResolvedValue({
        data: mockResponse,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.deleteDoc(FIXED_DOC_ID);

      expect(mockApiClient.delete).toHaveBeenCalledWith(
        `/v0/docs/${FIXED_DOC_ID}`
      );
      expect(result).toEqual(mockResponse);
    });

    it("should handle error when deleting document", async () => {
      mockApiClient.delete.mockRejectedValue(new Error("Not Found"));

      await expect(docService.deleteDoc(FIXED_DOC_ID)).rejects.toThrow(
        "Not Found"
      );
    });
  });

  describe("replaceDoc", () => {
    it("should replace document successfully", async () => {
      const replaceRequest: DocReplaceRequest = {
        id: FIXED_DOC_ID,
        region: Region.PERSONAL,
        doc_name: "Replaced Document",
        content: "Completely replaced content",
      };

      const mockReplacedDoc: Doc = {
        created_at: "2025-07-22T14:57:02.492Z",
        updated_at: "2025-07-22T14:57:02.492Z",
        id: FIXED_DOC_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        doc_name: "Replaced Document",
        content: "Completely replaced content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "document",
      };

      mockApiClient.put.mockResolvedValue({
        data: mockReplacedDoc,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.replaceDoc(FIXED_DOC_ID, replaceRequest);

      expect(mockApiClient.put).toHaveBeenCalledWith(
        `/v0/docs/${FIXED_DOC_ID}`,
        replaceRequest
      );
      expect(result).toEqual(mockReplacedDoc);
    });

    it("should handle validation error when replacing document", async () => {
      const replaceRequest: DocReplaceRequest = {
        id: FIXED_DOC_ID,
        region: Region.PERSONAL,
        doc_name: "",
        content: "Content",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "doc_name"],
            msg: "document name cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.put.mockRejectedValue(httpError);

      await expect(
        docService.replaceDoc(FIXED_DOC_ID, replaceRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("createDoc", () => {
    it("should create document successfully", async () => {
      const createRequest: DocCreateRequest = {
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        doc_name: "New Document",
        content: "New document content",
      };

      const mockCreatedDoc: Doc = {
        created_at: "2025-07-22T14:57:02.494Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_DOC_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        doc_name: "New Document",
        content: "New document content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "document",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedDoc,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.createDoc(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/docs/",
        createRequest
      );
      expect(result).toEqual(mockCreatedDoc);
    });

    it("should handle validation error when creating document", async () => {
      const createRequest: DocCreateRequest = {
        project_id: "",
        region: Region.PERSONAL,
        doc_name: "Document",
        content: "Content",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "project_id"],
            msg: "project id cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.post.mockRejectedValue(httpError);

      await expect(docService.createDoc(createRequest)).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });

    it("should create document with team region", async () => {
      const createRequest: DocCreateRequest = {
        project_id: FIXED_PROJECT_ID,
        region: Region.SHARED,
        doc_name: "Team Document",
        content: "Team document content",
      };

      const mockCreatedDoc: Doc = {
        created_at: "2025-07-22T14:57:02.494Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_DOC_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.SHARED,
        doc_name: "Team Document",
        content: "Team document content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "document",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedDoc,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.createDoc(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/docs/",
        createRequest
      );
      expect(result).toEqual(mockCreatedDoc);
    });

    it("should create document with reference region", async () => {
      const createRequest: DocCreateRequest = {
        project_id: FIXED_PROJECT_ID,
        region: Region.REFERENCE,
        doc_name: "Reference Document",
        content: "Reference document content",
      };

      const mockCreatedDoc: Doc = {
        created_at: "2025-07-22T14:57:02.494Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_DOC_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.REFERENCE,
        doc_name: "Reference Document",
        content: "Reference document content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "document",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedDoc,
        status: 200,
        statusText: "OK",
      });

      const result = await docService.createDoc(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/docs/",
        createRequest
      );
      expect(result).toEqual(mockCreatedDoc);
    });
  });
});
