import { systemService } from "../../lib/api/services/systemService";
import { apiClient } from "../../lib/api/base/apiClient";

// Mock apiClient
jest.mock("../../lib/api/base/apiClient", () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe("SystemService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("setUserId", () => {
    it("should set user ID successfully", async () => {
      const userIdRequest = { user_id: "test-user-123" };
      const mockResponse = {
        message: "用户ID test-user-123 已设置到Cookie",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockResponse,
        status: 200,
        statusText: "OK",
      });

      const result = await systemService.setUserId(userIdRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/set_user_id",
        userIdRequest
      );
      expect(result).toEqual(mockResponse);
    });

    it("should handle validation error when setting user ID", async () => {
      const userIdRequest = { user_id: "" };
      const mockError = {
        detail: [
          {
            loc: ["body", "user_id"],
            msg: "field required",
            type: "value_error.missing",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.post.mockRejectedValue(httpError);

      await expect(
        systemService.setUserId(userIdRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/set_user_id",
        userIdRequest
      );
    });
  });

  describe("healthCheck", () => {
    it("should return health status successfully", async () => {
      const mockResponse = {
        status: "ok",
      };

      mockApiClient.get.mockResolvedValue({
        data: mockResponse,
        status: 200,
        statusText: "OK",
      });

      const result = await systemService.healthCheck();

      expect(mockApiClient.get).toHaveBeenCalledWith("/health");
      expect(result).toEqual(mockResponse);
    });

    it("should handle health check failure", async () => {
      mockApiClient.get.mockRejectedValue(new Error("Service Unavailable"));

      await expect(systemService.healthCheck()).rejects.toThrow(
        "Service Unavailable"
      );
      expect(mockApiClient.get).toHaveBeenCalledWith("/health");
    });
  });
});
