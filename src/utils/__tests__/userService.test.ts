import { userService } from "../../lib/api/services/userService";
import { apiClient } from "../../lib/api/base/apiClient";
import { UserProfile, ProfileUpdateRequest } from "../../lib/api/types/user";

// Mock apiClient
jest.mock("../../lib/api/base/apiClient", () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe("UserService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getProfile", () => {
    it("should get user profile successfully", async () => {
      const mockProfile: UserProfile = {
        real_name: "<PERSON>",
        company_name: "Tech Corp",
        company_size: "100-500",
        position: "Software Engineer",
        avatar: "https://example.com/avatar.jpg",
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      };

      mockApiClient.get.mockResolvedValue({
        data: mockProfile,
        status: 200,
        statusText: "OK",
      });

      const result = await userService.getProfile();

      expect(mockApiClient.get).toHaveBeenCalledWith("/v0/users/profile");
      expect(result).toEqual(mockProfile);
    });

    it("should handle error when getting profile", async () => {
      mockApiClient.get.mockRejectedValue(new Error("Not Found"));

      await expect(userService.getProfile()).rejects.toThrow("Not Found");
      expect(mockApiClient.get).toHaveBeenCalledWith("/v0/users/profile");
    });
  });

  describe("updateProfile", () => {
    it("should update user profile successfully", async () => {
      const updateRequest: ProfileUpdateRequest = {
        real_name: "Jane Doe",
        company_name: "New Tech Corp",
        company_size: "500-1000",
        position: "Senior Software Engineer",
        avatar: "https://example.com/new-avatar.jpg",
      };

      const mockUpdatedProfile: UserProfile = {
        real_name: updateRequest.real_name!,
        company_name: updateRequest.company_name!,
        company_size: updateRequest.company_size!,
        position: updateRequest.position!,
        avatar: updateRequest.avatar!,
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      };

      mockApiClient.patch.mockResolvedValue({
        data: mockUpdatedProfile,
        status: 200,
        statusText: "OK",
      });

      const result = await userService.updateProfile(updateRequest);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        "/v0/users/profile",
        updateRequest
      );
      expect(result).toEqual(mockUpdatedProfile);
    });

    it("should handle validation error when updating profile", async () => {
      const updateRequest: ProfileUpdateRequest = {
        real_name: "",
        company_name: "Tech Corp",
        company_size: "100-500",
        position: "Engineer",
        avatar: "invalid-url",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "real_name"],
            msg: "field required",
            type: "value_error.missing",
          },
          {
            loc: ["body", "avatar"],
            msg: "invalid url format",
            type: "value_error.url",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.patch.mockRejectedValue(httpError);

      await expect(
        userService.updateProfile(updateRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
      expect(mockApiClient.patch).toHaveBeenCalledWith(
        "/v0/users/profile",
        updateRequest
      );
    });
  });
});
