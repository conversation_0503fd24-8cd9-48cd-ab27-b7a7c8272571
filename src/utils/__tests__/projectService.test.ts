import { projectService } from "../../lib/api/services/projectService";
import { apiClient } from "../../lib/api/base/apiClient";
import {
  Project,
  ProjectCreateRequest,
  ProjectUpdateRequest,
  ProjectPersonalizationUpdateRequest,
  ProjectTree,
} from "../../lib/api/types/project";
import { ProjectStatus, Region } from "../../lib/api/types/common";

// Mock apiClient
jest.mock("../../lib/api/base/apiClient", () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Fixed project ID as specified
const FIXED_PROJECT_ID = "0000002e-1234-5678-9abc-def012345678";

describe("ProjectService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getProject", () => {
    it("should get project by ID successfully", async () => {
      const mockProject: Project = {
        created_at: "2025-07-22T14:33:54.377Z",
        updated_at: "2025-07-22T14:33:54.377Z",
        id: FIXED_PROJECT_ID,
        name: "Test Project",
        last_edited_at: "2025-07-22T14:33:54.377Z",
        description: "A test project description",
        status: ProjectStatus.ARCHIVED,
      };

      mockApiClient.get.mockResolvedValue({
        data: mockProject,
        status: 200,
        statusText: "OK",
      });

      const result = await projectService.getProject(FIXED_PROJECT_ID);

      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/v0/projects/${FIXED_PROJECT_ID}`
      );
      expect(result).toEqual(mockProject);
    });

    it("should handle validation error when getting project", async () => {
      const mockError = {
        detail: [
          {
            loc: ["path", "project_id"],
            msg: "invalid project id format",
            type: "value_error.uuid",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.get.mockRejectedValue(httpError);

      await expect(
        projectService.getProject("invalid-id")
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("updateProject", () => {
    it("should update project successfully", async () => {
      const updateRequest: ProjectUpdateRequest = {
        id: FIXED_PROJECT_ID,
        name: "Updated Project Name",
        description: "Updated description",
        status: ProjectStatus.ARCHIVED,
      };

      const mockUpdatedProject: Project = {
        created_at: "2025-07-22T14:33:54.377Z",
        updated_at: "2025-07-22T14:33:54.377Z",
        id: FIXED_PROJECT_ID,
        name: "Updated Project Name",
        last_edited_at: "2025-07-22T14:33:54.377Z",
        description: "Updated description",
        status: ProjectStatus.ARCHIVED,
      };

      mockApiClient.patch.mockResolvedValue({
        data: mockUpdatedProject,
        status: 200,
        statusText: "OK",
      });

      const result = await projectService.updateProject(
        FIXED_PROJECT_ID,
        updateRequest
      );

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        `/v0/projects/${FIXED_PROJECT_ID}`,
        updateRequest
      );
      expect(result).toEqual(mockUpdatedProject);
    });

    it("should handle validation error when updating project", async () => {
      const updateRequest: ProjectUpdateRequest = {
        id: FIXED_PROJECT_ID,
        name: "",
        description: "Description",
        status: ProjectStatus.ARCHIVED,
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "name"],
            msg: "project name cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.patch.mockRejectedValue(httpError);

      await expect(
        projectService.updateProject(FIXED_PROJECT_ID, updateRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("deleteProject", () => {
    it("should delete project successfully", async () => {
      const mockResponse = "Project deleted successfully";

      mockApiClient.delete.mockResolvedValue({
        data: mockResponse,
        status: 200,
        statusText: "OK",
      });

      const result = await projectService.deleteProject(FIXED_PROJECT_ID);

      expect(mockApiClient.delete).toHaveBeenCalledWith(
        `/v0/projects/${FIXED_PROJECT_ID}`
      );
      expect(result).toEqual(mockResponse);
    });

    it("should handle error when deleting project", async () => {
      mockApiClient.delete.mockRejectedValue(new Error("Not Found"));

      await expect(
        projectService.deleteProject(FIXED_PROJECT_ID)
      ).rejects.toThrow("Not Found");
    });
  });

  describe("createProject", () => {
    it("should create project successfully", async () => {
      const createRequest: ProjectCreateRequest = {
        name: "New Project",
        description: "A new project description",
        status: ProjectStatus.ARCHIVED,
      };

      const mockCreatedProject: Project = {
        created_at: "2025-07-22T14:33:54.381Z",
        updated_at: "2025-07-22T14:33:54.381Z",
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        name: "New Project",
        last_edited_at: "2025-07-22T14:33:54.381Z",
        description: "A new project description",
        status: ProjectStatus.ARCHIVED,
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedProject,
        status: 200,
        statusText: "OK",
      });

      const result = await projectService.createProject(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/projects/",
        createRequest
      );
      expect(result).toEqual(mockCreatedProject);
    });

    it("should handle validation error when creating project", async () => {
      const createRequest: ProjectCreateRequest = {
        name: "",
        description: "Description",
        status: ProjectStatus.ARCHIVED,
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "name"],
            msg: "project name cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.post.mockRejectedValue(httpError);

      await expect(
        projectService.createProject(createRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("getProjectTree", () => {
    it("should get project tree successfully", async () => {
      const mockProjectTree: ProjectTree = {
        id: FIXED_PROJECT_ID,
        items: [
          {
            created_at: "2025-07-22T14:33:54.383Z",
            updated_at: "2025-07-22T14:33:54.383Z",
            project_id: FIXED_PROJECT_ID,
            region: Region.PERSONAL,
            name: "Document 1",
            entity_type: "document",
          },
          {
            created_at: "2025-07-22T14:33:54.383Z",
            updated_at: "2025-07-22T14:33:54.383Z",
            project_id: FIXED_PROJECT_ID,
            region: Region.PERSONAL,
            name: "File 1",
            entity_type: "file",
          },
        ],
      };

      mockApiClient.get.mockResolvedValue({
        data: mockProjectTree,
        status: 200,
        statusText: "OK",
      });

      const result = await projectService.getProjectTree(FIXED_PROJECT_ID);

      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/v0/projects/${FIXED_PROJECT_ID}/tree`
      );
      expect(result).toEqual(mockProjectTree);
    });

    it("should handle error when getting project tree", async () => {
      mockApiClient.get.mockRejectedValue(new Error("Not Found"));

      await expect(
        projectService.getProjectTree(FIXED_PROJECT_ID)
      ).rejects.toThrow("Not Found");
    });
  });

  describe("updatePersonalization", () => {
    it("should update project personalization successfully", async () => {
      const updateRequest: ProjectPersonalizationUpdateRequest = {
        is_favorite: true,
      };

      const mockResponse = "Project personalization updated successfully";

      mockApiClient.patch.mockResolvedValue({
        data: mockResponse,
        status: 200,
        statusText: "OK",
      });

      const result = await projectService.updatePersonalization(
        FIXED_PROJECT_ID,
        updateRequest
      );

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        `/v0/projects/${FIXED_PROJECT_ID}/personalize`,
        updateRequest
      );
      expect(result).toEqual(mockResponse);
    });

    it("should handle validation error when updating personalization", async () => {
      // Note: Since is_favorite is boolean, we can't test with empty string
      // Instead, we'll test with a scenario where the API might reject the request
      const updateRequest: ProjectPersonalizationUpdateRequest = {
        is_favorite: true,
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "is_favorite"],
            msg: "invalid favorite status",
            type: "value_error.bool",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.patch.mockRejectedValue(httpError);

      await expect(
        projectService.updatePersonalization(FIXED_PROJECT_ID, updateRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });
});
