import { fileService } from "../../lib/api/services/fileService";
import { apiClient } from "../../lib/api/base/apiClient";
import {
  File,
  FileCreateRequest,
  FileUpdateRequest,
  FileMoveRequest,
} from "../../lib/api/types/file";
import { Region } from "../../lib/api/types/common";

// Mock apiClient
jest.mock("../../lib/api/base/apiClient", () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Fixed project ID as specified
const FIXED_PROJECT_ID = "0000002e-1234-5678-9abc-def012345678";
const FIXED_FILE_ID = "3fa85f64-5717-4562-b3fc-2c963f66afa6";
const FIXED_USER_ID = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

describe("FileService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getFile", () => {
    it("should get file by ID successfully", async () => {
      const mockFile: File = {
        created_at: "2025-07-22T14:54:42.750Z",
        updated_at: "2025-07-22T14:54:42.750Z",
        id: FIXED_FILE_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        file_name: "test.txt",
        content: "This is a test file content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "file",
        file_size: 27,
        mime_type: "text/plain",
      };

      mockApiClient.get.mockResolvedValue({
        data: mockFile,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.getFile(FIXED_FILE_ID);

      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/v0/files/${FIXED_FILE_ID}`
      );
      expect(result).toEqual(mockFile);
    });

    it("should handle validation error when getting file", async () => {
      const mockError = {
        detail: [
          {
            loc: ["path", "file_id"],
            msg: "invalid file id format",
            type: "value_error.uuid",
          },
        ],
      };

      // Mock 一个真实的 HTTP 422 响应错误
      const httpError = new Error("HTTP 422: Validation Error");
      (httpError as any).responseData = mockError;
      (httpError as any).status = 422;

      mockApiClient.get.mockRejectedValue(httpError);

      await expect(fileService.getFile("invalid-id")).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("updateFile", () => {
    it("should update file successfully", async () => {
      const updateRequest: FileUpdateRequest = {
        id: FIXED_FILE_ID,
        region: Region.PERSONAL,
        file_name: "updated_test.txt",
        content: "Updated file content",
      };

      const mockUpdatedFile: File = {
        created_at: "2025-07-22T14:54:42.750Z",
        updated_at: "2025-07-22T14:54:42.750Z",
        id: FIXED_FILE_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        file_name: "updated_test.txt",
        content: "Updated file content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "file",
        file_size: 20,
        mime_type: "text/plain",
      };

      mockApiClient.patch.mockResolvedValue({
        data: mockUpdatedFile,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.updateFile(FIXED_FILE_ID, updateRequest);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        `/v0/files/${FIXED_FILE_ID}`,
        updateRequest
      );
      expect(result).toEqual(mockUpdatedFile);
    });

    it("should handle validation error when updating file", async () => {
      const updateRequest: FileUpdateRequest = {
        id: FIXED_FILE_ID,
        region: Region.PERSONAL,
        file_name: "",
        content: "Content",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "file_name"],
            msg: "file name cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      const apiError = new Error("HTTP 422: Validation Error");
      (apiError as any).responseData = mockError;
      (apiError as any).status = 422;

      mockApiClient.patch.mockRejectedValue(apiError);

      await expect(
        fileService.updateFile(FIXED_FILE_ID, updateRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });

  describe("deleteFile", () => {
    it("should delete file successfully", async () => {
      const mockResponse = "File deleted successfully";

      mockApiClient.delete.mockResolvedValue({
        data: mockResponse,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.deleteFile(FIXED_FILE_ID);

      expect(mockApiClient.delete).toHaveBeenCalledWith(
        `/v0/files/${FIXED_FILE_ID}`
      );
      expect(result).toEqual(mockResponse);
    });

    it("should handle error when deleting file", async () => {
      mockApiClient.delete.mockRejectedValue(new Error("Not Found"));

      await expect(fileService.deleteFile(FIXED_FILE_ID)).rejects.toThrow(
        "Not Found"
      );
    });
  });

  describe("createFile", () => {
    it("should create file successfully", async () => {
      const createRequest: FileCreateRequest = {
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        file_name: "new_file.txt",
        content: "New file content",
      };

      const mockCreatedFile: File = {
        created_at: "2025-07-22T14:57:02.494Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_FILE_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.PERSONAL,
        file_name: "new_file.txt",
        content: "New file content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "file",
        file_size: 16,
        mime_type: "text/plain",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedFile,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.createFile(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/files/",
        createRequest
      );
      expect(result).toEqual(mockCreatedFile);
    });

    it("should handle validation error when creating file", async () => {
      const createRequest: FileCreateRequest = {
        project_id: "",
        region: Region.PERSONAL,
        file_name: "file.txt",
        content: "Content",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "project_id"],
            msg: "project id cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      const apiError = new Error("HTTP 422: Validation Error");
      (apiError as any).responseData = mockError;
      (apiError as any).status = 422;

      mockApiClient.post.mockRejectedValue(apiError);

      await expect(fileService.createFile(createRequest)).rejects.toMatchObject(
        {
          type: "VALIDATION_ERROR",
          status: 422,
          details: mockError.detail,
        }
      );
    });

    it("should create file with team region", async () => {
      const createRequest: FileCreateRequest = {
        project_id: FIXED_PROJECT_ID,
        region: Region.SHARED,
        file_name: "team_file.txt",
        content: "Team file content",
      };

      const mockCreatedFile: File = {
        created_at: "2025-07-22T14:57:02.494Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_FILE_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.SHARED,
        file_name: "team_file.txt",
        content: "Team file content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "file",
        file_size: 17,
        mime_type: "text/plain",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedFile,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.createFile(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/files/",
        createRequest
      );
      expect(result).toEqual(mockCreatedFile);
    });

    it("should create file with reference region", async () => {
      const createRequest: FileCreateRequest = {
        project_id: FIXED_PROJECT_ID,
        region: Region.REFERENCE,
        file_name: "reference_file.txt",
        content: "Reference file content",
      };

      const mockCreatedFile: File = {
        created_at: "2025-07-22T14:57:02.494Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_FILE_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.REFERENCE,
        file_name: "reference_file.txt",
        content: "Reference file content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "file",
        file_size: 22,
        mime_type: "text/plain",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockCreatedFile,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.createFile(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/v0/files/",
        createRequest
      );
      expect(result).toEqual(mockCreatedFile);
    });
  });

  describe("moveFile", () => {
    it("should move file successfully", async () => {
      const moveRequest: FileMoveRequest = {
        id: FIXED_USER_ID,
        project_id: FIXED_PROJECT_ID,
      };

      const mockMovedFile: File = {
        created_at: "2025-07-22T14:54:42.750Z",
        updated_at: "2025-07-22T14:57:02.494Z",
        id: FIXED_FILE_ID,
        project_id: FIXED_PROJECT_ID,
        region: Region.REFERENCE,
        file_name: "moved_file.txt",
        content: "File content",
        owner_user_id: FIXED_USER_ID,
        entity_type: "file",
        file_size: 12,
        mime_type: "text/plain",
      };

      mockApiClient.post.mockResolvedValue({
        data: mockMovedFile,
        status: 200,
        statusText: "OK",
      });

      const result = await fileService.moveFile(FIXED_FILE_ID, moveRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        `/v0/files/${FIXED_FILE_ID}/move`,
        moveRequest
      );
      expect(result).toEqual(mockMovedFile);
    });

    it("should handle error when target project does not exist", async () => {
      const moveRequest: FileMoveRequest = {
        id: FIXED_USER_ID,
        project_id: "non-existent-project",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "project_id"],
            msg: "target project does not exist",
            type: "value_error.project.not_found",
          },
        ],
      };

      const apiError = new Error("HTTP 422: Validation Error");
      (apiError as any).responseData = mockError;
      (apiError as any).status = 422;

      mockApiClient.post.mockRejectedValue(apiError);

      await expect(
        fileService.moveFile(FIXED_FILE_ID, moveRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });

    it("should handle validation error when moving file", async () => {
      const moveRequest: FileMoveRequest = {
        id: FIXED_USER_ID,
        project_id: "",
      };

      const mockError = {
        detail: [
          {
            loc: ["body", "project_id"],
            msg: "project id cannot be empty",
            type: "value_error.str.empty",
          },
        ],
      };

      const apiError = new Error("HTTP 422: Validation Error");
      (apiError as any).responseData = mockError;
      (apiError as any).status = 422;

      mockApiClient.post.mockRejectedValue(apiError);

      await expect(
        fileService.moveFile(FIXED_FILE_ID, moveRequest)
      ).rejects.toMatchObject({
        type: "VALIDATION_ERROR",
        status: 422,
        details: mockError.detail,
      });
    });
  });
});
